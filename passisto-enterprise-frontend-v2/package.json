{"name": "passisto-enterprise-frontend-v2", "version": "0.1.0", "private": true, "proxy": "https://pe.passisto.com", "scripts": {"dev": "npx next dev --turbopack", "build": "npx next build", "start": "npx next start", "lint": "npx next lint"}, "dependencies": {"@clerk/localizations": "^3.16.4", "@clerk/nextjs": "^6.12.0", "@hookform/resolvers": "^5.0.1", "@next/env": "^15.3.4", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@reduxjs/toolkit": "^2.8.1", "@tanstack/react-table": "^8.21.2", "@vapi-ai/web": "^2.2.5", "@xyflow/react": "^12.6.0", "axios": "^1.12.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.4.7", "lucide-react": "^0.475.0", "next": "^15.2.4", "next-intl": "^4.1.0", "next-themes": "^0.4.4", "openai": "^5.22.1", "passisto-enterprise-frontend-v2": "file:", "react": "^19.0.0", "react-dnd": "latest", "react-dnd-html5-backend": "latest", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-hook-toast": "^0.0.3", "react-redux": "^9.2.0", "react-router-dom": "^6.30.1", "react-toastify": "^11.0.5", "react-webcam": "^7.2.0", "recharts": "^2.15.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "latest", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^22.14.1", "@types/react": "19.1.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.8.3"}}