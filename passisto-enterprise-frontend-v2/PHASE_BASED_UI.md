# Phase-Based Candidate Management UI

## Overview

The Phase-Based Candidate Management system provides a structured workflow for managing candidates through multiple hiring phases. This new UI replaces the traditional single-view candidate management with a phase-by-phase approach.

## Key Features

### 1. **Phase Navigation**
- Tab-based interface showing all hiring phases
- Visual indicators for phase status (PENDING, ACTIVE, COMPLETED, CANC<PERSON>LED)
- Lock icon for closed/read-only phases
- Automatic phase progression tracking

### 2. **Phase Types**

#### **Screening Phase (CV Screening)**
- AI-powered CV scoring
- Automated feedback generation
- Bulk scoring capabilities
- Score-based filtering and sorting

#### **Interview Phase (AI Interview)**
- Send interview links to candidates
- AI-generated interview feedback
- Interview template selection
- Interview performance tracking

### 3. **Candidate Management Per Phase**

Each phase shows only candidates who belong to that phase:
- **View Details**: Full candidate profile with phase-specific scores and feedback
- **Track Actions**: Complete history of actions taken (emails sent, scores assigned, etc.)
- **Filter Candidates**: Multi-criteria filtering (score, status, actions, keywords)
- **Batch Operations**: Bulk actions on multiple candidates

### 4. **Phase Progression Rules**

```
1. Candidates start in the first phase (Screening)
2. User scores/evaluates candidates in current phase
3. User selects candidates to QUALIFY for next phase
4. Non-qualified candidates are marked as REJECTED
5. Qualified candidates move to next phase
6. Previous phase can be CLOSED (becomes read-only)
7. Repeat until final hiring decision
```

### 5. **Action Tracking**

Every action is logged with timestamps:
- `EMAIL_SENT` - Email communication
- `SCORE_ASSIGNED` - AI or manual scoring
- `INTERVIEW_SENT` - Interview link sent
- `FEEDBACK_GENERATED` - AI feedback created

## Components

### Main Components

#### 1. `PhaseCandidatesSection`
Main container component with phase tabs and candidate table.

**Props:**
- `projectId: string` - Project identifier

**Features:**
- Phase tab navigation
- Candidate filtering and search
- Bulk selection and actions
- Phase-specific action panels
- Summary statistics

#### 2. `PhaseCandidateDetailModal`
Detailed view of a candidate within a specific phase.

**Props:**
- `open: boolean` - Modal visibility
- `onOpenChange: (open: boolean) => void` - Close handler
- `candidate: Candidate` - Candidate data
- `phase: Phase` - Current phase
- `onQualify?: () => void` - Qualify action
- `onReject?: () => void` - Reject action
- `onScoreCandidate?: () => void` - Score action
- `onSendInterview?: () => void` - Send interview action
- `onSendEmail?: () => void` - Send email action

**Features:**
- Phase context display
- Score visualization
- AI feedback display
- Action history timeline
- Quick actions (qualify/reject/score/email)

#### 3. `BulkActionModal`
Modal for performing bulk operations on multiple candidates.

**Props:**
- `open: boolean` - Modal visibility
- `onOpenChange: (open: boolean) => void` - Close handler
- `actionType: "EMAIL" | "INTERVIEW" | "SCORE"` - Type of bulk action
- `candidates: Candidate[]` - Selected candidates
- `onConfirm: (data: any) => Promise<void>` - Confirm handler

**Features:**
- Email composition (subject + body with template variables)
- Interview template selection
- Bulk scoring confirmation
- Selected candidates preview

## Data Types

### Phase
```typescript
type Phase = {
  id: string
  name: string
  type: "SCREENING" | "INTERVIEW"
  description: string
  order: number
  status: "PENDING" | "ACTIVE" | "COMPLETED" | "CANCELLED"
  isClosed: boolean
  passingScore: number
  candidateCount: number
  qualifiedCount: number
  rejectedCount: number
}
```

### Candidate
```typescript
type Candidate = {
  id: string
  name: string
  email: string
  location: string
  experience: number
  avatar?: string
  phaseStatus: "PENDING" | "QUALIFIED" | "REJECTED" | "IN_PROGRESS"
  score?: number
  feedback?: string
  actions: CandidateAction[]
  skills: string[]
}
```

### CandidateAction
```typescript
type CandidateAction = {
  id: string
  type: "EMAIL_SENT" | "SCORE_ASSIGNED" | "INTERVIEW_SENT" | "FEEDBACK_GENERATED"
  timestamp: string
  details?: string
}
```

## Usage

### Integrating into Project Page

```tsx
import { PhaseCandidatesSection } from './_components/phase-candidates-section'

// In your component
<PhaseCandidatesSection projectId={projectId} />
```

### Toggle Between Classic and Phase-Based UI

The main project page includes a toggle button:

```tsx
const [usePhaseBasedUI, setUsePhaseBasedUI] = useState(false)

// Toggle button
<Button onClick={() => setUsePhaseBasedUI(!usePhaseBasedUI)}>
  {usePhaseBasedUI ? "Phase-Based View" : "Classic View"}
</Button>

// Conditional rendering
{usePhaseBasedUI ? (
  <PhaseCandidatesSection projectId={projectId} />
) : (
  <CandidatesSection projectId={projectId} />
)}
```

## Workflow Example

### 1. **Screening Phase**
```
1. Upload CVs
2. AI scores all candidates automatically
3. Review scores and AI feedback
4. Select high-scoring candidates
5. Click "Qualify" to move them to Interview phase
6. Remaining candidates are marked as "Rejected"
7. Close Screening phase (optional, makes it read-only)
```

### 2. **Interview Phase**
```
1. Qualified candidates from Screening appear here
2. Select interview template
3. Send interview links to candidates
4. Candidates complete AI interview
5. Review AI-generated interview feedback
6. Qualify successful candidates for next phase
7. Mark others as rejected
```

### 3. **Final Phase**
```
1. Review all qualified candidates
2. Make final hiring decisions
3. Mark candidates as "Hired" or "Rejected"
4. Close project
```

## Features by Phase Type

### Screening Phase Actions
- ✅ Score CVs (individual or bulk)
- ✅ View AI feedback
- ✅ Send emails to candidates
- ✅ Filter by score range
- ✅ Qualify/Reject candidates
- ✅ Advance to next phase

### Interview Phase Actions
- ✅ Send interview links (individual or bulk)
- ✅ View interview feedback
- ✅ Send emails to candidates
- ✅ Filter by interview performance
- ✅ Qualify/Reject candidates
- ✅ Advance to next phase

### Common Actions (All Phases)
- ✅ View candidate details
- ✅ Track action history
- ✅ Send emails
- ✅ Export candidate data
- ✅ Search and filter
- ✅ Bulk operations

## Styling

The UI uses a consistent design system with:
- **Primary Color**: Blue (#2563EB)
- **Success Color**: Green (#059669)
- **Danger Color**: Red (#DC2626)
- **Warning Color**: Yellow (#D97706)
- **Neutral Grays**: 50-900 scale

### Component Variants

**Badge Variants:**
- `default` - Blue (active, in-progress)
- `success` - Green (qualified, passed)
- `destructive` - Red (rejected, failed)
- `warning` - Yellow (pending review)
- `secondary` - Gray (neutral, inactive)

**Button Variants:**
- `default` - Primary blue
- `outline` - White with border
- `ghost` - Transparent
- `destructive` - Red
- `success` - Green

## Best Practices

### 1. **Phase Management**
- Always close a phase before moving to the next
- Don't skip phases unless explicitly allowed
- Keep passing scores consistent across phases

### 2. **Candidate Progression**
- Review all candidates before qualifying
- Provide rejection reasons when possible
- Track all actions for audit trail

### 3. **Bulk Operations**
- Verify selection before bulk actions
- Use templates for consistency
- Preview emails before sending

### 4. **Data Integrity**
- Don't modify closed phases
- Maintain action history
- Export data regularly

## API Integration Points

The UI is designed to integrate with these backend endpoints:

```typescript
// Get phases for project
GET /api/phases/project/:projectId

// Get candidates in phase
GET /api/phases/:phaseId/candidates

// Score candidate
POST /api/phases/:phaseId/cv-screening/:candidateId

// Bulk score
POST /api/phases/:phaseId/cv-screening/bulk-score

// Advance candidates
POST /api/phases/:phaseId/candidates/advance

// Close phase
PATCH /api/phases/:phaseId (status: COMPLETED, isClosed: true)

// Send interview
POST /api/interviews/send

// Bulk email
POST /api/candidates/bulk-email
```

## Future Enhancements

- [ ] Real-time updates with WebSockets
- [ ] Advanced analytics dashboard
- [ ] Custom phase types
- [ ] Interview scheduling integration
- [ ] Video interview playback
- [ ] Collaborative hiring (multi-user)
- [ ] AI recommendation engine
- [ ] Candidate self-service portal
- [ ] Mobile app support
- [ ] Integration with ATS systems

## Troubleshooting

### Issue: Candidates not appearing in phase
**Solution**: Check if candidates have been qualified from previous phase

### Issue: Can't modify phase
**Solution**: Check if phase is closed (isClosed: true)

### Issue: Bulk actions not working
**Solution**: Ensure candidates are selected and phase is not closed

### Issue: Scores not saving
**Solution**: Verify phase type is SCREENING and candidate is in correct phase

## Support

For questions or issues:
1. Check this documentation
2. Review component source code
3. Contact development team
4. File an issue in the repository
