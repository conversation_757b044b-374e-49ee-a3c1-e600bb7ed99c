# NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_b3V0Z29pbmctd29sZi0yNC5jbGVyay5hY2NvdW50cy5kZXYk
# CLERK_SECRET_KEY=sk_test_8xlcRqEwhhh1I6tCh54wKv6dzql95zymDtmKhOfQGM

# NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_bWFqb3Ita2lsbGRlZXItOTMuY2xlcmsuYWNjb3VudHMuZGV2JA
# CLERK_SECRET_KEY=sk_test_fEeFEpnugrbYK4zz4ETrczslzc64T7jdtQkUCqKpGw

NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Y29tbXVuYWwtZmFsY29uLTU1LmNsZXJrLmFjY291bnRzLmRldiQ
CLERK_SECRET_KEY=sk_test_6tVsqZAkDzHcl1az5A3lL66VUY4zVslvBaAcPnBMB1

NEXT_PUBLIC_CLERK_SIGN_IN_URL=http://localhost/auth/login
NEXT_PUBLIC_CLERK_SIGN_UP_URL=http://localhost/auth/register
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=http://localhost/
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=http://localhost/auth/onboarding/

# NEXT_PUBLIC_API_URL=https://pe.passisto.com/
# NEXT_PUBLIC_API_URL=http://********:1025
NEXT_PUBLIC_API_URL=http://localhost
NEXT_PUBLIC_APP_URL=http://localhost

# Chatbot API configuration
NEXT_PUBLIC_CHATBOT_API_URL=http://localhost/chatbot
# NEXT_PUBLIC_CHATBOT_ALIAS=844597d4-facb-4d76-814e-c8a4edfa1cdd

# NEXT_PUBLIC_VAPI_WEB_TOKEN = "285774be-bfe8-41c2-923b-18d598130607"
# NEXT_PUBLIC_VAPI_WORFLOW_ID = "f17c7253-5289-4afc-8fe9-b7c4e0004b6d"

NEXT_PUBLIC_VAPI_WEB_TOKEN_T1 = "c5596d0f-68a9-44d1-b572-2fcb7349470d"
NEXT_PUBLIC_VAPI_WORKFLOW_ID_W1 = "110f54ee-4072-455c-8938-8cc6301e4f07"

NEXT_PUBLIC_VAPI_WEB_TOKEN_T2 = "be054f87-b391-4f54-b642-884273e8f488"
NEXT_PUBLIC_VAPI_WORKFLOW_ID_W2 = "44bd5301-1795-463e-8de9-349ab296318e"

NEXT_PUBLIC_VAPI_WEB_TOKEN_T3 = "79fc56e9-4b0d-4aca-9b9e-4a82f63a8f95"
NEXT_PUBLIC_VAPI_WORKFLOW_ID_W3 = "6154afe8-a8c1-4bf4-942d-6240593e651a"


NEXT_PUBLIC_WORKFLOWS_API_URL=http://localhost/api/v1/workflows-api
NEXT_PUBLIC_SOCKET_URL=http://localhost
NEXT_PUBLIC_FRONTEND_URL=http://localhost
BACKEND_URL=http://passisto-enterprise-backend-v2:5000

# Socket.IO port (for the API route approach)
SOCKET_PORT=3001
NEXT_PUBLIC_ENV=dev

NEXT_PUBLIC_HIRING_API_URL=http://localhost