
services:
  client_front_end:
    container_name: pe_client_frontend_v2-payment
    build:
      context: .
      dockerfile: Dockerfile.development
      args:
        - NODE_ENV=development

    env_file:
      - .env.yassine

    environment:
      - PORT=3041

    ports:
      - 127.0.0.1:3041:3041
      - 10.0.4.1:3041:3041

    command: "npm run dev"

    volumes:
      - ./src:/app/src

    networks:
      - pe-net       


networks:
  pe-net:
    external: true
