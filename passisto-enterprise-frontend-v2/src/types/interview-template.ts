export interface InterviewQuestion {
  id?: string;
  question: string;
  type: "Technical" | "Behavioral" | "Problem-Solving" | "System-Design" | "Coding" | "Culture-Fit";
  difficulty: "Easy" | "Medium" | "Hard";
  category: string;
  expectedAnswer?: string;
  timeLimit?: number; // in minutes for individual question
  weight?: number; // scoring weight (1-10)
}

export interface StructuralInterviewTemplate {
  id?: string;
  // Basic template information
  name: string;
  description?: string;
  type: "Technical" | "Behavioral" | "Mixed" | "Custom" | "Sales" | "Leadership";
  
  // Job/Role specific details (used for AI question generation)
  role: string;
  level: "Junior" | "Mid" | "Senior" | "Lead" | "Principal" | "Director";
  techStack?: string[];
  
  // Template configuration
  estimatedDuration: number; // in minutes for the entire interview
  questionCount: number;
  isActive: boolean;
  
  // Generated or manually added questions
  questions: InterviewQuestion[];
  
  // AI Configuration (for question generation)
  aiSettings?: {
    useAI: boolean;
    model?: string;
    prompt?: string;
    temperature?: number;
  };
  
  // Company/Team context
  companyId: string;
  teamId?: string;
  createdBy: string;
  
  // Template metadata
  tags?: string[];
  difficulty?: "Easy" | "Medium" | "Hard";
  isPublic?: boolean;
  
  // Timestamps
  createdAt?: string;
  updatedAt?: string;
}

export interface EmailTemplate {
  id?: string;
  // Basic email template information
  name: string;
  subject: string;
  body: string;
  
  // Email platform/type
  platform: "HackerRank" | "Codility" | "LeetCode" | "Custom" | "Generic";
  
  // Template configuration
  isActive: boolean;
  
  // Available variables for email personalization
  variables: string[]; // e.g., ["{candidate_name}", "{position_title}", "{company_name}"]
  
  // Company/Team context
  companyId: string;
  teamId?: string;
  createdBy: string;
  
  // Template metadata
  tags?: string[];
  isPublic?: boolean;
  
  // Timestamps
  createdAt?: string;
  updatedAt?: string;
}

// Request types for API
export interface CreateStructuralInterviewTemplateRequest {
  name: string;
  description?: string;
  type: StructuralInterviewTemplate["type"];
  role: string;
  level: StructuralInterviewTemplate["level"];
  techStack?: string[];
  estimatedDuration: number; // entire interview duration in minutes
  questionCount: number;
  isActive: boolean;
  questions: Omit<InterviewQuestion, "id">[];
  aiSettings?: StructuralInterviewTemplate["aiSettings"];
  teamId?: string;
  tags?: string[];
  difficulty?: StructuralInterviewTemplate["difficulty"];
  isPublic?: boolean;
}

export interface CreateEmailTemplateRequest {
  name: string;
  subject: string;
  body: string;
  platform: EmailTemplate["platform"];
  isActive: boolean;
  variables: string[];
  teamId?: string;
  tags?: string[];
  isPublic?: boolean;
}

export interface UpdateStructuralInterviewTemplateRequest extends Partial<CreateStructuralInterviewTemplateRequest> {
  id: string;
}

export interface UpdateEmailTemplateRequest extends Partial<CreateEmailTemplateRequest> {
  id: string;
}
// Define interfaces for Interview Templates

export interface InterviewTemplateResponseDTO {
  id: number;
  phaseId: number;
  name: string;
  description?: string;
  template: any;
  templateType?: string;
  durationMinutes: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateStructuralTemplateDTO {
  phaseId: number;
  name: string;
  description?: string;
  templateType: 'structural';
  durationMinutes?: number;
  isActive?: boolean;
  role: string;
  level: string;
  techStack: string[];
  questionCount: number;
  questions: {};
}

export interface CreateEmailTemplateDTO {
  phaseId: number;
  name: string;
  description?: string;
  templateType: 'email';
  durationMinutes?: number;
  isActive?: boolean;
  subject: string;
  body: string;
  variables: string[];
}

export interface UpdateInterviewTemplateDTO {
  name?: string;
  description?: string;
  role?: string;
  level?: string;
  techStack?: string[];
  questionCount?: number;
  durationMinutes?: number;
  template?: any;
  isActive?: boolean;
}

export interface QuestionGenerationParamsDTO {
  role: string;
  level: string;
  techStack: string[];
  questionCount: number;
  interviewDuration: number;
}
