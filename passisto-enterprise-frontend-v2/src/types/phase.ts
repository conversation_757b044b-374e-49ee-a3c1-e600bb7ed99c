// types/phase.ts
export interface CreatePhaseDTO {
  name: string;
  description?: string;
  phaseType: 'APPLICATION' | 'SCREENING' | 'INTERVIEW' | 'ASSESSMENT' | 'FINAL_REVIEW';
  phaseOrder: number;
  isActive?: boolean;
  isRequired?: boolean;
  estimatedDuration?: number;
  startDate?: Date;
  endDate?: Date;
}

export interface PhaseResponseDTO {
  id: number;
  name: string;
  description?: string;
  phaseType: string;
  phaseOrder: number;
  projectId: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
