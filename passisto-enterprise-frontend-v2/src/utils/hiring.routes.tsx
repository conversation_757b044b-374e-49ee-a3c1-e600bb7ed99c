export const API_BASE_URL = `api-hiring/v1`;

export const PROJECT_ROUTES = {
  LIST: `${API_BASE_URL}/projects`,
  CREATE: `${API_BASE_URL}/projects`,
  DETAILS: (id: string) => `${API_BASE_URL}/projects/${id}`,
  UPDATE: (id: string) => `${API_BASE_URL}/projects/${id}`,
  DELETE: (id: string) => `${API_BASE_URL}/projects/${id}`,
};

export const JOB_OFFER_ROUTES = {
  CREATE: `${API_BASE_URL}/job-offers`,
  UPDATE: (id: number) => `${API_BASE_URL}/job-offers/${id}`,
  DELETE: (id: number) => `${API_BASE_URL}/job-offers/${id}`,
  ACTIVATE: (id: number) => `${API_BASE_URL}/job-offers/${id}/activate`,
  DEACTIVATE: (id: number) => `${API_BASE_URL}/job-offers/${id}/deactivate`,
  BY_PROJECT: (projectId: number) => `${API_BASE_URL}/job-offers/project/${projectId}`,
};

export const PHASE_CREATE = (projectId: number) => 
  `${API_BASE_URL}/phases/project/${projectId}`;
export const PHASE_LIST = (projectId: number) =>
  `${API_BASE_URL}/phases/project/${projectId}`;
export const PHASE_UPDATE = (id: number) =>
  `${API_BASE_URL}/phases/${id}`;
export const PHASE_PATCH = (id: number) =>
  `${API_BASE_URL}/phases/${id}`;
export const PHASE_DELETE = (id: number) =>
  `${API_BASE_URL}/phases/${id}`;
export const PHASE_SUGGEST = (projectId: number) =>
  `${API_BASE_URL}/phases/project/${projectId}/suggest-phase`;


// candidate routes

export const CANDIDATE_LIST = (projectId: number) =>
  `${API_BASE_URL}/candidates/project/${projectId}`;


