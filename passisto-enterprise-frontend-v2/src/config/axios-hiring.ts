import axios from "axios";

// const HIRING_API_BASE_URL = process.env.HIRING_API_BASE_URL || "https://enterprise.passisto.com/";
// const HIRING_API_BASE_URL = process.env.NEXT_PUBLIC_HIRING_API_URL || "http://localhost:4040";
const HIRING_API_BASE_URL =  "http://localhost/";
// const API_BASE_URL = "https://dev.passisto.com/";
const axiosHiringInstance = axios.create({
  baseURL: HIRING_API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

export default axiosHiringInstance;
