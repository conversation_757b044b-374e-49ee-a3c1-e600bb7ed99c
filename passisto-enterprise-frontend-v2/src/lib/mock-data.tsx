// Mock data for the user management system




// User interface
export interface User {
  id: string
  name: string
  email: string
  roles: string[]
  teams: string[]
  permissions?: string[] // For backward compatibility
  permissionsCount:Number
  extraPermissions: { permission: string; scopeType: string; scopeTarget?: string }[]
  revokedPermissions: { permission: string; scopeType: string; scopeTarget?: string }[]
  isActive: boolean
  createdAt: string
  
}

// Team interface
export interface Team {
  id: string
  name: string
  description: string
  permissions: { permission: string; scopeType: string }[]
  memberCount: number
}

// Permission interface
export interface Permission {
  permission: string
  scopeType: string
  scopeTarget?: string
}

// Define all available permissions
export const allPermissions = [
  "CAN_CREATE_TEAM",
  "CAN_CREATE_USER",
  "CAN_ASSIGN_USER_TO_TEAM",
  "CAN_CREATE_DATA_PROVIDER_FTP",
  "CAN_CREATE_DATA_PROVIDER_WEBSITE",
  "CAN_CREATE_DATA_PROVIDER_JIRA",
  "CAN_CREATE_DATA_PROVIDER_LOCAL",
  "CAN_ASSIGN_TEAM_TO_DATA_PROVIDER",
  "CAN_DELEGATE_CREATE_TEAM",
  "CAN_DELEGATE_CREATE_USER",
  "CAN_DELEGATE_ASSIGN_USER_TO_TEAM",
  "CAN_DELEGATE_CREATE_DATA_PROVIDER_FTP",
  "CAN_DELEGATE_CREATE_DATA_PROVIDER_HTTP",
  "CAN_DELEGATE_CREATE_DATA_PROVIDER_LOCAL",
  "CAN_VIEW_ANALYTICS",
  "CAN_EXPORT_DATA",
  "CAN_MANAGE_BILLING",
  "CAN_INVITE_USERS",
]

// Define available permission scopes
export const permissionScopes = [
  {
    id: "global",
    name: "Global",
    description: "Applies across the entire organization",
  },
  {
    id: "team",
    name: "Team",
    description: "Applies only to the user's teams",
  },
  {
    id: "self",
    name: "Self",
    description: "Applies only to the user's own resources",
  },
  {
    id: "project",
    name: "Project",
    description: "Applies to specific projects",
  },
]

// Group permissions by category for better UI organization
export const permissionCategories = [
  {
    name: "User & Team Management",
    permissions: ["CAN_CREATE_TEAM", "CAN_CREATE_USER", "CAN_ASSIGN_USER_TO_TEAM", "CAN_INVITE_USERS"],
  },
  {
    name: "Data Provider Management",
    permissions: [
      "CAN_CREATE_DATA_PROVIDER_FTP",
      "CAN_CREATE_DATA_PROVIDER_WEBSITE",
      "CAN_CREATE_DATA_PROVIDER_JIRA",
      "CAN_CREATE_DATA_PROVIDER_LOCAL",
      "CAN_ASSIGN_TEAM_TO_DATA_PROVIDER",
    ],
  },
  {
    name: "Delegation Permissions",
    permissions: [
      "CAN_DELEGATE_CREATE_TEAM",
      "CAN_DELEGATE_CREATE_USER",
      "CAN_DELEGATE_ASSIGN_USER_TO_TEAM",
      "CAN_DELEGATE_CREATE_DATA_PROVIDER_FTP",
      "CAN_DELEGATE_CREATE_DATA_PROVIDER_HTTP",
      "CAN_DELEGATE_CREATE_DATA_PROVIDER_LOCAL",
    ],
  },
  {
    name: "Analytics & Reporting",
    permissions: ["CAN_VIEW_ANALYTICS", "CAN_EXPORT_DATA"],
  },
  {
    name: "Administration",
    permissions: ["CAN_MANAGE_BILLING"],
  },
]

// Define default scopes for each permission
export const defaultPermissionScopes = {
  CAN_CREATE_TEAM: "global",
  CAN_CREATE_USER: "global",
  CAN_ASSIGN_USER_TO_TEAM: "team",
  CAN_CREATE_DATA_PROVIDER_FTP: "team",
  CAN_CREATE_DATA_PROVIDER_WEBSITE: "team",
  CAN_CREATE_DATA_PROVIDER_JIRA: "team",
  CAN_CREATE_DATA_PROVIDER_LOCAL: "self",
  CAN_ASSIGN_TEAM_TO_DATA_PROVIDER: "team",
  CAN_DELEGATE_CREATE_TEAM: "global",
  CAN_DELEGATE_CREATE_USER: "global",
  CAN_DELEGATE_ASSIGN_USER_TO_TEAM: "team",
  CAN_DELEGATE_CREATE_DATA_PROVIDER_FTP: "team",
  CAN_DELEGATE_CREATE_DATA_PROVIDER_HTTP: "team",
  CAN_DELEGATE_CREATE_DATA_PROVIDER_LOCAL: "team",
  CAN_VIEW_ANALYTICS: "team",
  CAN_EXPORT_DATA: "team",
  CAN_MANAGE_BILLING: "global",
  CAN_INVITE_USERS: "global",
}

// Define roles with their default permissions
export const roleDefinitions = [
  {
    id: "1",
    name: "ADMIN",
    displayName: "Administrator",
    description: "Full system access with all permissions",
    permissions: [
      { permission: "CAN_CREATE_TEAM", scopeType: "global" },
      { permission: "CAN_CREATE_USER", scopeType: "global" },
      { permission: "CAN_ASSIGN_USER_TO_TEAM", scopeType: "global" },
      { permission: "CAN_CREATE_DATA_PROVIDER_FTP", scopeType: "global" },
      { permission: "CAN_CREATE_DATA_PROVIDER_WEBSITE", scopeType: "global" },
      { permission: "CAN_CREATE_DATA_PROVIDER_JIRA", scopeType: "global" },
      { permission: "CAN_CREATE_DATA_PROVIDER_LOCAL", scopeType: "global" },
      { permission: "CAN_ASSIGN_TEAM_TO_DATA_PROVIDER", scopeType: "global" },
      { permission: "CAN_DELEGATE_CREATE_TEAM", scopeType: "global" },
      { permission: "CAN_DELEGATE_CREATE_USER", scopeType: "global" },
      { permission: "CAN_DELEGATE_ASSIGN_USER_TO_TEAM", scopeType: "global" },
      { permission: "CAN_VIEW_ANALYTICS", scopeType: "global" },
      { permission: "CAN_EXPORT_DATA", scopeType: "global" },
      { permission: "CAN_MANAGE_BILLING", scopeType: "global" },
      { permission: "CAN_INVITE_USERS", scopeType: "global" },
    ],
  },
  {
    id: "2",
    name: "MANAGER",
    displayName: "Manager",
    description: "Team management and limited administrative capabilities",
    permissions: [
      { permission: "CAN_CREATE_TEAM", scopeType: "team" },
      { permission: "CAN_ASSIGN_USER_TO_TEAM", scopeType: "team" },
      { permission: "CAN_CREATE_DATA_PROVIDER_JIRA", scopeType: "team" },
      { permission: "CAN_VIEW_ANALYTICS", scopeType: "team" },
      { permission: "CAN_EXPORT_DATA", scopeType: "team" },
      { permission: "CAN_INVITE_USERS", scopeType: "team" },
    ],
  },
  {
    id: "3",
    name: "MEMBER",
    displayName: "Member",
    description: "Standard team member with basic permissions",
    permissions: [
      { permission: "CAN_CREATE_DATA_PROVIDER_LOCAL", scopeType: "self" },
      { permission: "CAN_VIEW_ANALYTICS", scopeType: "team" },
    ],
  },
  {
    id: "4",
    name: "GUEST",
    displayName: "Guest",
    description: "Limited view-only access",
    permissions: [],
  },
]

// Mock teams for demonstration
export const mockTeams = [
  {
    id: "1",
    name: "Engineering",
    description: "Software development team",
    permissions: [
      { permission: "CAN_CREATE_DATA_PROVIDER_JIRA", scopeType: "team" },
      { permission: "CAN_VIEW_ANALYTICS", scopeType: "team" },
    ],
    memberCount: 12,
  },
  {
    id: "2",
    name: "Marketing",
    description: "Marketing and communications team",
    permissions: [
      { permission: "CAN_VIEW_ANALYTICS", scopeType: "team" },
      { permission: "CAN_EXPORT_DATA", scopeType: "team" },
    ],
    memberCount: 8,
  },
  {
    id: "3",
    name: "Operations",
    description: "Operations and support team",
    permissions: [
      { permission: "CAN_CREATE_DATA_PROVIDER_FTP", scopeType: "team" },
      { permission: "CAN_CREATE_DATA_PROVIDER_WEBSITE", scopeType: "team" },
    ],
    memberCount: 5,
  },
  {
    id: "4",
    name: "Executive",
    description: "Executive leadership team",
    permissions: [
      { permission: "CAN_VIEW_ANALYTICS", scopeType: "global" },
      { permission: "CAN_EXPORT_DATA", scopeType: "global" },
    ],
    memberCount: 3,
  },
]

// Helper function to calculate effective permissions for a user
export function calculateEffectivePermissions(user: User) {
  // Start with an empty map of permissions to their scopes
  const effectivePermissions = new Map()

  // Add permissions from roles
  user.roles.forEach((roleName) => {
    const role = roleDefinitions.find((r) => r.name === roleName)
    if (role) {
      role.permissions.forEach((permObj) => {
        const { permission, scopeType } = permObj
        // Only add if the permission doesn't exist or the scopeType is broader
        if (!effectivePermissions.has(permission) || isBroaderScope(scopeType, effectivePermissions.get(permission))) {
          effectivePermissions.set(permission, scopeType)
        }
      })
    }
  })

  // Add permissions from teams
  user.teams.forEach((teamName) => {
    const team = mockTeams.find((t) => t.name === teamName)
    if (team) {
      team.permissions.forEach((permObj) => {
        const { permission, scopeType } = permObj
        // Only add if the permission doesn't exist or the scopeType is broader
        if (!effectivePermissions.has(permission) || isBroaderScope(scopeType, effectivePermissions.get(permission))) {
          effectivePermissions.set(permission, scopeType)
        }
      })
    }
  })

  // Add extra permissions
  user.extraPermissions.forEach((permObj) => {
    const { permission, scopeType } = permObj
    // Only add if the permission doesn't exist or the scopeType is broader
    if (!effectivePermissions.has(permission) || isBroaderScope(scopeType, effectivePermissions.get(permission))) {
      effectivePermissions.set(permission, scopeType)
    }
  })

  // Remove revoked permissions
  user.revokedPermissions.forEach((permObj) => {
    const { permission, scopeType } = permObj
    // If the permission exists and the scopeType matches or is broader, remove it
    if (effectivePermissions.has(permission)) {
      const currentScope = effectivePermissions.get(permission)
      if (scopeType === currentScope || isBroaderScope(scopeType, currentScope)) {
        effectivePermissions.delete(permission)
      }
    }
  })

  // Convert Map to array of objects
  return Array.from(effectivePermissions.entries()).map(([permission, scopeType]) => ({ permission, scopeType }))
}

// Helper function to determine if one scopeType is broader than another
export function isBroaderScope(scope1: string, scope2: string) {
  const scopeHierarchy: Record<string, number> = {
    global: 3,
    team: 2,
    project: 1,
    self: 0,
  }

  return scopeHierarchy[scope1] > scopeHierarchy[scope2]
}

//-------------------------------------------------------------------------------------------------------------------

















// For backward compatibility
export const roles = roleDefinitions.map((role) => role.name)
export const permissions = allPermissions

// export const mockUsers: User[] = [
//   {
//     id: "1",
//     name: "John Doe",
//     email: "<EMAIL>",
//     roles: ["ADMIN"],
//     teams: ["Engineering", "Executive"],
//     extraPermissions: [],
//     revokedPermissions: [],
//   },
//   {
//     id: "2",
//     name: "Jane Smith",
//     email: "<EMAIL>",
//     roles: ["MANAGER"],
//     teams: ["Marketing"],
//     extraPermissions: [{ permission: "CAN_VIEW_ANALYTICS", scopeType: "global" }],
//     revokedPermissions: [],
//   },
//   {
//     id: "3",
//     name: "Bob Johnson",
//     email: "<EMAIL>",
//     roles: ["MEMBER"],
//     teams: ["Operations"],
//     extraPermissions: [],
//     revokedPermissions: [{ permission: "CAN_CREATE_DATA_PROVIDER_LOCAL", scopeType: "self" }],
//   },
//   {
//     id: "4",
//     name: "Alice Williams",
//     email: "<EMAIL>",
//     roles: ["MANAGER"],
//     teams: ["Engineering"],
//     extraPermissions: [],
//     revokedPermissions: [],
//   },
//   {
//     id: "5",
//     name: "Charlie Brown",
//     email: "<EMAIL>",
//     roles: ["MEMBER"],
//     teams: ["Marketing", "Operations"],
//     extraPermissions: [],
//     revokedPermissions: [],
//   },
//   {
//     id: "6",
//     name: "Diana Prince",
//     email: "<EMAIL>",
//     roles: ["GUEST"],
//     teams: [],
//     extraPermissions: [],
//     revokedPermissions: [],
//   },
// ]
