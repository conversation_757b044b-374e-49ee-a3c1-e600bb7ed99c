import axiosInstance from "@/config/axios";
import { CHECK_USAGE_LIMIT, CHECK_USAGE_LIMIT_BY_COMPANY_ID } from "../utils/routes";
import { toast } from "sonner";

interface UsageResponse {
  success: boolean;
  isExceeded: boolean;
  remaining: number;
  currentUsage: number;
  limit: number;
}

interface UsageLimitResult {
  canProceed: boolean;
  limit?: number;
  remaining?: number;
}

// Translation function type for flexibility
type TranslationFunction = (key: string, params?: Record<string, any>) => string;

export async function checkUsageLimit(
  key: string, // now accepts any feature key, e.g. "maxEmailAgents"
  token: string,
  warnThreshold: number = 10, // optional: warn when remaining <= threshold
  t?: TranslationFunction // optional translation function
): Promise<UsageLimitResult> {
  try {
    const response = await axiosInstance.post<UsageResponse>(
      CHECK_USAGE_LIMIT,
      { key },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (response.data.isExceeded) {
      const message = t 
        ? t("exceeded") 
        : "Usage limit exceeded for this feature.";
      toast.error(message);
      return {
        canProceed: false,
        limit: response.data.limit,
      };
    }

    if (response.data.remaining <= warnThreshold) {
      const message = t 
        ? t("warning", { remaining: response.data.remaining })
        : `Usage limit warning: You have ${response.data.remaining} remaining for this feature in your current plan.`;
      toast.warning(message);
    }

    return {
      canProceed: true,
      remaining: response.data.remaining,
      limit: response.data.limit,
    };
  } catch (error: any) {
    const message =
      error.response?.data?.message || 
      (t ? t("checkFailed") : "Failed to check usage limit");
    const errorTitle = t ? t("common.error") : "Error";
    toast.error(errorTitle, { description: message });
    return { canProceed: false };
  }
}

export async function checkUsageLimitByCompanyId(
  key: string, // now accepts any feature key
  companyId: string,
  warnThreshold: number = 10,
  t?: TranslationFunction // optional translation function
): Promise<UsageLimitResult> {
  try {
    const response = await axiosInstance.post<UsageResponse>(
      CHECK_USAGE_LIMIT_BY_COMPANY_ID,
      { key, companyId },
    );
    if (response.data.isExceeded) {
      const message = t 
        ? t("exceeded") 
        : "Usage limit exceeded for this feature.";
      toast.error(message);
      return {
        canProceed: false,
        limit: response.data.limit,
      };
    }

    if (response.data.remaining <= warnThreshold) {
      const message = t 
        ? t("warning", { remaining: response.data.remaining })
        : `Usage limit warning: You have ${response.data.remaining} remaining for this feature in your current plan.`;
      toast.warning(message);
    }

    return {
      canProceed: true,
      remaining: response.data.remaining,
      limit: response.data.limit,
    };
  } catch (error: any) {
    const message =
      error.response?.data?.message || 
      (t ? t("checkFailed") : "Failed to check usage limit");
    const errorTitle = t ? t("common.error") : "Error";
    toast.error(errorTitle, { description: message });
    return { canProceed: false };
  }
}
