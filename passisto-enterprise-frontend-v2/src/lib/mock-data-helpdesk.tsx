export interface User {
  id: string
  name: string
  email: string
  role: "client" | "agent"
  avatar?: string
}

export interface Message {
  id: string
  content: string
  author: string
  authorType: "client" | "agent"
  timestamp: string
  attachments?: string[]
}

export interface Ticket {
  id: string
  subject: string
  status: "nouveau" | "en-cours" | "resolu" | "ferme"
  priority: "basse" | "normale" | "haute" | "urgente"
  category: "C1" | "C2" | "C3" | "C4"
  createdAt: string
  updatedAt: string
  description: string
  clientName: string
  clientEmail: string
  assignedAgent?: string
  unreadMessages: number
  messages: Message[]
  history: Array<{
    id: string
    action: string
    timestamp: string
    author: string
  }>
}

export interface TicketFormData {
  name: string
  email: string
  subject: string
  description: string
  priority: "basse" | "normale" | "haute" | "urgente"
  category: "C1" | "C2" | "C3" | "C4"
  attachments?: File[]
}

// Mock Users
export const mockUsers: User[] = [
  {
    id: "1",
    name: "<PERSON>",
    email: "jean.du<PERSON>@email.com",
    role: "client",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "agent",
  },
  {
    id: "3",
    name: "<PERSON> Durand",
    email: "<EMAIL>",
    role: "agent",
  },
]

// Mock Messages
export const mockMessages: Record<string, Message[]> = {
  "1": [
    {
      id: "1",
      content:
        "Bonjour Jean, merci pour votre ticket. Je vais examiner votre problème de connexion. Pouvez-vous me confirmer l'adresse email que vous utilisez pour vous connecter ?",
      author: "Marie Martin",
      authorType: "agent",
      timestamp: "2024-01-15T11:00:00Z",
    },
    {
      id: "2",
      content:
        "Bonjour Marie, j'utilise bien l'adresse <EMAIL>. J'ai essayé de réinitialiser mon mot de passe mais je ne reçois pas l'email.",
      author: "Jean Dupont",
      authorType: "client",
      timestamp: "2024-01-15T11:30:00Z",
    },
    {
      id: "3",
      content:
        "Je vois le problème. Il y a eu un souci avec notre serveur d'emails hier soir. Je vais réinitialiser votre mot de passe manuellement et vous envoyer les nouvelles informations par email sécurisé.",
      author: "Marie Martin",
      authorType: "agent",
      timestamp: "2024-01-16T14:20:00Z",
    },
  ],
  "2": [
    {
      id: "4",
      content:
        "Bonjour, j'ai examiné votre problème de paiement. Il semble qu'il y ait eu une erreur temporaire avec notre processeur de paiement. Je vais procéder au remboursement immédiatement.",
      author: "Pierre Durand",
      authorType: "agent",
      timestamp: "2024-01-14T15:30:00Z",
    },
    {
      id: "5",
      content: "Merci beaucoup ! Quand puis-je m'attendre à voir le remboursement sur mon compte ?",
      author: "Jean Dupont",
      authorType: "client",
      timestamp: "2024-01-14T16:00:00Z",
    },
    {
      id: "6",
      content:
        "Le remboursement a été traité. Vous devriez le voir apparaître sur votre compte dans 2-3 jours ouvrables selon votre banque.",
      author: "Pierre Durand",
      authorType: "agent",
      timestamp: "2024-01-16T09:15:00Z",
    },
  ],
  "3": [
    {
      id: "7",
      content:
        "Bonjour, concernant votre question sur la facturation, voici le détail de votre dernière facture. Les frais supplémentaires correspondent aux services premium activés le mois dernier.",
      author: "Marie Martin",
      authorType: "agent",
      timestamp: "2024-01-11T10:00:00Z",
    },
    {
      id: "8",
      content: "Parfait, merci pour la clarification. Tout est clair maintenant.",
      author: "Jean Dupont",
      authorType: "client",
      timestamp: "2024-01-11T14:30:00Z",
    },
  ],
}

// Mock Tickets
export const mockTickets: Ticket[] = [
  {
    id: "1",
    subject: "Login connection issue",
    status: "en-cours",
    priority: "haute",
    category: "C2",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-16T14:20:00Z",
    description:
      "I can't log in since this morning. I get an 'Incorrect credentials' error even with the correct password.",
    clientName: "Jean Dupont",
    clientEmail: "<EMAIL>",
    assignedAgent: "Marie Martin",
    unreadMessages: 0,
    messages: mockMessages["1"],
    history: [
      {
        id: "1",
        action: "Ticket created",
        timestamp: "2024-01-15T10:30:00Z",
        author: "Jean Dupont",
      },
      {
        id: "2",
        action: "Ticket assigned to Marie Martin",
        timestamp: "2024-01-15T10:45:00Z",
        author: "System",
      },
      {
        id: "3",
        action: 'Status changed to "In Progress"',
        timestamp: "2024-01-15T11:00:00Z",
        author: "Marie Martin",
      },
    ],
  },
  {
    id: "2",
    subject: "Payment processing error",
    status: "en-cours",
    priority: "urgente",
    category: "C4",
    createdAt: "2024-01-14T09:15:00Z",
    updatedAt: "2024-01-16T09:15:00Z",
    description: "Transaction failed but amount was debited from my bank account",
    clientName: "Jean Dupont",
    clientEmail: "<EMAIL>",
    assignedAgent: "Pierre Durand",
    unreadMessages: 2,
    messages: mockMessages["2"],
    history: [
      {
        id: "4",
        action: "Ticket created",
        timestamp: "2024-01-14T09:15:00Z",
        author: "Jean Dupont",
      },
      {
        id: "5",
        action: "Ticket assigned to Pierre Durand",
        timestamp: "2024-01-14T09:30:00Z",
        author: "System",
      },
      {
        id: "6",
        action: 'Status changed to "In Progress"',
        timestamp: "2024-01-14T15:30:00Z",
        author: "Pierre Durand",
      },
    ],
  },
  {
    id: "3",
    subject: "Billing inquiry",
    status: "resolu",
    priority: "normale",
    category: "C1",
    createdAt: "2024-01-10T14:20:00Z",
    updatedAt: "2024-01-12T16:45:00Z",
    description: "Need clarification on my last invoice, unexplained additional charges",
    clientName: "Jean Dupont",
    clientEmail: "<EMAIL>",
    assignedAgent: "Marie Martin",
    unreadMessages: 0,
    messages: mockMessages["3"],
    history: [
      {
        id: "7",
        action: "Ticket created",
        timestamp: "2024-01-10T14:20:00Z",
        author: "Jean Dupont",
      },
      {
        id: "8",
        action: "Ticket assigned to Marie Martin",
        timestamp: "2024-01-10T14:35:00Z",
        author: "System",
      },
      {
        id: "9",
        action: 'Status changed to "In Progress"',
        timestamp: "2024-01-11T10:00:00Z",
        author: "Marie Martin",
      },
      {
        id: "10",
        action: 'Status changed to "Resolved"',
        timestamp: "2024-01-12T16:45:00Z",
        author: "Marie Martin",
      },
    ],
  },
  {
    id: "4",
    subject: "Account deletion request",
    status: "nouveau",
    priority: "basse",
    category: "C1",
    createdAt: "2024-01-17T11:00:00Z",
    updatedAt: "2024-01-17T11:00:00Z",
    description: "I want to permanently delete my account and all my personal data",
    clientName: "Jean Dupont",
    clientEmail: "<EMAIL>",
    unreadMessages: 0,
    messages: [],
    history: [
      {
        id: "11",
        action: "Ticket created",
        timestamp: "2024-01-17T11:00:00Z",
        author: "Jean Dupont",
      },
    ],
  },
  {
    id: "5",
    subject: "Mobile display issue",
    status: "ferme",
    priority: "normale",
    category: "C3",
    createdAt: "2024-01-08T16:30:00Z",
    updatedAt: "2024-01-09T10:15:00Z",
    description: "The interface doesn't display correctly on my Android phone",
    clientName: "Jean Dupont",
    clientEmail: "<EMAIL>",
    assignedAgent: "Marie Martin",
    unreadMessages: 0,
    messages: [],
    history: [
      {
        id: "12",
        action: "Ticket created",
        timestamp: "2024-01-08T16:30:00Z",
        author: "Jean Dupont",
      },
      {
        id: "13",
        action: "Ticket assigned to Marie Martin",
        timestamp: "2024-01-08T16:45:00Z",
        author: "System",
      },
      {
        id: "14",
        action: 'Status changed to "Resolved"',
        timestamp: "2024-01-09T09:30:00Z",
        author: "Marie Martin",
      },
      {
        id: "15",
        action: 'Status changed to "Closed"',
        timestamp: "2024-01-09T10:15:00Z",
        author: "System",
      },
    ],
  },
]

// Utility functions
export const getTicketById = (id: string): Ticket | undefined => {
  return mockTickets.find((ticket) => ticket.id === id)
}

export const getUserById = (id: string): User | undefined => {
  return mockUsers.find((user) => user.id === id)
}

export const getTicketsByStatus = (status: string): Ticket[] => {
  if (status === "all") return mockTickets
  return mockTickets.filter((ticket) => ticket.status === status)
}

export const searchTickets = (query: string): Ticket[] => {
  const lowercaseQuery = query.toLowerCase()
  return mockTickets.filter(
    (ticket) =>
      ticket.subject.toLowerCase().includes(lowercaseQuery) ||
      ticket.description.toLowerCase().includes(lowercaseQuery),
  )
}

export const addMessageToTicket = (ticketId: string, message: Omit<Message, "id">): Message => {
  const newMessage: Message = {
    ...message,
    id: Date.now().toString(),
  }

  // In a real app, this would update the database
  const ticket = mockTickets.find((t) => t.id === ticketId)
  if (ticket) {
    ticket.messages.push(newMessage)
    ticket.updatedAt = new Date().toISOString()
  }

  return newMessage
}

export const updateTicket = (ticketId: string, updates: Partial<Ticket>): Ticket | null => {
  const ticketIndex = mockTickets.findIndex((t) => t.id === ticketId)
  if (ticketIndex === -1) return null

  mockTickets[ticketIndex] = {
    ...mockTickets[ticketIndex],
    ...updates,
    updatedAt: new Date().toISOString(),
  }

  return mockTickets[ticketIndex]
}

export const deleteTicket = (ticketId: string): boolean => {
  const ticketIndex = mockTickets.findIndex((t) => t.id === ticketId)
  if (ticketIndex === -1) return false

  const ticket = mockTickets[ticketIndex]
  // Only allow deletion if ticket is new or in progress
  if (ticket.status === "nouveau" || ticket.status === "en-cours") {
    mockTickets.splice(ticketIndex, 1)
    return true
  }

  return false
}

export const createTicket = (ticketData: TicketFormData): Ticket => {
  const newTicket: Ticket = {
    id: (mockTickets.length + 1).toString(),
    subject: ticketData.subject,
    status: "nouveau",
    priority: ticketData.priority,
    category: ticketData.category,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    description: ticketData.description,
    clientName: ticketData.name,
    clientEmail: ticketData.email,
    unreadMessages: 0,
    messages: [],
    history: [
      {
        id: Date.now().toString(),
        action: "Ticket created",
        timestamp: new Date().toISOString(),
        author: ticketData.name,
      },
    ],
  }

  mockTickets.unshift(newTicket)
  return newTicket
}

