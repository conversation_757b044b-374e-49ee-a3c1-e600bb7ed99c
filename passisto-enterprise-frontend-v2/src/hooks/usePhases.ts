import { useState, useEffect, useCallback } from 'react'

type PhaseStatus = "PENDING" | "ACTIVE" | "COMPLETED" | "CANCELLED"
type PhaseType = "APPLICATION" | "SCREENING" | "INTERVIEW" | "ASSESSMENT" | "FINAL_REVIEW"

export type Phase = {
  id: number
  name: string
  type: PhaseType
  description?: string
  order: number
  status: PhaseStatus
  isRequired: boolean
  estimatedDuration?: number
  startDate?: string
  endDate?: string
  allowSkip: boolean
  requiresApproval: boolean
  autoAdvance: boolean
  passingScore?: number
  maxAttempts: number
  projectId: number
  createdAt: string
  updatedAt: string
  // Additional computed fields for UI
  isClosed?: boolean
  candidateCount?: number
  qualifiedCount?: number
  rejectedCount?: number
}

export type CreatePhaseInput = {
  name: string
  type: PhaseType
  description?: string
  phaseOrder: number
  isRequired?: boolean
  estimatedDuration?: number
  startDate?: Date
  endDate?: Date
  allowSkip?: boolean
  requiresApproval?: boolean
  autoAdvance?: boolean
  passingScore?: number
  maxAttempts?: number
  status: PhaseStatus
}

export type UpdatePhaseInput = Partial<CreatePhaseInput>

export function usePhases(projectId: string | number) {
  const [phases, setPhases] = useState<Phase[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const fetchPhases = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api-hiring/v1/phases/project/${projectId}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch phases')
      }
      
      const data = await response.json()
      
      // Map backend response to UI format
      const mappedPhases: Phase[] = data.map((phase: any) => ({
        id: phase.id,
        name: phase.name,
        type: phase.phaseType,
        description: phase.description,
        order: phase.phaseOrder,
        status: phase.status,
        isRequired: phase.isRequired,
        estimatedDuration: phase.estimatedDuration,
        startDate: phase.startDate,
        endDate: phase.endDate,
        allowSkip: phase.allowSkip,
        requiresApproval: phase.requiresApproval,
        autoAdvance: phase.autoAdvance,
        passingScore: phase.passingScore ? Number(phase.passingScore) : undefined,
        maxAttempts: phase.maxAttempts,
        projectId: phase.projectId,
        createdAt: phase.createdAt,
        updatedAt: phase.updatedAt,
        isClosed: phase.status === 'COMPLETED',
        candidateCount: 0, // Will be populated by separate API call
        qualifiedCount: 0,
        rejectedCount: 0
      }))
      
      setPhases(mappedPhases)
      setError(null)
    } catch (err) {
      setError(err as Error)
      console.error('Error fetching phases:', err)
    } finally {
      setLoading(false)
    }
  }, [projectId])

  const createPhase = useCallback(async (input: CreatePhaseInput) => {
    try {
      const response = await fetch(`/api-hiring/v1/phases/project/${projectId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })
      
      if (!response.ok) {
        throw new Error('Failed to create phase')
      }
      
      const newPhase = await response.json()
      await fetchPhases() // Refresh the list
      return newPhase
    } catch (err) {
      console.error('Error creating phase:', err)
      throw err
    }
  }, [projectId, fetchPhases])

  const updatePhase = useCallback(async (phaseId: number, input: UpdatePhaseInput) => {
    try {
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(input),
      })
      
      if (!response.ok) {
        throw new Error('Failed to update phase')
      }
      
      const updatedPhase = await response.json()
      await fetchPhases() // Refresh the list
      return updatedPhase
    } catch (err) {
      console.error('Error updating phase:', err)
      throw err
    }
  }, [fetchPhases])

  const closePhase = useCallback(async (phaseId: number) => {
    try {
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'COMPLETED' }),
      })
      
      if (!response.ok) {
        throw new Error('Failed to close phase')
      }
      
      await fetchPhases() // Refresh the list
    } catch (err) {
      console.error('Error closing phase:', err)
      throw err
    }
  }, [fetchPhases])

  const deletePhase = useCallback(async (phaseId: number) => {
    try {
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}`, {
        method: 'DELETE',
      })
      
      if (!response.ok) {
        throw new Error('Failed to delete phase')
      }
      
      await fetchPhases() // Refresh the list
    } catch (err) {
      console.error('Error deleting phase:', err)
      throw err
    }
  }, [fetchPhases])

  useEffect(() => {
    if (projectId) {
      fetchPhases()
    }
  }, [projectId, fetchPhases])

  return {
    phases,
    loading,
    error,
    refetch: fetchPhases,
    createPhase,
    updatePhase,
    closePhase,
    deletePhase,
  }
}
