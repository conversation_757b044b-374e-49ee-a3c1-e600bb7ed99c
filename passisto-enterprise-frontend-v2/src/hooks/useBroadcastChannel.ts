// hooks/useBroadcastChannel.ts
import { useEffect, useRef, useCallback } from 'react';

interface JobOfferMessage {
  type: 'JOB_OFFER_DATA';
  payload: {
    title?: string;
    description?: string;
    location?: string;
    department?: string;
    experienceLevel?: string;
    workArrangement?: string;
    requiredSkills?: string;
    languages?: string;
    educationLevel?: string;
    yearsOfExperience?: number;
    benefits?: string;
    additionalBenefits?: string;
    applicationInstructions?: string;
  };
}

type BroadcastMessage = JobOfferMessage;

export function useBroadcastChannel(
  channelName: string,
  onMessage?: (message: BroadcastMessage) => void
) {
  const channelRef = useRef<BroadcastChannel | null>(null);

  useEffect(() => {
    // Check if BroadcastChannel is supported
    if (typeof BroadcastChannel === 'undefined') {
      console.warn('BroadcastChannel API is not supported in this browser');
      return;
    }

    // Create the channel
    channelRef.current = new BroadcastChannel(channelName);

    // Set up message listener
    if (onMessage) {
      channelRef.current.onmessage = (event: MessageEvent<BroadcastMessage>) => {
        console.log("[BroadcastChannel] Received:", event.data);
        onMessage(event.data);
      };
    }

    // Cleanup on unmount
    return () => {
      channelRef.current?.close();
      channelRef.current = null;
    };
  }, [channelName, onMessage]);

  const postMessage = useCallback((message: BroadcastMessage) => {
    if (channelRef.current) {
      channelRef.current.postMessage(message);
    } else {
      console.warn('BroadcastChannel is not initialized');
    }
  }, []);

  return { postMessage };
}