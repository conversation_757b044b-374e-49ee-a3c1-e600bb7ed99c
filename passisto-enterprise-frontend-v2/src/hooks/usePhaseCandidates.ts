import { useState, useEffect, useCallback } from 'react'

type CandidateStatus = "PARSING_CV" | "APPLIED" | "IN_REVIEW" | "INTERVIEWING" | "ASSESSMENT" | "HIRED" | "REJECTED" | "WITHDRAWN"

export interface PhaseCandidate {
  id: number;
  name: string;
  email: string;
  phone?: string;
  city?: string;
  location?: string;
  currentPhaseId?: number;
  currentStatus?: string;
  applicationDate?: string;
  updatedAt?: string;
  phaseScore?: number;
  feedback?: string;
  phaseStatus?: "PENDING" | "QUALIFIED" | "REJECTED" | "IN_PROGRESS";
  actions?: any[];
  skills?: string[];
  parsedProfile?: any;
  avatar: string;
}

export type CandidateAction = {
  id: string
  type: "EMAIL_SENT" | "SCORE_ASSIGNED" | "INTERVIEW_SENT" | "FEEDBACK_GENERATED"
  timestamp: string
  details?: string
}

export type ScoringResult = {
  scores: any
  final_score: number
  recommendation: string
  confidence_level: string
  flags: string[]
  scored_at: string
}

export function usePhaseCandidates(phaseId: number | null) {
  const [candidates, setCandidates] = useState<PhaseCandidate[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const fetchCandidates = useCallback(async () => {
    if (!phaseId) {
      setCandidates([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}/candidates`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch candidates')
      }
      
      const data = await response.json()
      
      // Map backend response to UI format
      const mappedCandidates: PhaseCandidate[] = data.map((candidate: any) => ({
        id: candidate.id,
        name: candidate.name || 'Unknown',
        email: candidate.email || '',
        phone: candidate.phone,
        city: candidate.city,
        location: candidate.city,
        currentPhaseId: candidate.currentPhaseId,
        currentStatus: candidate.currentStatus,
        applicationDate: candidate.applicationDate,
        updatedAt: candidate.updatedAt,
        phaseScore: candidate.score ? Number(candidate.score) : undefined,
        feedback: candidate.feedback, // Added feedback field
        phaseStatus: determinePhaseStatus(candidate),
        actions: [], // TODO: Fetch from action history API
        skills: extractSkills(candidate.parsedProfile),
        parsedProfile: candidate.parsedProfile,
        avatar: generateAvatar(candidate.name, candidate.email)
      }))
      
      setCandidates(mappedCandidates)
      setError(null)
    } catch (err) {
      setError(err as Error)
      console.error('Error fetching candidates:', err)
    } finally {
      setLoading(false)
    }
  }, [phaseId])

  const scoreCandidate = useCallback(async (candidateId: number): Promise<ScoringResult> => {
    if (!phaseId) throw new Error('No phase selected')
    
    try {
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}/cv-screening/${candidateId}`, {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error('Failed to score candidate')
      }
      
      const result = await response.json()
      await fetchCandidates() // Refresh the list
      return result
    } catch (err) {
      console.error('Error scoring candidate:', err)
      throw err
    }
  }, [phaseId, fetchCandidates])

  const bulkScoreCandidates = useCallback(async () => {
    if (!phaseId) throw new Error('No phase selected')
    
    try {
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}/cv-screening/bulk-score`, {
        method: 'POST',
      })
      
      if (!response.ok) {
        throw new Error('Failed to bulk score candidates')
      }
      
      const result = await response.json()
      await fetchCandidates() // Refresh the list
      return result
    } catch (err) {
      console.error('Error bulk scoring candidates:', err)
      throw err
    }
  }, [phaseId, fetchCandidates])

  const advanceCandidates = useCallback(async (candidateIds: number[]) => {
    if (!phaseId) throw new Error('No phase selected')
    
    try {
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}/candidates/advance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(candidateIds),
      })
      
      if (!response.ok) {
        throw new Error('Failed to advance candidates')
      }
      
      const result = await response.json()
      await fetchCandidates() // Refresh the list
      return result
    } catch (err) {
      console.error('Error advancing candidates:', err)
      throw err
    }
  }, [phaseId, fetchCandidates])

  const updateCandidateStatus = useCallback(async (candidateId: number, status: CandidateStatus) => {
    try {
      const response = await fetch(`/api-hiring/v1/candidates/${candidateId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ currentStatus: status }),
      })
      
      if (!response.ok) {
        throw new Error('Failed to update candidate status')
      }
      
      await fetchCandidates() // Refresh the list
    } catch (err) {
      console.error('Error updating candidate status:', err)
      throw err
    }
  }, [fetchCandidates])

  useEffect(() => {
    fetchCandidates()
  }, [fetchCandidates])

  return {
    candidates,
    loading,
    error,
    refetch: fetchCandidates,
    scoreCandidate,
    bulkScoreCandidates,
    advanceCandidates,
    updateCandidateStatus,
  }
}

// Helper functions
export function determinePhaseStatus(candidate: any): "PENDING" | "QUALIFIED" | "REJECTED" | "IN_PROGRESS" {
  // This logic should match your backend's phase status determination
  if (candidate.currentStatus === 'REJECTED' || candidate.currentStatus === 'WITHDRAWN') {
    return 'REJECTED'
  }
  
  if (candidate.score) {
    return candidate.score >= 70 ? 'QUALIFIED' : 'IN_PROGRESS'
  }
  
  return 'PENDING'
}

function extractSkills(parsedProfile: any): string[] {
  if (!parsedProfile) return []
  
  try {
    const profile = typeof parsedProfile === 'string' ? JSON.parse(parsedProfile) : parsedProfile
    return profile?.skills || []
  } catch {
    return []
  }
}

function generateAvatar(name?: string, email?: string): string {
  const displayName = name || email || 'User'
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(displayName)}&background=random`
}
