import { useCallback } from 'react'

export type BulkEmailData = {
  subject: string
  body: string
}

export type InterviewData = {
  template: string
  templateId?: number
}

export function usePhaseActions(phaseId?: number) {
  const sendBulkEmail = useCallback(async (candidateIds: number[], emailData: BulkEmailData) => {
    if (!phaseId) {
      throw new Error('Phase ID is required for bulk email')
    }
    
    try {
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}/candidates/bulk-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidateIds,
          subject: emailData.subject,
          message: emailData.body,
        }),
      })
      
      if (!response.ok) {
        throw new Error('Failed to send bulk email')
      }
      
      return await response.json()
    } catch (err) {
      console.error('Error sending bulk email:', err)
      throw err
    }
  }, [phaseId])

  const sendBulkInterview = useCallback(async (
    candidateIds: number[],
    interviewData: InterviewData
  ) => {
    if (!phaseId) {
      throw new Error('Phase ID is required for bulk interview')
    }
    
    try {
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}/candidates/bulk-interview`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          candidateIds,
          templateId: interviewData.templateId,
          sendEmail: true,
        }),
      })
      
      if (!response.ok) {
        throw new Error('Failed to send bulk interviews')
      }
      
      return await response.json()
    } catch (err) {
      console.error('Error sending bulk interviews:', err)
      throw err
    }
  }, [phaseId])

  const advanceCandidates = useCallback(async (candidateIds: number[]) => {
    if (!phaseId) {
      throw new Error('Phase ID is required to advance candidates')
    }
    
    try {
      const response = await fetch(`/api-hiring/v1/phases/${phaseId}/candidates/advance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(candidateIds),
      })
      
      if (!response.ok) {
        throw new Error('Failed to advance candidates')
      }
      
      return await response.json()
    } catch (err) {
      console.error('Error advancing candidates:', err)
      throw err
    }
  }, [phaseId])

  const rejectCandidates = useCallback(async (candidateIds: number[]) => {
    try {
      // Update status for each candidate to REJECTED
      // Use the same payload property as usePhaseCandidates.updateCandidateStatus ({ currentStatus })
      const results = await Promise.all(
        candidateIds.map(async (candidateId) => {
          try {
            const response = await fetch(`/api-hiring/v1/candidates/${candidateId}/status`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                currentStatus: 'REJECTED',
              }),
            })

            if (!response.ok) {
              return { candidateId, success: false }
            }

            return { candidateId, success: true }
          } catch (err) {
            return { candidateId, success: false }
          }
        })
      )

      return {
        success: true,
        results,
      }
    } catch (err) {
      console.error('Error rejecting candidates:', err)
      throw err
    }
  }, [])

  return {
    sendBulkEmail,
    sendBulkInterview,
    advanceCandidates,
    rejectCandidates,
  }
}
