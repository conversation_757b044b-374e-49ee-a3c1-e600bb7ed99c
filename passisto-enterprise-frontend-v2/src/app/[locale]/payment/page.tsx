"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Mail,
  Bot,
  Loader2,
  Sparkles,
  Zap,
  Clock,
  Shield,
  Check,
  LayoutDashboard,
  Users,
  Building2,
  ChevronDown,
  ChevronUp,
  Star,
  ArrowRight,
  FormInput,
  Search,
  MessageSquareText,
  Database,
  FileStack,
  HelpCircle,
} from "lucide-react"
import { toast } from "sonner"
import axiosInstance from "@/config/axios"
import { CREATE_STRIPE_CHECKOUT_SESSION, GET_STRIPE_PLANS, SUBSCRIPTION_NAME } from "@/utils/routes"
import { useAuth } from "@clerk/nextjs"
import { UserButton } from "@clerk/nextjs"
import { Link } from "@/i18n/nvigation"
import Image from "next/image"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useTranslations } from "next-intl"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

// Define feature icons mapping here in the frontend
export const FEATURE_ICONS = {
  "email-builder": Mail,
  "form-builder": FormInput,
  "ai-interviewer": Bot,
  search: Search,
  chat: MessageSquareText,
  users: Users,
  storage: Database,
  "file-storage": FileStack,
} as const

interface PlanFeature {
  id: string
  name: string
  limit: string
  description: string
}

interface Plan {
  name: string
  monthlyPrice: number
  annualDiscount: number
  monthlyPriceId: string
  yearlyPriceId: string
  features: PlanFeature[]
}

export default function PaymentPage() {
  const t = useTranslations()
  const { getToken } = useAuth()
  const [plans, setPlans] = useState<Plan[]>([])
  const [isAnnual, setIsAnnual] = useState(false)
  const [pageLoading, setPageLoading] = useState(true)
  const [loadingStates, setLoadingStates] = useState({})
  const [currentSubscription, setCurrentSubscription] = useState("")
  const [showSubscriptionDialog, setShowSubscriptionDialog] = useState(false)
  const [allPlansExpanded, setAllPlansExpanded] = useState(false)

  const commonFeatures = [
    { id: "ai_powered", name: t("ai-powered"), description: t("advanced-ai-technology"), icon: Sparkles },
    { id: "real_time", name: t("real-time-analysis"), description: t("instant-feedback"), icon: Zap },
    { id: "available", name: t("24-7-available"), description: t("access-anytime"), icon: Clock },
    { id: "security", name: t("enterprise-security"), description: t("bank-grade-encryption"), icon: Shield },
  ]

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = await getToken()
        const subscriptionResponse = await axiosInstance.get(SUBSCRIPTION_NAME, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })
        console.log(subscriptionResponse.data.subscriptionName)
        setCurrentSubscription(subscriptionResponse.data.subscriptionName)
        const plansResponse = await axiosInstance.get(GET_STRIPE_PLANS)
        setPlans(plansResponse.data)
      } catch (error) {
        toast.error(t("failed-to-load-subscription-information"))
        console.error("Error fetching data:", error)
      } finally {
        setPageLoading(false)
      }
    }
    fetchData()
  }, [getToken])

  const handleSubscribe = async (planId: string, planName: string) => {
    if (planName === "Free" && currentSubscription !== "No Active Subscription") {
      setShowSubscriptionDialog(true)
      return
    }

    setLoadingStates((prev) => ({ ...prev, [planId]: true }))
    try {
      const token = await getToken()
      const response = await axiosInstance.post(
        CREATE_STRIPE_CHECKOUT_SESSION,
        {
          priceId: planId,
          planName: planName,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (response.data.url) {
        window.location.href = response.data.url
      }
      toast.success(t("redirecting-to-checkout"))
    } catch (error) {
      toast.error(t("failed-to-initialize-checkout"))
      console.error("Checkout error:", error)
    } finally {
      setLoadingStates((prev) => ({ ...prev, [planId]: false }))
    }
  }

  const SubscriptionDialog = () => (
    <Dialog open={showSubscriptionDialog} onOpenChange={setShowSubscriptionDialog}>
      <DialogContent className="sm:max-w-md border-slate-200 dark:border-slate-700">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100">
            {t("free-plan-subscription-limit-reached")}
          </DialogTitle>
          <DialogDescription className="text-slate-600 dark:text-slate-400 leading-relaxed">
            {t(
              "you-can-only-subscribe-to-the-free-plan-once-even-if-your-previous-free-plan-subscription-is-no-longer-active-you-cannot-subscribe-to-the-free-plan-again-to-continue-using-the-service-please-choose-a-paid-plan",
            )}
          </DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  )

  if (pageLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 dark:text-blue-400" />
          <p className="text-slate-600 dark:text-slate-400">Loading plans...</p>
        </div>
      </div>
    )
  }

  if (!plans.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 flex items-center justify-center">
        <div className="text-center space-y-4">
          <p className="text-slate-900 dark:text-slate-100 font-medium text-lg">{t("failed-to-load-plans")}</p>
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="border-slate-300 dark:border-slate-600"
          >
            {t("try-again")}
          </Button>
        </div>
      </div>
    )
  }

  // Create enterprise plan object for consistent handling
  const enterprisePlan = {
    name: "Customize",
    monthlyPrice: 0,
    annualDiscount: 0,
    monthlyPriceId: "",
    yearlyPriceId: "",
    features: [
      {
        id: "custom_1",
        name: t("custom-integration"),
        description: t("tailored-to-your-needs"),
        limit: "Unlimited",
      },
      {
        id: "custom_2",
        name: t("dedicated-support"),
        description: t("priority-24-7-support"),
        limit: "24/7",
      },
      {
        id: "custom_3",
        name: t("custom-features"),
        description: t("built-for-your-workflow"),
        limit: "Custom",
      },
    ],
  }

  const allPlans = [...plans, enterprisePlan]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Navigation */}
      <nav className="border-b border-slate-200/60 dark:border-slate-700/60 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl py-4 px-6 flex justify-between items-center sticky top-0 z-50 shadow-sm">
        <Link href="/enterprise/dashboard" className="hover:opacity-90 transition-opacity">
          <Image
            src="/passisto_logo.png"
            alt="Passisto Logo"
            width={150}
            height={40}
            priority
            className="dark:invert"
          />
        </Link>
        <UserButton afterSignOutUrl="/auth/login">
          <UserButton.MenuItems>
            <UserButton.Action
              label={t("dashboard")}
              labelIcon={<LayoutDashboard size={16} />}
              onClick={() => (window.location.href = "/enterprise/dashboard")}
            />
            <UserButton.Action
              label="support"
              labelIcon={<HelpCircle size={16} />}
              onClick={() => (window.location.href = "/support")}
            />
          </UserButton.MenuItems>
        </UserButton>
      </nav>

      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-slate-900 via-blue-800 to-slate-900 dark:from-white dark:via-blue-200 dark:to-white bg-clip-text text-transparent">
            {t("choose-your-plan")}
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
            {t(
              "access-powerful-ai-tools-to-streamline-your-recruitment-process-and-find-the-perfect-candidates-faster",
            )}
          </p>
        </div>

        {/* Billing Toggle */}
        <div className="flex items-center justify-center gap-4 mb-12">
          <div className="flex items-center gap-3 bg-white dark:bg-slate-800 rounded-xl p-1 shadow-lg border border-slate-200 dark:border-slate-700">
            <button
              onClick={() => setIsAnnual(false)}
              className={`px-6 py-3 rounded-lg text-sm font-medium transition-all ${
                !isAnnual
                  ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-md"
                  : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700/50"
              }`}
            >
              {t("monthly")}
            </button>
            <button
              onClick={() => setIsAnnual(true)}
              className={`px-6 py-3 rounded-lg text-sm font-medium transition-all flex items-center gap-2 ${
                isAnnual
                  ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-md"
                  : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200 hover:bg-slate-50 dark:hover:bg-slate-700/50"
              }`}
            >
              {t("annual")}
              <Badge className="text-xs bg-gradient-to-r from-emerald-500 to-emerald-600 text-white border-0">
                {t("save-20")}
              </Badge>
            </button>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid lg:grid-cols-3 md:grid-cols-2 gap-8">
          {allPlans.map((plan) => {
            const annualMonthlyPrice = Number.parseFloat(
              (plan.monthlyPrice * (1 - plan.annualDiscount / 100)).toFixed(2),
            )
            const isPopular = plan.name !== "Free" && plan.name !== "Customize"
            const isEnterprise = plan.name === "Customize"
            const isFree = plan.name === "Free"
            const isExpanded = allPlansExpanded

            return (
              <div
                key={plan.name}
                className={`relative rounded-2xl overflow-hidden transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 ${
                  isPopular
                    ? "bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950/50 dark:to-indigo-950/50 border-2 border-blue-200 dark:border-blue-800 shadow-xl ring-1 ring-blue-200/50 dark:ring-blue-800/50"
                    : isEnterprise
                      ? "bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-900 border-2 border-slate-300 dark:border-slate-600"
                      : "bg-white dark:bg-slate-800 border-2 border-slate-200 dark:border-slate-700 shadow-lg"
                } ${isExpanded ? "shadow-2xl" : ""}`}
              >
                {/* Popular Badge */}
                {isPopular && (
                  <div className="absolute -top-0 mt-2 left-1/2 transform -translate-x-1/2 z-10">
                    <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg flex items-center gap-2">
                      <Star className="w-4 h-4" />
                      {t("popular")}
                    </div>
                  </div>
                )}

                {/* Plan Header */}
                <div className={`p-8 ${isPopular ? "pt-12" : "pt-8"}`}>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-3 mb-3">
                      {isEnterprise && <Building2 className="w-7 h-7 text-slate-600 dark:text-slate-400" />}
                      <h3
                        className={`text-3xl font-bold ${
                          isPopular
                            ? "text-blue-900 dark:text-blue-100"
                            : isEnterprise
                              ? "text-slate-800 dark:text-slate-200"
                              : "text-slate-900 dark:text-slate-100"
                        }`}
                      >
                        {plan.name}
                      </h3>
                    </div>
                    <p
                      className={`text-sm mb-6 ${
                        isPopular ? "text-blue-700 dark:text-blue-300" : "text-slate-600 dark:text-slate-400"
                      }`}
                    >
                      {isEnterprise ? t("custom-solutions-for-large-teams") : t("all-in-one-ai-recruitment-suite")}
                    </p>

                    <div className="mb-8">
                      <div className="flex items-baseline justify-center gap-1">
                        <span
                          className={`text-5xl font-bold ${
                            isPopular
                              ? "bg-gradient-to-r from-blue-700 to-indigo-700 dark:from-blue-300 dark:to-indigo-300 bg-clip-text text-transparent"
                              : isEnterprise
                                ? "text-slate-700 dark:text-slate-300"
                                : "text-slate-900 dark:text-slate-100"
                          }`}
                        >
                          {isEnterprise ? "Custom" : isAnnual ? `$${annualMonthlyPrice}` : `$${plan.monthlyPrice}`}
                        </span>
                        {!isEnterprise && (
                          <span
                            className={`text-lg ${
                              isPopular ? "text-blue-600 dark:text-blue-400" : "text-slate-500 dark:text-slate-400"
                            }`}
                          >
                            /month
                          </span>
                        )}
                      </div>
                      {!isEnterprise && isAnnual && (
                        <p
                          className={`text-sm mt-2 ${
                            isPopular ? "text-blue-600 dark:text-blue-400" : "text-slate-500 dark:text-slate-400"
                          }`}
                        >
                          Billed annually
                        </p>
                      )}
                      {isEnterprise && (
                        <p className="text-sm text-slate-500 dark:text-slate-400 mt-2">
                          {t("tailored-pricing-for-your-specific-needs")}
                        </p>
                      )}
                    </div>

                    <Button
                      size="lg"
                      className={`w-full mb-6 h-12 text-base font-semibold transition-all duration-200 ${
                        isPopular
                          ? "bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl"
                          : isEnterprise
                            ? "bg-gradient-to-r from-slate-700 to-slate-800 hover:from-slate-800 hover:to-slate-900 text-white shadow-lg hover:shadow-xl"
                            : isFree
                              ? "bg-gradient-to-r from-emerald-500 to-emerald-600 hover:from-emerald-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl"
                              : "border-2 border-slate-300 dark:border-slate-600 hover:border-blue-400 dark:hover:border-blue-500 bg-white dark:bg-slate-800 hover:bg-blue-50 dark:hover:bg-blue-950/20"
                      }`}
                      onClick={() => {
                        if (isEnterprise) {
                          window.location.href = "mailto:<EMAIL>?subject=Enterprise Plan Inquiry"
                        } else {
                          handleSubscribe(isAnnual ? plan.yearlyPriceId : plan.monthlyPriceId, plan.name)
                        }
                      }}
                      disabled={
                        !isEnterprise &&
                        (loadingStates[isAnnual ? plan.yearlyPriceId : plan.monthlyPriceId] ||
                          (plan.name === "Free" && currentSubscription !== t("no-active-subscription-0")))
                      }
                    >
                      {!isEnterprise && loadingStates[isAnnual ? plan.yearlyPriceId : plan.monthlyPriceId] ? (
                        <Loader2 className="h-5 w-5 animate-spin mr-2" />
                      ) : plan.name === "Free" ? (
                        currentSubscription !== t("no-active-subscription") ? (
                          t("currently-subscribed")
                        ) : (
                          <>
                            {t("start-free-trial")} <ArrowRight className="w-5 h-5 ml-2" />
                          </>
                        )
                      ) : isEnterprise ? (
                        <>
                          <Users className="h-5 w-5 mr-2" />
                          {t("contact-sales")}
                        </>
                      ) : (
                        <>
                          {t("subscribe-now")} <ArrowRight className="w-5 h-5 ml-2" />
                        </>
                      )}
                    </Button>

                    <Collapsible open={allPlansExpanded} onOpenChange={setAllPlansExpanded}>
                      <CollapsibleTrigger asChild>
                        <Button
                          variant="ghost"
                          className={`w-full h-auto font-medium transition-colors ${
                            isPopular
                              ? "text-blue-700 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-200 hover:bg-blue-100 dark:hover:bg-blue-900/20"
                              : "text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-200 hover:bg-slate-100 dark:hover:bg-slate-700/50"
                          }`}
                        >
                          {allPlansExpanded ? (
                            <>
                              Hide all details <ChevronUp className="w-4 h-4 ml-1" />
                            </>
                          ) : (
                            <>
                              View all details <ChevronDown className="w-4 h-4 ml-1" />
                            </>
                          )}
                        </Button>
                      </CollapsibleTrigger>
                    </Collapsible>
                  </div>
                </div>

                {/* Plan Details */}
                <Collapsible open={allPlansExpanded} onOpenChange={setAllPlansExpanded}>
                  <CollapsibleContent>
                    <div
                      className={`p-8 border-t-2 ${
                        isPopular
                          ? "border-blue-200 dark:border-blue-800 bg-white/50 dark:bg-slate-900/50"
                          : isEnterprise
                            ? "border-slate-300 dark:border-slate-600 bg-white/50 dark:bg-slate-800/50"
                            : "border-slate-200 dark:border-slate-700 bg-slate-50/50 dark:bg-slate-800/50"
                      }`}
                    >
                      {/* Plan Features */}
                      <div className="mb-8">
                        <h4
                          className={`font-bold text-lg mb-6 flex items-center gap-3 ${
                            isPopular ? "text-blue-900 dark:text-blue-100" : "text-slate-900 dark:text-slate-100"
                          }`}
                        >
                          <div
                            className={`p-2 rounded-lg ${
                              isPopular ? "bg-blue-100 dark:bg-blue-900/30" : "bg-slate-100 dark:bg-slate-700"
                            }`}
                          >
                            <Sparkles
                              className={`w-5 h-5 ${
                                isPopular ? "text-blue-600 dark:text-blue-400" : "text-slate-600 dark:text-slate-400"
                              }`}
                            />
                          </div>
                          {isEnterprise ? t("enterprise-features") : t("plan-features")}
                        </h4>
                        <div className="space-y-4">
                          {plan.features.map((feature) => {
                            const FeatureIcon =
                              FEATURE_ICONS[feature.id as keyof typeof FEATURE_ICONS] ||
                              (isEnterprise ? Building2 : Mail)
                            return (
                              <div
                                key={feature.id}
                                className={`flex items-start gap-4 p-4 rounded-xl transition-colors hover:scale-[1.02] ${
                                  isPopular
                                    ? "bg-blue-50/80 dark:bg-blue-950/30 border border-blue-100 dark:border-blue-900/50"
                                    : "bg-white dark:bg-slate-700/50 border border-slate-200 dark:border-slate-600/50"
                                }`}
                              >
                                <div
                                  className={`p-2.5 rounded-lg flex-shrink-0 ${
                                    isPopular ? "bg-blue-100 dark:bg-blue-900/50" : "bg-slate-100 dark:bg-slate-600"
                                  }`}
                                >
                                  <FeatureIcon
                                    className={`w-5 h-5 ${
                                      isPopular
                                        ? "text-blue-600 dark:text-blue-400"
                                        : "text-slate-600 dark:text-slate-400"
                                    }`}
                                  />
                                </div>
                                <div className="flex-1">
                                  <div
                                    className={`font-semibold text-sm mb-1 ${
                                      isPopular
                                        ? "text-blue-900 dark:text-blue-100"
                                        : "text-slate-900 dark:text-slate-100"
                                    }`}
                                  >
                                    {feature.name}
                                  </div>
                                  <div
                                    className={`text-xs mb-2 ${
                                      isPopular
                                        ? "text-blue-700 dark:text-blue-300"
                                        : "text-slate-600 dark:text-slate-400"
                                    }`}
                                  >
                                    {feature.description}
                                  </div>
                                  <Badge
                                    className={`text-xs font-medium ${
                                      isPopular
                                        ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0"
                                        : "bg-gradient-to-r from-slate-500 to-slate-600 text-white border-0"
                                    }`}
                                  >
                                    {feature.limit}
                                  </Badge>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>

                      {/* Common Features */}
                      <div>
                        <h4
                          className={`font-bold text-lg mb-6 flex items-center gap-3 ${
                            isPopular
                              ? "text-emerald-800 dark:text-emerald-200"
                              : "text-emerald-700 dark:text-emerald-300"
                          }`}
                        >
                          <div className="p-2 rounded-lg bg-emerald-100 dark:bg-emerald-900/30">
                            <Shield className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                          </div>
                          {t("included-for-all-plans")}
                        </h4>
                        <div className="space-y-3">
                          {commonFeatures.map((feature) => (
                            <div
                              key={feature.id}
                              className="flex items-start gap-4 p-4 rounded-xl bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-950/30 dark:to-green-950/30 border border-emerald-100 dark:border-emerald-900/50"
                            >
                              <div className="p-2 rounded-lg bg-emerald-100 dark:bg-emerald-900/50 flex-shrink-0">
                                <Check className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                              </div>
                              <div>
                                <div className="font-semibold text-sm text-emerald-900 dark:text-emerald-100 mb-1">
                                  {feature.name}
                                </div>
                                <div className="text-xs text-emerald-700 dark:text-emerald-300">
                                  {feature.description}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {plan.name === "Free" && (
                        <div className="mt-8 p-6 bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-950/30 dark:to-green-950/30 rounded-xl border-2 border-emerald-200 dark:border-emerald-800">
                          <div className="flex items-center gap-3 text-emerald-800 dark:text-emerald-200">
                            <div className="p-2 rounded-lg bg-emerald-100 dark:bg-emerald-900/50">
                              <Shield className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                            </div>
                            <span className="font-semibold text-sm">
                              {t("no-credit-card-required")} • {t("cancel-anytime")}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </div>
            )
          })}
        </div>

        <SubscriptionDialog />

        {/* Footer */}
        <div className="mt-20 text-center">
          <div className="max-w-2xl mx-auto p-8 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-200 dark:border-slate-700 shadow-lg">
            <h3 className="text-2xl font-bold mb-4 text-slate-900 dark:text-slate-100">Still have questions?</h3>
            <p className="text-slate-600 dark:text-slate-400 mb-6">
              {t("have-questions-about-which-plan-is-right-for-you-our-team-is-happy-to-help")}
            </p>
            <Button
              asChild
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg"
            >
              <a href="mailto:<EMAIL>" className="flex items-center gap-2">
                <Mail className="w-4 h-4" />
                {t("contact-support")}
              </a>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
