"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  Search,
  Filter,
  LayoutDashboard,
} from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth, UserButton } from "@clerk/nextjs";
import axiosInstance from "@/config/axios";
import { GET_HELPDESK_TICKETS, GET_HELPDESK_CATEGORIES } from "@/utils/routes";
import { toast } from "sonner";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { TicketsCardView } from "./_components/tickets-card-view";
import { TicketsTableView } from "./_components/tickets-table-view";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogFooter, DialogTitle, DialogDescription } from "@/components/ui/dialog"; // Ajoute cet import

interface Ticket {
  id: string;
  subject: string;
  status: string;
  priority: string;
  category: string;
  createdAt: string;
  updatedAt: string;
  description: string;
  clientName: string;
  clientEmail: string;
  assignedAgent?: string;
  unreadMessages: number;
}

const Dashboard = () => {
  const t = useTranslations();
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<Ticket[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [categories, setCategories] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [view, setView] = useState<"card" | "table">("card");
  const router = useRouter();
  const { getToken } = useAuth();
  const ticketsPerPage = 5;

  const staticStatuses = ["pending", "started", "resolved", "cancelled"];

  // Fetch tickets
  useEffect(() => {
    const fetchTickets = async () => {
      try {
        setIsLoading(true);
        const token = await getToken();

        const response = await axiosInstance.get(GET_HELPDESK_TICKETS, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const ticketsData = response.data.data || response.data;
        setTickets(ticketsData);
        setFilteredTickets(ticketsData);
      } catch (error) {
        console.error("Error fetching tickets:", error);
        toast.error("Failed to load tickets");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTickets();
  }, [getToken]);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const token = await getToken();
        const response = await axiosInstance.get(GET_HELPDESK_CATEGORIES, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const data = response.data;

        // Cas 1 : tableau de chaînes
        if (Array.isArray(data) && typeof data[0] === "string") {
          setCategories(data);
        }
        // Cas 2 : tableau d’objets { name: string }
        else if (Array.isArray(data) && typeof data[0] === "object" && "name" in data[0]) {
          setCategories(data.map((c: any) => c.name));
        } else if (data.data && Array.isArray(data.data)) {
          // parfois la réponse est dans data.data
          setCategories(data.data.map((c: any) => c.name));
        } else {
          setCategories([]);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des catégories :", error);
        toast.error("Impossible de charger les catégories");
      }
    };

    fetchCategories();
  }, [getToken]);

  useEffect(() => {
    let filtered = tickets;

    if (searchTerm) {
      filtered = filtered.filter(
        (ticket) =>
          ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
          ticket.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter((ticket) => ticket.status === statusFilter);
    }

    if (categoryFilter !== "all") {
      filtered = filtered.filter((ticket) => ticket.category === categoryFilter);
    }

    setFilteredTickets(filtered);
    setCurrentPage(1);
  }, [searchTerm, statusFilter, categoryFilter, tickets]);

  // Ajoute ces états pour la gestion du dialog
  const [openDialog, setOpenDialog] = useState(false);
  const [ticketToDelete, setTicketToDelete] = useState<string | null>(null);

  // Modifie handleDeleteTicket pour ne faire que la suppression
  const confirmDeleteTicket = async () => {
    if (!ticketToDelete) return;
    try {
      const token = await getToken();

      await axiosInstance.delete(`${GET_HELPDESK_TICKETS}/${ticketToDelete}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setTickets(tickets.filter((t) => t.id !== ticketToDelete));
      toast.success("Ticket deleted successfully");
    } catch (error) {
      console.error("Error deleting ticket:", error);
      toast.error("Failed to delete ticket");
    } finally {
      setOpenDialog(false);
      setTicketToDelete(null);
    }
  };

  // Nouvelle fonction pour ouvrir le dialog
  const handleAskDeleteTicket = (ticketId: string) => {
    setTicketToDelete(ticketId);
    setOpenDialog(true);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      Pending: "default",
      Started: "secondary",
      Resolved: "outline",
      Cancelled: "destructive",
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || "default"}>
        {status}
      </Badge>
    );
  };

  const totalPages = Math.ceil(filteredTickets.length / ticketsPerPage);
  const startIndex = (currentPage - 1) * ticketsPerPage;
  const currentTickets = filteredTickets.slice(
    startIndex,
    startIndex + ticketsPerPage
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4 text-gray-600">Loading tickets...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Dialog de confirmation */}
      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmer la suppression</DialogTitle>
            <DialogDescription>
              Êtes-vous sûr de vouloir supprimer ce ticket ? Cette action est irréversible.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenDialog(false)}>
              Annuler
            </Button>
            <Button variant="destructive" onClick={confirmDeleteTicket}>
              Supprimer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm border-b">
          <nav className="border-b border-slate-200/60 dark:border-slate-700/60 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl py-4 px-6 flex justify-between items-center sticky top-0 z-50 shadow-sm">
            <Link href="/enterprise/dashboard" className="hover:opacity-90 transition-opacity">
              <Image src="/passisto_logo.png" alt="Passisto Logo" width={150} height={40} priority className="dark:invert" />
            </Link>
            <UserButton afterSignOutUrl="/auth/login">
              <UserButton.MenuItems>
                <UserButton.Action
                  label={t("dashboard")}
                  labelIcon={<LayoutDashboard size={16} />}
                  onClick={() => (window.location.href = "/enterprise/dashboard")}
                />
              </UserButton.MenuItems>
            </UserButton>
          </nav>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Mes Tickets</h2>
              <p className="text-gray-600">Gérez vos demandes de support</p>
            </div>
            <Link href="/support/create-ticket">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Nouveau Ticket
              </Button>
            </Link>
          </div>

          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Rechercher dans les tickets..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-48">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Filtrer par statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tous les statuts</SelectItem>
                      {staticStatuses.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-full sm:w-48">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="Filtrer par catégorie" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Toutes les catégories</SelectItem>
                      {Array.isArray(categories) &&
                        categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Tickets ({filteredTickets.length})</h3>
            <div className="flex gap-2">
              <Button variant={view === "card" ? "default" : "outline"} size="sm" onClick={() => setView("card")}>
                Cartes
              </Button>
              <Button variant={view === "table" ? "default" : "outline"} size="sm" onClick={() => setView("table")}>
                Tableau
              </Button>
            </div>
          </div>

          {view === "card" ? (
            <TicketsCardView
              tickets={currentTickets}
              onDeleteTicket={handleAskDeleteTicket}
              getStatusBadge={getStatusBadge}
            />
          ) : (
            <TicketsTableView
              tickets={currentTickets}
              onDeleteTicket={handleAskDeleteTicket}
              getStatusBadge={getStatusBadge}
            />
          )}

          {totalPages > 1 && (
            <div className="flex justify-center items-center gap-2 mt-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Précédent
              </Button>
              <span className="text-sm text-gray-600">
                Page {currentPage} sur {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                Suivant
              </Button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Dashboard;
