"use client"
import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ArrowLeft, Send, ImageIcon, Clock, User, MessageSquare, Edit, Trash2, Paperclip, Download } from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import Link from "next/link"
import { useAuth } from "@clerk/nextjs"
import axiosInstance from "@/config/axios"
import { GET_HELPDESK_TICKETS, GET_TICKET_MESSAGES, POST_TICKET_MESSAGE } from "@/utils/routes"
import { toast } from "sonner"
import Image from "next/image"

interface MessageAttachment {
  id: string
  name: string
  mimetype: string
  url: string
  size?: number
}

interface Message {
  id: string
  content: string
  author: string
  authorType: "client" | "agent"
  timestamp: string
  attachments?: MessageAttachment[]
  images?: MessageAttachment[]
}

interface Attachment {
  id: number
  name: string
  mimetype: string
  datas_fname: string
  datas: string
}

interface Ticket {
  id: string
  name: string
  description: string
  stage_id: [number, string]
  create_date: string
  user_id: [number, string] | false
  partner_name: string
  team_id: [number, string] | false
  creator_company_id: [number, string]
  saas_company_id: [number, string]
  messages?: Message[]
  attachments?: Attachment[]
}

const formatDate = (dateString: string) => new Date(dateString).toLocaleString("fr-FR")
const stripHtml = (html: string) => (html ? html.replace(/<[^>]*>/g, "") : "")
const getUpdateDate = (createDate: string) => {
  const created = new Date(createDate)
  const hoursToAdd = Math.floor(Math.random() * 48) + 1
  return new Date(created.getTime() + hoursToAdd * 60 * 60 * 1000).toISOString()
}

const isImageFile = (mimetype: string) => {
  return mimetype.startsWith("image/")
}

const formatFileSize = (bytes?: number) => {
  if (!bytes) return "Taille inconnue"
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + " " + sizes[i]
}

export default function TicketDetail() {
  const [ticket, setTicket] = useState<Ticket | null>(null)
  const [newMessage, setNewMessage] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isPageLoading, setIsPageLoading] = useState(true)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  const router = useRouter()
  const params = useParams()
  const { getToken } = useAuth()

  useEffect(() => {
    const fetchMessages = async (ticketId: string) => {
      try {
        const token = await getToken()
        const response = await axiosInstance.get(GET_TICKET_MESSAGES(ticketId), {
          headers: { Authorization: `Bearer ${token}` },
        })

        const messages = response.data.data || []
        const processedMessages = messages.map((msg: any) => {
          if (msg.attachments) {
            const images = msg.attachments.filter((att: any) => isImageFile(att.mimetype))
            const otherAttachments = msg.attachments.filter((att: any) => !isImageFile(att.mimetype))

            return {
              ...msg,
              images,
              attachments: otherAttachments,
            }
          }
          return msg
        })

        return processedMessages
      } catch (error) {
        console.error("Erreur lors du chargement des messages :", error)
        toast.error("Impossible de charger les messages")
        return []
      }
    }

    const fetchTicket = async () => {
      if (!params.id) return
      try {
        setIsPageLoading(true)
        const token = await getToken()
        if (!token) {
          router.push("/")
          return
        }

        const response = await axiosInstance.get(`${GET_HELPDESK_TICKETS}/${params.id}`, {
          headers: { Authorization: `Bearer ${token}` },
        })
        const apiTicket = response.data.data
        if (!apiTicket) {
          toast.error("Ticket non trouvé")
          return
        }

        const messages = await fetchMessages(apiTicket.id)
        setTicket({ ...apiTicket, messages })
      } catch (error: any) {
        console.error("Erreur lors du chargement du ticket :", error)
        if (error.response?.status === 404) toast.error("Ticket non trouvé")
        else toast.error("Erreur lors du chargement du ticket")
      } finally {
        setIsPageLoading(false)
      }
    }

    fetchTicket()
  }, [params.id, getToken, router])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0])
    }
  }

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() && !selectedFile) return
    if (!ticket) return

    setIsLoading(true)
    try {
      const token = await getToken()

      console.log("[v0] Sending message with data:", {
        ticketId: ticket.id,
        content: newMessage,
        hasFile: !!selectedFile,
        fileName: selectedFile?.name,
      })

      let response
      if (selectedFile) {
        // Use FormData for file uploads
        const formData = new FormData()
        const messageContent = newMessage.trim() || (selectedFile ? `Fichier partagé: ${selectedFile.name}` : "")
        formData.append("content", messageContent)
        formData.append("attachments", selectedFile)

        console.log("[v0] Using FormData for file upload with content:", messageContent)
        response = await axiosInstance.post(POST_TICKET_MESSAGE(ticket.id), formData, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "multipart/form-data",
          },
        })
      } else {
        // Use JSON for text-only messages
        const messageData = {
          content: newMessage.trim(),
          ticket_id: ticket.id,
        }

        console.log("[v0] Using JSON for text message:", messageData)
        response = await axiosInstance.post(POST_TICKET_MESSAGE(ticket.id), messageData, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        })
      }

      console.log("[v0] Message sent successfully:", response.data)

      const sentMessage: Message = response.data.data
      setTicket((prev) => prev && { ...prev, messages: [...(prev.messages || []), sentMessage] })
      setNewMessage("")
      setSelectedFile(null)
      toast.success("Message envoyé avec succès")
    } catch (error: any) {
      console.error("[v0] Erreur lors de l'envoi du message :", error)
      console.error("[v0] Error response:", error.response?.data)
      console.error("[v0] Error status:", error.response?.status)
      console.error("[v0] Error headers:", error.response?.headers)

      if (error.response?.status === 400) {
        toast.error(`Erreur de requête: ${error.response?.data?.message || "Données invalides"}`)
      } else {
        toast.error("Impossible d'envoyer le message")
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteTicket = async (ticketId: string) => {
    try {
      const token = await getToken()
      await axiosInstance.delete(`${GET_HELPDESK_TICKETS}/${ticketId}`, {
        headers: { Authorization: `Bearer ${token}` },
      })
      toast.success("Ticket supprimé avec succès")
      router.push("/support")
    } catch (error) {
      console.error("Erreur lors de la suppression du ticket :", error)
      toast.error("Erreur lors de la suppression du ticket")
    }
  }

  const handleDownloadAttachment = (attachment: Attachment) => {
    const link = document.createElement("a")
    link.href = `data:${attachment.mimetype};base64,${attachment.datas}`
    link.download = attachment.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const getStatusBadge = (stage_id: [number, string] | undefined) => {
    if (!stage_id || !Array.isArray(stage_id)) return <Badge variant="secondary">Inconnu</Badge>
    const statusName = stage_id[1].toLowerCase()
    const statusConfig = {
      new: { variant: "default" as const, text: stage_id[1] },
      awaiting: { variant: "secondary" as const, text: stage_id[1] },
      "in progress": { variant: "secondary" as const, text: stage_id[1] },
      solved: { variant: "outline" as const, text: stage_id[1] },
      closed: { variant: "destructive" as const, text: stage_id[1] },
    }
    const config = statusConfig[statusName as keyof typeof statusConfig] || statusConfig["new"]
    return <Badge variant={config.variant}>{config.text}</Badge>
  }

  const sortMessages = (messages: Message[]) => {
    return messages.filter(Boolean).sort((a, b) => {
      const timeA = new Date(a.timestamp || 0).getTime()
      const timeB = new Date(b.timestamp || 0).getTime()
      if (timeA !== timeB) return timeA - timeB
      const idA = Number.parseInt(a.id) || 0
      const idB = Number.parseInt(b.id) || 0
      return idA - idB
    })
  }

  const renderMessageContent = (msg: Message) => {
    const authorType = msg?.authorType || "client"
    const author = msg?.author || "Inconnu"
    const timestamp = msg?.timestamp || new Date().toISOString()
    const content = msg?.content || ""

    if (msg.images && msg.images.length > 0) {
      console.log("[v0] Message images data:", msg.images)
      msg.images.forEach((image, index) => {
        console.log(`[v0] Image ${index}:`, {
          name: image.name,
          mimetype: image.mimetype,
          data: (image as any).data,
          dataType: typeof (image as any).data,
          dataLength: (image as any).data?.length,
          url: image.url,
          urlType: typeof image.url,
        })
      })
    }

    return (
      <div
        key={msg.id || Math.random()}
        className={`flex gap-4 mb-6 ${authorType === "client" ? "flex-row-reverse" : ""}`}
      >
        <Avatar className="h-10 w-10 flex-shrink-0">
          <AvatarImage
            src={`/ceholder-svg-key-srxi5-height-40-width-40-text-.png?key=srxi5&height=40&width=40&text=${author.charAt(0)}`}
          />
          <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
            {author.charAt(0)}
          </AvatarFallback>
        </Avatar>

        <div className={`flex-1 max-w-[70%] ${authorType === "client" ? "text-right" : ""}`}>
          <div className={`flex items-center gap-2 mb-2 ${authorType === "client" ? "justify-end" : ""}`}>
            <span className="font-semibold text-sm text-gray-900">{author}</span>
            <Badge
              variant={authorType === "agent" ? "default" : "secondary"}
              className={`text-xs ${authorType === "agent" ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"}`}
            >
              {authorType === "agent" ? "Support" : "Client"}
            </Badge>
            <span className="text-xs text-gray-500">{formatDate(timestamp)}</span>
          </div>

          <div
            className={`rounded-2xl p-4 shadow-sm ${
              authorType === "client"
                ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white ml-8"
                : "bg-white border border-gray-200 text-gray-900 mr-8"
            }`}
          >
            {content && <p className="text-sm leading-relaxed mb-3">{content}</p>}

            {msg.images && msg.images.length > 0 && (
              <div className="space-y-2 mb-3">
                <div className="flex items-center gap-2 mb-2">
                  <ImageIcon className="h-4 w-4" />
                  <span className="text-xs font-medium">Pièces jointes ({msg.images.length})</span>
                </div>
                {msg.images.map((image, index) => {
                  const imageData = (image as any).data || image.url
                  let downloadUrl = "/placeholder.svg"

                  if (imageData) {
                    if (imageData.startsWith("data:")) {
                      downloadUrl = imageData
                    } else if (imageData.startsWith("http")) {
                      downloadUrl = imageData
                    } else if (imageData.length > 0) {
                      downloadUrl = `data:${image.mimetype || "image/jpeg"};base64,${imageData}`
                    }
                  }

                  const handleDownload = () => {
                    const link = document.createElement("a")
                    link.href = downloadUrl
                    link.download = image.name || `image-${index + 1}`
                    document.body.appendChild(link)
                    link.click()
                    document.body.removeChild(link)
                  }

                  return (
                    <div
                      key={index}
                      className={`flex items-center gap-3 p-3 rounded-lg border ${
                        authorType === "client"
                          ? "bg-blue-400 bg-opacity-20 border-blue-300"
                          : "bg-gray-50 border-gray-200"
                      }`}
                    >
                      <div
                        className={`p-2 rounded-lg ${
                          authorType === "client" ? "bg-blue-300 bg-opacity-30" : "bg-blue-100"
                        }`}
                      >
                        <ImageIcon
                          className={`h-4 w-4 ${authorType === "client" ? "text-blue-100" : "text-blue-600"}`}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p
                          className={`text-sm font-medium truncate ${
                            authorType === "client" ? "text-blue-50" : "text-gray-900"
                          }`}
                        >
                          {image.name || `image-${index + 1}.${image.mimetype?.split("/")[1] || "jpg"}`}
                        </p>
                        <p className={`text-xs ${authorType === "client" ? "text-blue-200" : "text-gray-500"}`}>
                          {image.mimetype || "image/jpeg"}
                        </p>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={handleDownload}
                        className={`h-8 w-8 p-0 ${
                          authorType === "client"
                            ? "hover:bg-blue-400 hover:bg-opacity-30 text-blue-100"
                            : "hover:bg-gray-200 text-gray-600"
                        }`}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  )
                })}
              </div>
            )}

            {msg.attachments && msg.attachments.length > 0 && (
              <div className="space-y-2">
                {msg.attachments.map((attachment, index) => (
                  <div
                    key={index}
                    className={`flex items-center gap-2 p-2 rounded-lg ${
                      authorType === "client" ? "bg-blue-400 bg-opacity-30" : "bg-gray-50"
                    }`}
                  >
                    <Paperclip className="h-4 w-4" />
                    <span className="text-xs font-medium truncate flex-1">{attachment.name}</span>
                    <span className="text-xs opacity-75">{formatFileSize(attachment.size)}</span>
                    <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                      <Download className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  if (isPageLoading)
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 border-t-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement du ticket...</p>
        </div>
      </div>
    )

  if (!ticket)
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Ticket non trouvé</h2>
          <p className="text-gray-600 mb-6">Le ticket demandé n'existe pas ou a été supprimé.</p>
          <Link href="/support">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Retour aux tickets
            </Button>
          </Link>
        </div>
      </div>
    )

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/support">
              <Button variant="ghost" size="sm" className="hover:bg-gray-100">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Retour
              </Button>
            </Link>
            <div className="ml-4">
              <h1 className="text-xl font-bold text-gray-900">Ticket #{ticket.id}</h1>
              <p className="text-sm text-gray-500">{ticket.name}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">{getStatusBadge(ticket.stage_id)}</div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-8 grid lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <Card className="shadow-sm border-0 bg-white">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl text-gray-900">{ticket.name}</CardTitle>
                  <CardDescription className="text-gray-600 mt-1">
                    Créé le {formatDate(ticket.create_date)} • Mis à jour le{" "}
                    {formatDate(getUpdateDate(ticket.create_date))}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-4 rounded-xl border">
                <p className="text-gray-800 leading-relaxed">{stripHtml(ticket.description)}</p>
              </div>
            </CardContent>
          </Card>

          {(ticket.attachments?.length || 0) > 0 && (
            <Card className="shadow-sm border-0 bg-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-900">
                  <ImageIcon className="h-5 w-5 text-blue-600" />
                  Pièces jointes ({ticket.attachments?.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {ticket.attachments?.map((att) => (
                    <div
                      key={att.id}
                      className="border border-gray-200 rounded-xl p-4 bg-gradient-to-br from-gray-50 to-white hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Paperclip className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-gray-900 truncate">{att.name}</p>
                          <p className="text-sm text-gray-500">{att.mimetype}</p>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => handleDownloadAttachment(att)}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          <Card className="shadow-sm border-0 bg-white">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-900">
                <MessageSquare className="h-5 w-5 text-green-600" />
                Conversation ({ticket.messages?.length || 0} messages)
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6">
              <div className="max-h-96 overflow-y-auto pr-2 space-y-1 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                {sortMessages(ticket.messages || []).map((msg) => renderMessageContent(msg))}
              </div>

              {ticket.stage_id && (
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <form onSubmit={handleSendMessage} className="space-y-4">
                    <Textarea
                      placeholder="Tapez votre réponse..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      className="min-h-[120px] border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                      disabled={isLoading}
                    />

                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-3">
                        <input
                          type="file"
                          id="file-upload"
                          className="hidden"
                          onChange={handleFileChange}
                          accept="image/*,.pdf,.doc,.docx,.txt"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => document.getElementById("file-upload")?.click()}
                          className="border-gray-300 hover:bg-gray-50"
                        >
                          <Paperclip className="h-4 w-4 mr-2" />
                          Joindre un fichier
                        </Button>

                        {selectedFile && (
                          <div className="flex items-center gap-2 bg-blue-50 px-3 py-1 rounded-full">
                            <span className="text-sm text-blue-700 font-medium truncate max-w-32">
                              {selectedFile.name}
                            </span>
                            <button
                              type="button"
                              onClick={() => setSelectedFile(null)}
                              className="text-blue-600 hover:text-blue-800"
                            >
                              ×
                            </button>
                          </div>
                        )}
                      </div>

                      <Button
                        type="submit"
                        disabled={isLoading || (!newMessage.trim() && !selectedFile)}
                        className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300"
                      >
                        <Send className="mr-2 h-4 w-4" />
                        {isLoading ? "Envoi..." : "Envoyer"}
                      </Button>
                    </div>
                  </form>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Détails du ticket</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {ticket.partner_name && (
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium">{ticket.partner_name}</p>
                    <p className="text-sm text-gray-500">Client</p>
                  </div>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Créé le</p>
                  <p className="font-medium">{formatDate(ticket.create_date)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {ticket.stage_id && ["pending"].includes(ticket.stage_id[1].toLowerCase()) && (
            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <Link href={`/support/ticket/${ticket.id}/edit`}>
                  <Button variant="outline" className="w-full bg-transparent">
                    <Edit className="mr-2 h-4 w-4" />
                    Modifier le ticket
                  </Button>
                </Link>
                <Button
                  variant="outline"
                  className="w-full text-red-600 bg-transparent"
                  onClick={() => handleDeleteTicket(ticket.id)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Supprimer le ticket
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {previewImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setPreviewImage(null)}
        >
          <div className="relative max-w-4xl max-h-full">
            <Image
              src={previewImage || "/placeholder.svg"}
              alt="Preview"
              width={800}
              height={600}
              className="max-w-full max-h-full object-contain rounded-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.src = "/placeholder.svg?height=600&width=800&text=Image non disponible"
              }}
            />
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-2 right-2 bg-black bg-opacity-50 text-white hover:bg-opacity-75"
              onClick={() => setPreviewImage(null)}
            >
              ×
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
