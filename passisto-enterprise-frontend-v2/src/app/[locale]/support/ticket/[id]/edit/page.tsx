"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ArrowLeft, Upload, X, FileText, FileImage } from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import Link from "next/link"
import { useAuth } from "@clerk/nextjs"
import axiosInstance from "@/config/axios"
import {
  GET_HELPDESK_TICKETS,
  GET_HELPDESK_CATEGORIES,
  UPDATE_HELPDESK_TICKET,
} from "@/utils/routes"
import { toast } from "sonner"

interface Attachment {
  id: number
  name: string
  mimetype: string
  datas_fname: string
  datas: string // base64 content
}

interface Ticket {
  id: string
  name: string
  description: string
  stage_id: [number, string]
  create_date: string
  user_id: [number, string] | false
  partner_id: [number, string] | false
  team_id: [number, string] | false
  creator_company_id: [number, string]
  saas_company_id: [number, string]
  category_id?: [number, string] | null
  attachments?: Attachment[]
}

interface HelpdeskCategory {
  id: number
  name: string
}

// Nettoyage HTML
const stripHtml = (html: string) => {
  const tmp = document.createElement("div")
  tmp.innerHTML = html
  return tmp.textContent || tmp.innerText || ""
}

export default function EditTicket() {
  const [isLoading, setIsLoading] = useState(false)
  const [isPageLoading, setIsPageLoading] = useState(true)

  const [ticket, setTicket] = useState<Ticket | null>(null)
  const [categories, setCategories] = useState<HelpdeskCategory[]>([])

  // fichiers EXISTANTS (issus d’Odoo) qu’on décide de garder
  const [existingAttachments, setExistingAttachments] = useState<Attachment[]>([])
  // nouveaux fichiers ajoutés dans le formulaire
  const [attachments, setAttachments] = useState<File[]>([])

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category_id: "",
  })

  const router = useRouter()
  const params = useParams()
  const { getToken } = useAuth()

  // --- Helpers pour UI fichiers ---
  const isImageFile = (file: File) => file.type.startsWith("image/")
  const getImagePreview = (file: File) => URL.createObjectURL(file)
  const getFileIcon = (file: File | { type: string }) =>
    file.type.startsWith("image/") ? (
      <FileImage className="h-4 w-4 text-blue-600" />
    ) : (
      <FileText className="h-4 w-4 text-gray-600" />
    )
  const formatFileSize = (size: number): string => {
    const units = ["B", "KB", "MB", "GB", "TB"]
    let i = 0
    let s = size
    while (s >= 1024 && i < units.length - 1) {
      s /= 1024
      i++
    }
    return s.toFixed(2) + " " + units[i]
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files) return

    const maxSize = 10 * 1024 * 1024 // 10MB
    const validTypes = [
      "image/png",
      "image/jpeg",
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ]

    const newOnes = Array.from(files).filter(
      (f) => f.size <= maxSize && validTypes.includes(f.type)
    )
    setAttachments((prev) => [...prev, ...newOnes])
  }

  const removeAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index))
  }

  const removeExistingAttachment = (id: number) => {
    // On l’enlève de la liste à conserver (il sera donc supprimé côté back si tu implémentes la logique)
    setExistingAttachments((prev) => prev.filter((a) => a.id !== id))
  }

  // --- Fetch ticket details ---
  useEffect(() => {
    const fetchTicket = async () => {
      if (!params.id) return

      try {
        setIsPageLoading(true)
        const token = await getToken()

        if (!token) {
          router.push("/")
          return
        }

        const response = await axiosInstance.get(`${GET_HELPDESK_TICKETS}/${params.id}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })

        const apiTicket: Ticket = response.data.data

        if (!apiTicket) {
          toast.error("Ticket non trouvé")
          router.push("/support")
          return
        }

        apiTicket.attachments = apiTicket.attachments || []

        setTicket(apiTicket)
        setExistingAttachments(apiTicket.attachments)

        setFormData({
          name: apiTicket.name || "",
          description: stripHtml(apiTicket.description || ""),
          category_id: apiTicket.category_id?.[0]?.toString() || "",
        })
      } catch (error: any) {
        console.error("Error fetching ticket:", error)
        if (error.response?.status === 404) {
          toast.error("Ticket non trouvé")
        } else {
          toast.error("Erreur lors du chargement du ticket")
        }
        router.push("/support")
      } finally {
        setIsPageLoading(false)
      }
    }

    fetchTicket()
  }, [params.id, getToken, router])

  // --- Fetch categories ---
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const token = await getToken()

        const response = await axiosInstance.get(GET_HELPDESK_CATEGORIES, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        })

        const categoriesData = response.data.data || response.data
        setCategories(categoriesData)
      } catch (error) {
        console.error("Error fetching categories:", error)
        toast.error("Erreur lors du chargement des catégories")
      }
    }

    fetchCategories()
  }, [getToken])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!ticket) return

    setIsLoading(true)

    try {
      const token = await getToken()

      // On passe en multipart/form-data pour envoyer les nouveaux fichiers
      const fd = new FormData()
      fd.append("name", formData.name)
      fd.append("description", formData.description)
      fd.append("category_id", formData.category_id)

      // IDs des pièces jointes existantes à conserver (celles qui restent dans existingAttachments)
      fd.append(
        "existing_file_ids",
        JSON.stringify(existingAttachments.map((a) => a.id))
      )

      // nouveaux fichiers
      attachments.forEach((file) => {
        fd.append("files", file)
      })

      await axiosInstance.put(UPDATE_HELPDESK_TICKET(ticket.id), fd, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "multipart/form-data",
        },
      })

      toast.success("Ticket mis à jour avec succès")
      router.push(`/support/ticket/${params.id}`)
    } catch (error) {
      console.error("Error updating ticket:", error)
      toast.error("Erreur lors de la mise à jour du ticket")
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  if (isPageLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Chargement...</h2>
        </div>
      </div>
    )
  }

  if (!ticket) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900">Ticket non trouvé</h2>
          <Link href="/support">
          <Button className="mt-4">Retour au support</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link href={`/support/ticket/${params.id}`}>
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour
              </Button>
            </Link>
            <h1 className="text-xl font-semibold text-gray-900 ml-4">
              Modifier le ticket #{params.id}
            </h1>
          </div>
        </div>
      </header>

      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle>Modifier le ticket</CardTitle>
            <CardDescription>
              Vous pouvez modifier ce ticket tant qu'il n'a pas été pris en charge par un agent.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Sujet */}
              <div className="space-y-2">
                <Label htmlFor="name">Sujet *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleChange("name", e.target.value)}
                  required
                />
              </div>

              {/* Catégorie */}
              <div className="space-y-2">
                <Label htmlFor="category">Catégorie *</Label>
                <Select
                  value={formData.category_id}
                  onValueChange={(value) => handleChange("category_id", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner une catégorie" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description du problème *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleChange("description", e.target.value)}
                  className="min-h-[120px]"
                  required
                  placeholder="Décrivez votre problème en détail..."
                />
              </div>

              {/* Pièces jointes existantes */}
              {existingAttachments.length > 0 && (
                <div className="space-y-2">
                  <Label>Pièces jointes existantes</Label>
                  <div className="space-y-2">
                    {existingAttachments.map((file) => (
                      <div key={file.id} className="border rounded-lg p-2 bg-gray-50">
                        {file.mimetype?.startsWith('image/') ? (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                {getFileIcon({ type: file.mimetype })}
                                <span className="text-sm font-medium truncate">{file.name}</span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeExistingAttachment(file.id)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                            <div className="flex justify-center">
                              <img
                                src={`data:${file.mimetype};base64,${file.datas}`}
                                alt={file.name}
                                className="max-w-full h-20 object-contain border rounded"
                              />
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 flex-1 min-w-0">
                              {getFileIcon({ type: file.mimetype })}
                              <a
                                href={`data:${file.mimetype};base64,${file.datas}`}
                                download={file.name}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm font-medium truncate text-blue-600 underline"
                              >
                                {file.name}
                              </a>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeExistingAttachment(file.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Ajout de nouvelles pièces jointes */}
              <div className="space-y-2">
                <Label htmlFor="attachments">Ajouter de nouvelles pièces jointes (optionnel)</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                  <Upload className="mx-auto h-8 w-8 text-gray-400" />
                  <div className="mt-2">
                    <Label htmlFor="file-upload" className="cursor-pointer">
                      <span className="block text-sm font-medium text-gray-900">
                        Cliquez pour télécharger
                      </span>
                      <span className="block text-xs text-gray-500">
                        PNG, JPG, PDF jusqu'à 10MB
                      </span>
                    </Label>
                    <Input
                      id="file-upload"
                      type="file"
                      className="hidden"
                      multiple
                      accept=".png,.jpg,.jpeg,.pdf,.doc,.docx"
                      onChange={handleFileUpload}
                    />
                  </div>
                </div>

                {attachments.length > 0 && (
                  <div className="mt-3 space-y-2">
                    <Label className="text-sm">Nouveaux fichiers sélectionnés :</Label>
                    {attachments.map((file, index) => (
                      <div key={index} className="border rounded-lg p-2 bg-gray-50">
                        {isImageFile(file) ? (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                {getFileIcon(file)}
                                <span className="text-sm font-medium truncate">{file.name}</span>
                                <span className="text-xs text-gray-500">
                                  ({formatFileSize(file.size)})
                                </span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeAttachment(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                            <div className="flex justify-center">
                              <img
                                src={getImagePreview(file)}
                                alt={file.name}
                                className="max-w-full h-20 object-contain border rounded"
                              />
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 flex-1 min-w-0">
                              {getFileIcon(file)}
                              <a
                                href={URL.createObjectURL(file)}
                                download={file.name}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm font-medium truncate text-blue-600 underline"
                              >
                                {file.name}
                              </a>
                              <span className="text-xs text-gray-500 flex-shrink-0">
                                ({formatFileSize(file.size)})
                              </span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeAttachment(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button type="submit" disabled={isLoading} className="flex-1">
                  {isLoading ? "Mise à jour..." : "Mettre à jour le ticket"}
                </Button>
                <Link href={`/support/ticket/${params.id}`}>
                  <Button type="button" variant="outline" className="w-full sm:w-auto bg-transparent">
                    Annuler
                  </Button>
                </Link>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}


