"use client"

import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit, Trash2, ChevronDown } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

interface Ticket {
  id: string;
  subject: string;
  status: string;
  priority: string;
  category: string;
  createdAt: string;
  updatedAt: string;
  description: string;
  // clientName: string;
  // clientEmail: string;
  // assignedAgent?: string;
  // unreadMessages: number;
}

interface TicketsCardViewProps {
  tickets: Ticket[]
  onDeleteTicket: (ticketId: string) => void
  getStatusBadge: (status: string) => JSX.Element
}

export function TicketsCardView({ tickets, onDeleteTicket, getStatusBadge }: TicketsCardViewProps) {
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set())

  const toggleExpanded = (ticketId: string) => {
    const newExpanded = new Set(expandedCards)
    if (newExpanded.has(ticketId)) {
      newExpanded.delete(ticketId)
    } else {
      newExpanded.add(ticketId)
    }
    setExpandedCards(newExpanded)
  }

  const getDescriptionPreview = (description: string, ticketId: string) => {
    const isExpanded = expandedCards.has(ticketId)
    const maxLength = 150
    
    if (description.length <= maxLength) {
      return (
        <div
          className="text-gray-600 text-sm mb-3"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )
    }

    if (isExpanded) {
      return (
        <div className="text-gray-600 text-sm mb-3">
          <div dangerouslySetInnerHTML={{ __html: description }} />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleExpanded(ticketId)}
            className="mt-2 p-0 h-auto text-blue-600 hover:text-blue-700"
          >
            Voir moins
            <ChevronDown className="h-4 w-4 ml-1 rotate-180" />
          </Button>
        </div>
      )
    }

    return (
      <div className="text-gray-600 text-sm mb-3">
        <div 
          className="line-clamp-2"
          dangerouslySetInnerHTML={{ __html: description.substring(0, maxLength) + '...' }} 
        />
        <div className="flex gap-2 mt-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleExpanded(ticketId)}
            className="p-0 h-auto text-blue-600 hover:text-blue-700"
          >
            Voir plus
            <ChevronDown className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {tickets.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <p className="text-gray-500">Aucun ticket trouvé</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        tickets.map((ticket) => (
          <Card
            key={ticket.id}
            className="hover:shadow-md transition-shadow"
          >
            <CardContent className="pt-6 pb-4">
              <div className="flex flex-col">
                {/* Header avec titre et badges */}
                <div className="flex flex-col sm:flex-row justify-between items-start gap-4 mb-4">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-lg truncate">
                        {ticket.subject}
                      </h3>
                    </div>
                    {getDescriptionPreview(ticket.description, ticket.id)}
                    <div className="flex flex-wrap items-center gap-2 text-sm text-gray-500">
                      <span>
                        Créé le{" "}
                        {new Date(ticket.createdAt).toLocaleDateString(
                          "fr-FR"
                        )}
                      </span>
                      <span>•</span>
                      <span>
                        Mis à jour le{" "}
                        {new Date(ticket.updatedAt).toLocaleDateString(
                          "fr-FR"
                        )}
                      </span>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    {getStatusBadge(ticket.status)}
                    <Badge className="bg-purple-100 text-purple-800">
                      {ticket.category}
                    </Badge>
                  </div>
                </div>
                
                {/* Boutons d'action en bas à droite */}
                <div className="flex justify-end gap-2 mt-auto">
                  <Link href={`/support/ticket/${ticket.id}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-1" />
                      Voir
                    </Button>
                  </Link>
                  {(ticket.status === "pending" ) && (
                    <>
                      <Link href={`/support/ticket/${ticket.id}/edit`}>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-1" />
                          Modifier
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDeleteTicket(ticket.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Supprimer
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  )
}




