"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Eye, Edit, Trash2 } from "lucide-react"
import Link from "next/link"

interface Ticket {
  id: string;
  subject: string;
  status: string;
  priority: string;
  category: string;
  createdAt: string;
  updatedAt: string;
  description: string;
  clientName: string;
  clientEmail: string;
  assignedAgent?: string;
  unreadMessages: number;
}

interface TicketsTableViewProps {
  tickets: Ticket[]
  onDeleteTicket: (ticketId: string) => void
  getStatusBadge: (status: string) => JSX.Element
}

export function TicketsTableView({ tickets, onDeleteTicket, getStatusBadge }: TicketsTableViewProps) {
  return (
    <div className="border rounded-md">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Sujet</TableHead>
            <TableHead>Catégorie</TableHead>
            <TableHead>Date de création</TableHead>
            <TableHead>Date de changement</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead className="w-[150px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tickets.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                Aucun ticket trouvé
              </TableCell>
            </TableRow>
          ) : (
            tickets.map((ticket) => (
              <TableRow key={ticket.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <Link href={`/support/ticket/${ticket.id}`}>
                      <span className="hover:underline text-blue-600 truncate">
                        {ticket.subject}
                      </span>
                    </Link>
                    {/* {ticket.unreadMessages > 0 && (
                      <Badge variant="destructive" className="text-xs">
                        {ticket.unreadMessages}
                      </Badge>
                    )} */}
                  </div>
                  {/* <p className="text-xs text-muted-foreground">#{ticket.id}</p> */}
                </TableCell>
                <TableCell>
                  <Badge className="bg-purple-100 text-purple-800">
                    {ticket.category}
                  </Badge>
                </TableCell>
                <TableCell>
                  {new Date(ticket.createdAt).toLocaleDateString("fr-FR")}
                </TableCell>
                <TableCell>
                  {new Date(ticket.updatedAt).toLocaleDateString("fr-FR")}
                </TableCell>
                <TableCell>
                  {getStatusBadge(ticket.status)}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Link href={`/support/ticket/${ticket.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    {(ticket.status === "pending" ) && (
                      <>
                        <Link href={`/support/ticket/${ticket.id}/edit`}>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDeleteTicket(ticket.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}

