"use client";

import type React from "react";
import { useState, useEffect } from "react";
import { useAuth, useUser } from "@clerk/nextjs";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import axiosInstance from "@/config/axios";
import {
  CREATE_HELPDESK_TICKET,
  GET_HELPDESK_CATEGORIES,
} from "@/utils/routes";
import { toast } from "sonner";
import { Upload, X, FileText, FileImage } from "lucide-react";

interface HelpdeskCategory {
  id: string;
  name: string;
}

export default function CreateTicket() {
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<HelpdeskCategory[]>([]);
  const { getToken } = useAuth();
  const { user } = useUser();
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState<string>("");

  // Etats pour les inputs
  const [subject, setSubject] = useState("");
  const [description, setDescription] = useState("");
  const [name, setName] = useState(user?.fullName || "");
  const [email, setEmail] = useState(user?.primaryEmailAddress?.emailAddress || "");

  const [attachments, setAttachments] = useState<File[]>([]);

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const newAttachments = Array.from(files).filter((file) => {
        const maxSize = 10 * 1024 * 1024; // 10MB
        const validTypes = [
          "image/png",
          "image/jpeg",
          "application/pdf",
          "application/msword",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ];
        return file.size <= maxSize && validTypes.includes(file.type);
      });
      setAttachments((prevAttachments) => [...prevAttachments, ...newAttachments]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments((prevAttachments) =>
      prevAttachments.filter((_, i) => i !== index)
    );
  };

  const formatFileSize = (size: number): string => {
    const units = ["B", "KB", "MB", "GB", "TB"];
    let i = 0;
    while (size >= 1024 && i < units.length - 1) {
      size /= 1024;
      i++;
    }
    return size.toFixed(2) + " " + units[i];
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <FileImage className="h-4 w-4 text-blue-600" />
    }
    return <FileText className="h-4 w-4 text-gray-600" />
  }

  const isImageFile = (file: File) => {
    return file.type.startsWith('image/')
  }

  const getImagePreview = (file: File) => {
    return URL.createObjectURL(file)
  }

  // Récupération catégories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const token = await getToken();

        const response = await axiosInstance.get(GET_HELPDESK_CATEGORIES, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        const categoriesData = response.data.data || response.data;
        setCategories(categoriesData);
      } catch (error) {
        console.error("Error fetching categories:", error);
        toast.error("Failed to load categories");
      }
    };

    fetchCategories();
  }, [getToken]);

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  setIsLoading(true);

  try {
    const token = await getToken();

    const name = user?.fullName || "";
    const email = user?.primaryEmailAddress?.emailAddress || "";
    const subject = (document.getElementById("subject") as HTMLInputElement)?.value || "";
    const description = (document.getElementById("description") as HTMLTextAreaElement)?.value || "";

    if (!name || !email || !subject || !description || !selectedCategory) {
      toast.error("Please fill in all required fields");
      setIsLoading(false);
      return;
    }

    const formData = new FormData();
    formData.append('name', subject);
    formData.append('description', description);
    formData.append('partner_name', name);
    formData.append('partner_email', email);
    formData.append('category_id', selectedCategory);

    attachments.forEach((file) => {
      formData.append("files", file);
    });

    console.log("Sending formData:");
    for (const [key, value] of formData.entries()) {
      console.log(`${key}:`, value);
    }

    const response = await axiosInstance.post(
      CREATE_HELPDESK_TICKET,
      formData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "multipart/form-data",
        },
      }
    );

    console.log("✅ Ticket created successfully:", response.data);
    toast.success("Ticket created successfully!");
    router.push("/support");

  } catch (error: any) {
    console.error("❌ Error creating ticket:", error);

    if (error.response?.data?.error) {
      toast.error(`Server error: ${error.response.data.error}`);
    } else {
      toast.error("Failed to create ticket");
    }
  } finally {
    setIsLoading(false);
  }
};


  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link href="/support">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
            </Link>
            <h1 className="text-xl font-semibold text-gray-900 ml-4">New Ticket</h1>
          </div>
        </div>
      </header>

      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Card>
          <CardHeader>
            <CardTitle>Create a new ticket</CardTitle>
            <CardDescription>
              Describe your problem in detail so our team can help you quickly.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="subject">Subject *</Label>
                <Input
                  id="subject"
                  placeholder="Summary of your problem"
                  required
                  name="subject"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  required
                  value={selectedCategory}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Problem description *</Label>
                <Textarea
                  id="description"
                  placeholder="Describe your problem in detail."
                  className="min-h-[120px]"
                  required
                  name="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="attachments">Pièces jointes (optionnel)</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                  <Upload className="mx-auto h-8 w-8 text-gray-400" />
                  <div className="mt-2">
                    <Label htmlFor="file-upload" className="cursor-pointer">
                      <span className="block text-sm font-medium text-gray-900">
                        Cliquez pour télécharger
                      </span>
                      <span className="block text-xs text-gray-500">
                        PNG, JPG, PDF jusqu'à 10MB
                      </span>
                    </Label>
                    <Input
                      id="file-upload"
                      type="file"
                      className="hidden"
                      multiple
                      accept=".png,.jpg,.jpeg,.pdf,.doc,.docx"
                      onChange={handleFileUpload}
                    />
                  </div>
                </div>

                {attachments.length > 0 && (
                  <div className="mt-3 space-y-2">
                    <Label className="text-sm">Fichiers sélectionnés :</Label>
                    {attachments.map((file, index) => (
                      <div key={index} className="border rounded-lg p-2 bg-gray-50">
                        {isImageFile(file) ? (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                {getFileIcon(file)}
                                <span className="text-sm font-medium truncate">{file.name}</span>
                                <span className="text-xs text-gray-500">
                                  ({formatFileSize(file.size)})
                                </span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeAttachment(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                            <div className="flex justify-center">
                              <img
                                src={getImagePreview(file)}
                                alt={file.name}
                                className="max-w-full h-20 object-contain border rounded"
                              />
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 flex-1 min-w-0">
                              {getFileIcon(file)}
                              <span className="text-sm font-medium truncate">{file.name}</span>
                              <span className="text-xs text-gray-500 flex-shrink-0">
                                ({formatFileSize(file.size)})
                              </span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeAttachment(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <Button type="submit" disabled={isLoading} className="flex-1">
                  {isLoading ? "Creating..." : "Create ticket"}
                </Button>
                <Link href="/support">
                  <Button type="button" variant="outline" className="w-full sm:w-auto">
                    Cancel
                  </Button>
                </Link>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}


