"use client"
import * as React from "react"
import { use<PERSON>ara<PERSON>, useSearchPara<PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { JobOfferSection } from "../../_components/job-offer-section"
import { InterviewPhaseConfigurationSection } from "../../_components/interview-phase-configuration"
import { InterviewTemplateSection } from "../../_components/interview-template-section"
import { CvUploadSection } from "../../_components/cv-upload-section-with-datatable"
import { PhaseCandidatesSectionConnected } from "../../_components/phase-candidates-section-connected"
import { ProjectOverviewSection } from "../../_components/project-overview-section"
import { Button } from "@/components/ui/button"
import { WorkflowStepper } from "../../_components/workflow-stepper"

export default function ProjectPage() {
  const params = useParams()
  const projectId = params.id as string
  const router = useRouter()
  const searchParams = useSearchParams()

  // State to hold configured phases
  const [configuredPhases, setConfiguredPhases] = React.useState<any[]>([])

  // Define the workflow steps
  const steps = [
    { id: "job-offer", title: "Define Job Offer" },
    { id: "interview-phases", title: "Configure Interview Phases" },
    { id: "interview-template", title: "Choose Interview Template" },
    { id: "cv-upload", title: "Upload Candidate CVs" },
    { id: "candidates", title: "Manage Candidates" },
    { id: "project-overview", title: "Project Overview" },
  ]

  // Get current step from URL or default to the first step
  const currentStepId = searchParams.get("step") || steps[0].id
  const currentStepIndex = steps.findIndex((step) => step.id === currentStepId)
  const currentStep = currentStepIndex !== -1 ? currentStepIndex : 0

  const handleNextStep = () => {
    const nextStepIndex = currentStep + 1
    if (nextStepIndex < steps.length) {
      router.push(`${projectId}?step=${steps[nextStepIndex].id}`)
    }
  }

  const handlePreviousStep = () => {
    const prevStepIndex = currentStep - 1
    if (prevStepIndex >= 0) {
      router.push(`${projectId}?step=${steps[prevStepIndex].id}`)
    }
  }

  const handlePhasesConfigured = (phases: any[]) => {
    setConfiguredPhases(phases)
  }

  const renderSection = () => {
    switch (steps[currentStep].id) {
      case "job-offer":
        return <JobOfferSection projectId={projectId} onNext={handleNextStep} />
      case "interview-phases":
        return (
          <InterviewPhaseConfigurationSection 
            projectId={projectId} 
            onNext={handleNextStep}
            onPhasesConfigured={handlePhasesConfigured}
          />
        )
      case "interview-template":
        return (
          <InterviewTemplateSection 
            projectId={projectId} 
            phases={configuredPhases}
            onNext={handleNextStep} 
          />
        )
      case "cv-upload":
        return <CvUploadSection projectId={projectId} onNext={handleNextStep} />
      case "candidates":
        // Always use the new phase-based UI for managing candidates
        return <PhaseCandidatesSectionConnected projectId={projectId} />
      case "project-overview":
        return <ProjectOverviewSection projectId={projectId} />
      default:
        return <JobOfferSection projectId={projectId} onNext={handleNextStep} />
    }
  }

  return (
    <div className="flex min-h-screen w-full flex-col">
      <main className="flex flex-1 flex-col p-4 md:p-8">
        <div className="mb-4">
          <Button variant="ghost" asChild>
            <Link href="/fr/enterprise/ai-agents/ai-interviewer">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Link>
          </Button>
        </div>
        
        {/* Show workflow stepper for all steps except candidates (phase-based UI has its own navigation) */}
        {steps[currentStep].id !== "candidates" && (
          <WorkflowStepper steps={steps} currentStep={currentStep} />
        )}

        <div className="mt-8 flex-1">{renderSection()}</div>

        {/* Show navigation buttons for all steps except candidates (phase-based UI is self-contained) */}
        {steps[currentStep].id !== "candidates" && (
          <div className="mt-8 flex justify-between">
            <Button onClick={handlePreviousStep} disabled={currentStep === 0} variant="outline">
              Previous
            </Button>
            {currentStep < steps.length - 1 ? (
              <Button onClick={handleNextStep}>Continue</Button>
            ) : (
              <Button onClick={() => console.log("Workflow Complete!")}>Finish</Button>
            )}
          </div>
        )}
      </main>
    </div>
  )
}
