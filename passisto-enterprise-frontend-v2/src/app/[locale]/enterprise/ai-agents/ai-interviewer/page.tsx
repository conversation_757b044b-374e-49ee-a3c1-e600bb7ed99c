"use client";
import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { ProjectCard } from "./_components/project-card"
import { useAuth } from "@clerk/nextjs"
import { Search, Filter, Plus, Archive } from "lucide-react"
import { toast } from "sonner";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useAppDispatch, useAppSelector } from "@/store/hooks"
import { 
  fetchProjects, 
  setFilter, 
  selectFilteredProjects, 
  selectProjectsLoading, 
  selectProjectFilters 
} from "@/store/slices/hiring-system/projectSlice"

export default function ProjectsListingPage() {
  const dispatch = useAppDispatch();
  const { getToken } = useAuth();
  
  const filteredProjects = useAppSelector(selectFilteredProjects);
  const isLoading = useAppSelector(selectProjectsLoading);
  const filter = useAppSelector(selectProjectFilters);
  const [alert, setAlert] = useState<{ title: string; description: string; onConfirm: () => void } | null>(null);

  // Load projects on component mount
  useEffect(() => {
    const loadProjects = async () => {
      try {
        const token = await getToken();
        if (token) {
          dispatch(fetchProjects(token));
        }
      } catch (error) {
        console.error("Error fetching projects:", error);
        toast.error("Failed to Load Projects", {
          description: "Unable to fetch projects. Please check your connection and try again.",
        });
      }
    };

    loadProjects();
  }, [dispatch, getToken]);

  // Handle filter changes
  const handleStatusFilterChange = (status: typeof filter.status) => {
    dispatch(setFilter({ status }));
  };

  const handleSearchChange = (search: string) => {
    dispatch(setFilter({ search }));
  };

  // Get counts for badges
  const getProjectCounts = () => {
    const active = filteredProjects.filter(p => 
      p.status === "IN_PROGRESS" || p.status === "COMPLETE" || p.status === "DRAFT"
    ).length;
    const archived = filteredProjects.filter(p => p.status === "ARCHIVED").length;
    const complete = filteredProjects.filter(p => p.status === "COMPLETE").length;
    const draft = filteredProjects.filter(p => p.status === "DRAFT").length;
    const inProgress = filteredProjects.filter(p => p.status === "IN_PROGRESS").length;

    return { active, archived, complete, draft, inProgress, total: filteredProjects.length };
  };

  const counts = getProjectCounts();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading projects...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 px-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold">Projects</h1>
          <p className="text-muted-foreground mt-1">
            Manage your hiring projects and track progress
          </p>
        </div>
        <Button onClick={() => window.location.href = "/enterprise/ai-agents/ai-interviewer/projects/create"}>
          <Plus className="h-4 w-4 mr-2" />
          New Project
        </Button>
      </div>

      {/* Filters and Search */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search projects by name or description..."
                  className="pl-9"
                  value={filter.search}
                  onChange={(e) => handleSearchChange(e.target.value)}
                />
              </div>
            </div>

            {/* Status Filter */}
            <Select value={filter.status} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">
                  <div className="flex items-center justify-between w-full">
                    <span>Active Projects</span>
                    <Badge variant="secondary" className="ml-2">
                      {counts.active}
                    </Badge>
                  </div>
                </SelectItem>
                <SelectItem value="complete">
                  <div className="flex items-center justify-between w-full">
                    <span>complete</span>
                    <Badge variant="secondary" className="ml-2">
                      {counts.complete}
                    </Badge>
                  </div>
                </SelectItem>
                <SelectItem value="inProgress">
                  <div className="flex items-center justify-between w-full">
                    <span>in progress</span>
                    <Badge variant="secondary" className="ml-2">
                      {counts.inProgress}
                    </Badge>
                  </div>
                </SelectItem>
                <SelectItem value="draft">
                  <div className="flex items-center justify-between w-full">
                    <span>draft</span>
                    <Badge variant="secondary" className="ml-2">
                      {counts.draft}
                    </Badge>
                  </div>
                </SelectItem>
                <SelectItem value="archived">
                  <div className="flex items-center justify-between w-full">
                    <span>
                      <Archive className="h-4 w-4 inline mr-1" />
                      Archived
                    </span>
                    <Badge variant="secondary" className="ml-2">
                      {counts.archived}
                    </Badge>
                  </div>
                </SelectItem>
                <SelectItem value="all">
                  <div className="flex items-center justify-between w-full">
                    <span>All Projects</span>
                    <Badge variant="secondary" className="ml-2">
                      {counts.total}
                    </Badge>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Active Filter Indicators */}
          <div className="flex flex-wrap gap-2">
            {filter.status !== "active" && (
              <Badge variant="outline" className="cursor-pointer" onClick={() => dispatch(setFilter({ status: "active" }))}>
                Status: {filter.status} ×
              </Badge>
            )}
            {filter.search && (
              <Badge variant="outline" className="cursor-pointer" onClick={() => dispatch(setFilter({ search: "" }))}>
                Search: "{filter.search}" ×
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Projects Grid */}
      {filteredProjects.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <div className="mx-auto h-12 w-12 text-muted-foreground mb-4">
                {filter.status === "archived" ? (
                  <Archive className="h-12 w-12" />
                ) : (
                  <Search className="h-12 w-12" />
                )}
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {filter.search ? "No matching projects found" : 
                 filter.status === "archived" ? "No archived projects" :
                 "No projects found"}
              </h3>
              <p className="text-muted-foreground mb-4">
                {filter.search ? `No projects match "${filter.search}". Try adjusting your search terms.` :
                 filter.status === "archived" ? "You haven't archived any projects yet." :
                 "Get started by creating your first project."}
              </p>
              {!filter.search && filter.status !== "archived" && (
                 <Button onClick={() => window.location.href = "/enterprise/ai-agents/ai-interviewer/projects/create"}>
                  <Plus className="h-4 w-4 mr-2" />
                  create your first Project
               </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredProjects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              onUpdate={async () => {
                const token = await getToken();
                if (token) {
                  dispatch(fetchProjects(token));
                }
              }}
              onDelete={async () => setAlert({
                title: "Delete Project",
                description: "Are you sure you want to delete this project? This action cannot be undone.",
                onConfirm: async () => {
                  const token = await getToken();
                  if (token) {
                    dispatch(fetchProjects(token));
                  }
                  setAlert(null);
                },
              })}
            />
          ))}
        </div>
      )}

      {/* Results Summary */}
      {filteredProjects.length > 0 && (
        <div className="mt-6 text-center text-sm text-muted-foreground">
          Showing {filteredProjects.length} of {counts.total} projects
          {filter.status === "archived" && " (archived)"}
          {filter.search && ` matching "${filter.search}"`}
        </div>
      )}

      {alert && (
        <Alert className="fixed bottom-4 right-4 max-w-sm">
          <AlertTitle>{alert.title}</AlertTitle>
          <AlertDescription>{alert.description}</AlertDescription>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setAlert(null)}>
              Cancel
            </Button>
            <Button onClick={alert.onConfirm}>Confirm</Button>
          </div>
        </Alert>
      )}
    </div>
  );
}