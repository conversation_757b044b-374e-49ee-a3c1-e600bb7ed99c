"use client"

import { ColumnDef } from "@tanstack/react-table"
import { ArrowUpDown, MoreHorizontal, Eye, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { CheckCircle, XCircle } from "lucide-react"

export type UploadedFile = {
  id: string
  name: string
  cvFilename?: string
  cvUrl?: string
  cvPath?: string
  status: "Pending" | "Uploading" | "Uploaded" | "Failed"
  currentStatus?: string
  file?: File
  serverMessage?: string
  timestamp?: string
  fileHash?: string
  fileSize?: number
  screeningScore?: number | null
}

interface ColumnActions {
  onRetry: (fileName: string) => void
  onRemove: (id: string) => void
  onViewCV: (file: UploadedFile) => void
  showScreeningScore?: boolean
}

export const createColumns = (actions: ColumnActions): ColumnDef<UploadedFile>[] => {
  const baseColumns: ColumnDef<UploadedFile>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Candidate Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => (
      <div className="font-medium max-w-[200px] truncate" title={row.getValue("name")}>
        {row.getValue("name")}
      </div>
    ),
  },
  {
    accessorKey: "cvFilename",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          CV File Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const filename = row.getValue("cvFilename") as string || row.getValue("name") as string
      const file = row.original
      const hasCV = file.cvUrl || file.cvPath || file.file
      
      return (
        <div className="flex items-center gap-2">
          <div className="text-muted-foreground max-w-[250px] truncate" title={filename}>
            {filename}
          </div>
          {hasCV && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2"
              onClick={() => actions.onViewCV(file)}
              title="View CV"
            >
              <Eye className="h-3 w-3" />
            </Button>
          )}
        </div>
      )
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Upload Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const status = row.getValue("status") as string
      return (
        <Badge
          variant={
            status === "Uploaded"
              ? "default"
              : status === "Failed"
              ? "destructive"
              : status === "Uploading"
              ? "secondary"
              : "secondary"
          }
        >
          {status === "Uploaded" && <CheckCircle className="mr-1 h-3 w-3" />}
          {status === "Failed" && <XCircle className="mr-1 h-3 w-3" />}
          {status}
        </Badge>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "currentStatus",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Current Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const currentStatus = row.getValue("currentStatus") as string
      return (
        <div className="text-muted-foreground">
          {currentStatus || "-"}
        </div>
      )
    },
  },
  {
    accessorKey: "timestamp",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Timestamp
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      )
    },
    cell: ({ row }) => {
      const timestamp = row.getValue("timestamp") as string
      return (
        <div className="whitespace-nowrap">
          {timestamp ? new Date(timestamp).toLocaleString() : "-"}
        </div>
      )
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const file = row.original

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {(file.cvUrl || file.cvPath || file.file) && (
              <DropdownMenuItem onClick={() => actions.onViewCV(file)}>
                <Eye className="mr-2 h-4 w-4" />
                View CV
              </DropdownMenuItem>
            )}
            {file.status === "Failed" && (
              <DropdownMenuItem onClick={() => actions.onRetry(file.name)}>
                Retry Upload
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={() => actions.onRemove(file.id)}
              className="text-destructive"
            >
              Remove
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]

  // Add screening score column if enabled
  if (actions.showScreeningScore) {
    baseColumns.splice(5, 0, {
      accessorKey: "screeningScore",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Screening Score
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const score = row.original.screeningScore
        console.log('Rendering screening score for row:', row.original.name, 'score:', score, 'from getValue:', row.getValue("screeningScore"))
        if (score === null || score === undefined) {
          return <div className="text-muted-foreground">-</div>
        }
        return (
          <div className="font-medium">
            <Badge variant={score >= 70 ? "default" : score >= 50 ? "secondary" : "destructive"}>
              {score.toFixed(1)}
            </Badge>
          </div>
        )
      },
    })
  }

  return baseColumns
}
