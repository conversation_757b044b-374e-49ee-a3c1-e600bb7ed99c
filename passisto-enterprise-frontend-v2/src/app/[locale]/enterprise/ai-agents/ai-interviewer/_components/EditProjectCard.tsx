"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@clerk/nextjs";
import { 
  CalendarDays, 
  MapPin, 
  Briefcase, 
  FileText, 
  X, 
  Save, 
  Loader2,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { 
  updateProject, 
  selectCurrentProject,
  setCurrentProject,
  UpdateProjectDTO,
  selectProjectsLoading
} from "@/store/slices/hiring-system/projectSlice";
import { toast } from "sonner";

interface EditProjectCardProps {
  projectId: string;
  onClose: () => void;
  onSave: () => void;
}


export function EditProjectCard({ projectId, onClose, onSave }: EditProjectCardProps) {
  const { getToken } = useAuth();
  const dispatch = useAppDispatch();
  const currentProject = useAppSelector(selectCurrentProject);
  const isLoading = useAppSelector(selectProjectsLoading);
  
  const [formData, setFormData] = useState<UpdateProjectDTO>({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
  });
  
  const [originalData, setOriginalData] = useState<UpdateProjectDTO>({});
  const [isSaving, setIsSaving] = useState(false);
  const [alert, setAlert] = useState<{ 
    title: string; 
    description: string; 
    onConfirm: () => void;
    variant?: "default" | "destructive";
  } | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (currentProject) {
      const formatDate = (dateString?: string) => {
        if (!dateString) return "";
        return new Date(dateString).toISOString().split('T')[0];
      };

      const projectData: UpdateProjectDTO = {
        name: currentProject.name || "",
        description: currentProject.description || "",
        startDate: formatDate(currentProject.startDate),
        endDate: formatDate(currentProject.endDate),
      };

      setFormData(projectData);
      setOriginalData(projectData);
    }
  }, [currentProject]);

  const hasChanges = () => {
    return Object.keys(formData).some(key => {
      const currentValue = formData[key as keyof UpdateProjectDTO] || "";
      const originalValue = originalData[key as keyof UpdateProjectDTO] || "";
      return currentValue !== originalValue;
    });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = "Project name is required";
    }

    if (formData.startDate && formData.endDate) {
      if (new Date(formData.startDate) > new Date(formData.endDate)) {
        newErrors.endDate = "End date must be after start date";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    if (!hasChanges()) {
      setAlert({
        title: "No Changes Made",
        description: "No changes were detected. The project remains unchanged.",
        onConfirm: () => setAlert(null),
      });
      return;
    }

    const changedData: UpdateProjectDTO = {};
    Object.keys(formData).forEach(key => {
      const currentValue = formData[key as keyof UpdateProjectDTO];
      const originalValue = originalData[key as keyof UpdateProjectDTO];
      
      if (currentValue !== originalValue) {
        changedData[key as keyof UpdateProjectDTO] = currentValue || undefined;
      }
    });

    setIsSaving(true);
    try {
      const token = await getToken();
      if (!token) throw new Error("No authentication token");

      await dispatch(updateProject({ 
        token, 
        projectId, 
        projectData: changedData 
      })).unwrap();

      toast.success("Project Updated", {
        description: "Project has been updated successfully!"
      });
      onSave();
      onClose();
    } catch (error) {
      console.error("Error updating project:", error);
      toast.error("Update Failed", {
        description: "Failed to update project. Please try again."
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges()) {
      setAlert({
        title: "Unsaved Changes",
        description: "You have unsaved changes. Are you sure you want to close without saving?",
        variant: "destructive",
        onConfirm: onClose,
      });
    } else {
      onClose();
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-lg">
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
              <p className="text-muted-foreground">Loading project details...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50" onClick={handleCancel} />
      
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardHeader className="border-b bg-muted/30">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <CardTitle>Edit Project</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Update your project details and settings
                </p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleCancel}
                className="rounded-full"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-6 p-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Briefcase className="h-4 w-4 text-primary" />
                <h3 className="font-medium">Project Information</h3>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="name">Project Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="flex items-center gap-2">
                  <FileText className="h-3 w-3" />
                  Description
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="min-h-[100px]"
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <CalendarDays className="h-4 w-4 text-primary" />
                <h3 className="font-medium">Project Timeline</h3>
              </div>

              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={handleInputChange}
                    disabled
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={handleInputChange}
                    className={errors.endDate ? "border-red-500" : ""}
                  />
                  {errors.endDate && (
                    <p className="text-sm text-red-500 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.endDate}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>

          <div className="border-t bg-muted/30 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {hasChanges() && (
                  <Badge variant="secondary" className="text-xs">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Changes detected
                  </Badge>
                )}
              </div>
              <div className="flex gap-3">
                <Button 
                  variant="outline" 
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  Cancel
                </Button>
                <Button 
                  onClick={handleSave}
                  disabled={isSaving}
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {alert && (
        <Alert className={`fixed bottom-4 right-4 max-w-sm z-[60] border-2 ${
          alert.variant === "destructive" ? "border-red-200 bg-red-50" : "border-green-200 bg-green-50"
        }`}>
          <div className="flex items-start gap-3">
            {alert.variant === "destructive" ? (
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
            ) : (
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
            )}
            <div className="flex-1">
              <AlertTitle className={alert.variant === "destructive" ? "text-red-800" : "text-green-800"}>
                {alert.title}
              </AlertTitle>
              <AlertDescription className={alert.variant === "destructive" ? "text-red-700" : "text-green-700"}>
                {alert.description}
              </AlertDescription>
              <div className="flex justify-end gap-2 mt-4">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setAlert(null)}
                  className="text-xs"
                >
                  Cancel
                </Button>
                <Button 
                  size="sm"
                  onClick={alert.onConfirm}
                  variant={alert.variant === "destructive" ? "destructive" : "default"}
                  className="text-xs"
                >
                  Confirm
                </Button>
              </div>
            </div>
          </div>
        </Alert>
      )}
    </>
  );
}