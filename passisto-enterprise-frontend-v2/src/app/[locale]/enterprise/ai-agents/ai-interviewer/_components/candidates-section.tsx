import React, { useState, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import { CandidateFeedbackModal } from './CandidateFeedbackModal'
import { CandidateProfileModal } from "./candidate-profile-modal"

import { 
  ChevronDown, 
  Download, 
  Mail, 
  Send, 
  MoreHorizontal, 
  User, 
  MapPin, 
  Briefcase, 
  Star, 
  Calendar,
  Filter,
  Search,
  X,
  Eye,
  MessageSquare,
  CheckCircle2,
  XCircle,
  Clock,
  AlertCircle
} from 'lucide-react'



type Candidate = {
  id: string
  name: string
  age: number
  location: string
  experience: number
  compatibilityScore: number
  interviewScore: number | null
  status: "Not Interviewed" | "Sent" | "Done" | "Rejected" | "Accepted"
  skills: string[]
  contact: string
  cvText: string
  currentPhaseId?: string
  avatar?: string
}

type InterviewPhase = {
  id: string
  name: string
  type: string
  description: string
  order: number
  isRequired: boolean
  estimatedDuration: number
  startDate: string
  endDate: string
  allowSkip: boolean
  requiresApproval: boolean
  autoAdvance: boolean
  passingScore: number
  maxAttempts: number
  status: string
  createdAt: string
  updatedAt: string
}

const initialCandidates: Candidate[] = [
  {
    id: "cand-1",
    name: "<PERSON>",
    age: 28,
    location: "London, UK",
    experience: 5,
    compatibilityScore: 92,
    interviewScore: null,
    status: "Not Interviewed",
    skills: ["React", "Node.js", "AWS", "TypeScript"],
    contact: "<EMAIL>",
    cvText: "Experienced software engineer with 5 years in web development...",
    avatar: "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
    currentPhaseId: "mock-phase-1"
  },
  {
    id: "cand-2",
    name: "Bob Williams",
    age: 35,
    location: "New York, USA",
    experience: 10,
    compatibilityScore: 88,
    interviewScore: null,
    status: "Not Interviewed",
    skills: ["Python", "Machine Learning", "Data Science"],
    contact: "<EMAIL>",
    cvText: "Data scientist with a strong background in AI and ML...",
    avatar: "https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
    currentPhaseId: "mock-phase-1"
  },
  {
    id: "cand-3",
    name: "Charlie Brown",
    age: 24,
    location: "Berlin, Germany",
    experience: 2,
    compatibilityScore: 75,
    interviewScore: null,
    status: "Not Interviewed",
    skills: ["JavaScript", "HTML", "CSS"],
    contact: "<EMAIL>",
    cvText: "Junior developer eager to learn and grow...",
    avatar: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
    currentPhaseId: "mock-phase-2"
  },
  {
    id: "cand-4",
    name: "Diana Prince",
    age: 31,
    location: "Paris, France",
    experience: 7,
    compatibilityScore: 95,
    interviewScore: null,
    status: "Not Interviewed",
    skills: ["Vue.js", "Firebase", "UI/UX Design"],
    contact: "<EMAIL>",
    cvText: "Product designer with a passion for user-centric design...",
    avatar: "https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
    currentPhaseId: "mock-phase-2"
  },
  {
    id: "cand-5",
    name: "Eve Adams",
    age: 40,
    location: "Sydney, Australia",
    experience: 15,
    compatibilityScore: 80,
    interviewScore: null,
    status: "Not Interviewed",
    skills: ["Project Management", "Leadership", "Agile"],
    contact: "<EMAIL>",
    cvText: "Senior leader with extensive experience in managing large teams...",
    avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
    currentPhaseId: "mock-phase-2"
  },
]

const mockPhases: InterviewPhase[] = [
  {
    id: "mock-phase-1",
    name: "Initial Screening",
    type: "APPLICATION",
    description: "Initial phone/video screening",
    order: 1,
    isRequired: true,
    estimatedDuration: 30,
    startDate: "",
    endDate: "",
    allowSkip: false,
    requiresApproval: true,
    autoAdvance: false,
    passingScore: 70,
    maxAttempts: 1,
    status: "PENDING",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: "mock-phase-2",
    name: "Technical Assessment",
    type: "TECHNICAL",
    description: "Coding challenge and technical skills evaluation",
    order: 2,
    isRequired: true,
    estimatedDuration: 90,
    startDate: "",
    endDate: "",
    allowSkip: false,
    requiresApproval: true,
    autoAdvance: false,
    passingScore: 75,
    maxAttempts: 1,
    status: "PENDING",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

type CandidatesSectionProps = {
  projectId: string
  phases?: InterviewPhase[]
  onNext: () => void
}

// Reusable components
const Badge = ({ children, variant = "default", className = "" }: { 
  children: React.ReactNode
  variant?: "default" | "secondary" | "destructive" | "outline" | "success"
  className?: string 
}) => {
  const variants = {
    default: "bg-blue-100 text-blue-800 border border-blue-200",
    secondary: "bg-gray-100 text-gray-800 border border-gray-200",
    destructive: "bg-red-100 text-red-800 border border-red-200",
    outline: "bg-transparent border border-gray-300 text-gray-700",
    success: "bg-green-100 text-green-800 border border-green-200"
  }
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[variant]} ${className}`}>
      {children}
    </span>
  )
}

const Button = ({ 
  children, 
  variant = "default", 
  size = "default",
  disabled = false,
  onClick,
  className = "",
  ...props 
}: {
  children: React.ReactNode
  variant?: "default" | "outline" | "ghost" | "destructive"
  size?: "default" | "sm" | "lg"
  disabled?: boolean
  onClick?: () => void
  className?: string
  [key: string]: any
}) => {
  const variants = {
    default: "bg-blue-600 text-white hover:bg-blue-700 border border-blue-600 shadow-sm",
    outline: "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 shadow-sm",
    ghost: "bg-transparent text-gray-700 hover:bg-gray-100 border-0",
    destructive: "bg-red-600 text-white hover:bg-red-700 border border-red-600 shadow-sm"
  }
  
  const sizes = {
    default: "px-4 py-2 text-sm",
    sm: "px-3 py-1.5 text-xs",
    lg: "px-6 py-3 text-base"
  }
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${variants[variant]} ${sizes[size]} ${className}`}
      {...props}
    >
      {children}
    </button>
  )
}

const Card = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <div className={`bg-white rounded-xl border border-gray-200 shadow-sm ${className}`}>
    {children}
  </div>
)

const CardHeader = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <div className={`px-6 py-5 border-b border-gray-100 ${className}`}>
    {children}
  </div>
)

const CardContent = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <div className={`px-6 py-5 ${className}`}>
    {children}
  </div>
)

const DropdownMenu = ({ 
  trigger, 
  children, 
  open, 
  onOpenChange 
}: { 
  trigger: React.ReactNode
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}) => {
  const [isOpen, setIsOpen] = useState(open || false)
  
  const handleToggle = () => {
    const newState = !isOpen
    setIsOpen(newState)
    onOpenChange?.(newState)
  }
  
  return (
    <div className="relative">
      <div onClick={handleToggle}>
        {trigger}
      </div>
      {isOpen && (
        <>
          <div className="fixed inset-0 z-10" onClick={() => setIsOpen(false)} />
          <div className="absolute right-0 top-full mt-1 z-20 min-w-[200px] bg-white rounded-lg border border-gray-200 shadow-lg py-1">
            {children}
          </div>
        </>
      )}
    </div>
  )
}

const DropdownMenuItem = ({ children, onClick, disabled = false }: {
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
}) => (
  <div 
    className={`px-3 py-2 text-sm cursor-pointer hover:bg-gray-50 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    onClick={disabled ? undefined : onClick}
  >
    {children}
  </div>
)

const StatusIcon = ({ status }: { status: Candidate["status"] }) => {
  const icons = {
    "Not Interviewed": <Clock className="w-4 h-4 text-gray-400" />,
    "Sent": <Mail className="w-4 h-4 text-blue-500" />,
    "Done": <CheckCircle2 className="w-4 h-4 text-green-500" />,
    "Rejected": <XCircle className="w-4 h-4 text-red-500" />,
    "Accepted": <CheckCircle2 className="w-4 h-4 text-green-600" />
  }
  return icons[status]
}

export function CandidatesSection({ projectId, phases = [], onNext }: CandidatesSectionProps) {
const router = useRouter()
  const [candidates, setCandidates] = useState<Candidate[]>(initialCandidates)
  const [searchQuery, setSearchQuery] = useState("")
  const [locationFilter, setLocationFilter] = useState("")
  const [experienceRange, setExperienceRange] = useState<[number, number]>([0, 20])
  const [compatibilityScoreRange, setCompatibilityScoreRange] = useState<[number, number]>([0, 100])
  const [interviewScoreRange, setInterviewScoreRange] = useState<[number, number]>([0, 100])
  const [statusFilters, setStatusFilters] = useState<Candidate["status"][]>([])
  const [phaseFilter, setPhaseFilter] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  
  
  // Bulk interview states
  const [selectedCandidatesForInterview, setSelectedCandidatesForInterview] = useState<string[]>([])
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([])
  const [showBulkInterviewSection, setShowBulkInterviewSection] = useState(false)
  const [selectedPhaseForInterview, setSelectedPhaseForInterview] = useState<string>("")

  

  // Feedback modal states
  const [showFeedbackModal, setShowFeedbackModal] = useState(false)
  const [selectedCandidateForFeedback, setSelectedCandidateForFeedback] = useState<Candidate | null>(null)


   const [showProfileModal, setShowProfileModal] = useState(false)
  const [selectedCandidateForProfile, setSelectedCandidateForProfile] = useState<Candidate | null>(null)

  const filteredCandidates = useMemo(() => {
    return candidates.filter((candidate) => {
      const matchesSearch = searchQuery === "" || 
        candidate.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        candidate.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase())) ||
        candidate.location.toLowerCase().includes(searchQuery.toLowerCase())
      
      const matchesLocation = locationFilter === "" ||
        candidate.location.toLowerCase().includes(locationFilter.toLowerCase())
      
      const matchesExperience = candidate.experience >= experienceRange[0] && 
        candidate.experience <= experienceRange[1]
      
      const matchesCompatibility = candidate.compatibilityScore >= compatibilityScoreRange[0] && 
        candidate.compatibilityScore <= compatibilityScoreRange[1]
      
      const matchesInterviewScore = candidate.interviewScore === null ||
        (candidate.interviewScore >= interviewScoreRange[0] && candidate.interviewScore <= interviewScoreRange[1])
      
      const matchesStatus = statusFilters.length === 0 || statusFilters.includes(candidate.status)
      
      const matchesPhase = phaseFilter.length === 0 || 
        phaseFilter.includes(candidate.currentPhaseId ?? "")

      return matchesSearch && matchesLocation && matchesExperience && 
             matchesCompatibility && matchesInterviewScore && matchesStatus && matchesPhase
    })
  }, [candidates, searchQuery, locationFilter, experienceRange, compatibilityScoreRange, 
      interviewScoreRange, statusFilters, phaseFilter])

  const candidatesReadyForInterview = useMemo(() => {
    return filteredCandidates.filter(candidate => candidate.status === "Not Interviewed")
  }, [filteredCandidates])

  const handleSendInterview = (candidateId: string) => {
    setCandidates(prev => prev.map(c => 
      c.id === candidateId ? { ...c, status: "Sent" } : c
    ))
  }
  const handleViewFeedback = (candidate: Candidate) => {
    setSelectedCandidateForFeedback(candidate)
    setShowFeedbackModal(true)
  }

  const handleUpdateStatus = (candidateId: string, newStatus: Candidate["status"]) => {
    setCandidates(prev => prev.map(c => 
      c.id === candidateId ? { ...c, status: newStatus } : c
    ))
  }

  const handleStatusFilterChange = (status: Candidate["status"], checked: boolean) => {
    setStatusFilters(prev => 
      checked ? [...prev, status] : prev.filter(s => s !== status)
    )
  }
  const handleViewProfile = (candidate: Candidate) => {
  setSelectedCandidateForProfile(candidate)
  setShowProfileModal(true)
}

  const handlePhaseFilterChange = (phaseId: string, checked: boolean) => {
    setPhaseFilter(prev => 
      checked ? [...prev, phaseId] : prev.filter(id => id !== phaseId)
    )
  }

  const handleSelectCandidate = (candidateId: string, checked: boolean) => {
    setSelectedCandidates(prev => 
      checked ? [...prev, candidateId] : prev.filter(id => id !== candidateId)
    )
  }

  const handleSelectAllCandidates = (checked: boolean) => {
    setSelectedCandidates(checked ? filteredCandidates.map(c => c.id) : [])
  }

  const handleBulkSendInterviews = () => {
    if (!selectedPhaseForInterview) {
      alert("Please select an interview phase before sending links.")
      return
    }

    setCandidates(prev => prev.map(candidate =>
      selectedCandidatesForInterview.includes(candidate.id)
        ? { ...candidate, status: "Sent" as const }
        : candidate
    ))
    
    alert(`Interview links sent to ${selectedCandidatesForInterview.length} candidates!`)
    setSelectedCandidatesForInterview([])
    setSelectedPhaseForInterview("")
    setShowBulkInterviewSection(false)
  }

  const clearAllFilters = () => {
    setSearchQuery("")
    setLocationFilter("")
    setExperienceRange([0, 20])
    setCompatibilityScoreRange([0, 100])
    setInterviewScoreRange([0, 100])
    setStatusFilters([])
    setPhaseFilter([])
    setPhaseFilter([])
  }

  const activeFiltersCount = [
    searchQuery,
    locationFilter,
    statusFilters.length > 0,
    phaseFilter.length > 0,
    experienceRange[0] !== 0 || experienceRange[1] !== 20,
    compatibilityScoreRange[0] !== 0 || compatibilityScoreRange[1] !== 100,
    interviewScoreRange[0] !== 0 || interviewScoreRange[1] !== 100
  ].filter(Boolean).length

  const allStatuses: Candidate["status"][] = ["Not Interviewed", "Sent", "Done", "Rejected", "Accepted"]

  return (
    <div className="w-full max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
        <div className="px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Candidate Management</h1>
              <p className="mt-1 text-sm text-gray-600">
                Manage and evaluate candidates for your project. {filteredCandidates.length} of {candidates.length} candidates shown.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export CSV
              </Button>
              {selectedCandidates.length > 0 && (
                <Button variant="outline" className="bg-green-50 border-green-200 text-green-700">
                  <CheckCircle2 className="w-4 h-4 mr-2" />
                  Actions ({selectedCandidates.length})
                </Button>
              )}
              {candidatesReadyForInterview.length > 0 && phaseFilter.length === 1 && (
                <>
                  {phaseFilter[0] === "mock-phase-1" && (
                    <Button
                      onClick={() => setShowBulkInterviewSection(!showBulkInterviewSection)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Send Email ({candidatesReadyForInterview.length})
                    </Button>
                  )}
                  {phaseFilter[0] === "mock-phase-2" && (
                    <Button
                      onClick={() => setShowBulkInterviewSection(!showBulkInterviewSection)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Send className="w-4 h-4 mr-2" />
                      Send Interview ({candidatesReadyForInterview.length})
                    </Button>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search candidates, skills, or locations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <Button 
              variant="outline" 
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? "bg-blue-50 border-blue-200 text-blue-700" : ""}
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters {activeFiltersCount > 0 && `(${activeFiltersCount})`}
            </Button>
            {activeFiltersCount > 0 && (
              <Button variant="ghost" onClick={clearAllFilters} size="sm">
                <X className="w-4 h-4 mr-1" />
                Clear
              </Button>
            )}
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="border-t border-gray-100 pt-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Location</label>
                  <input
                    type="text"
                    placeholder="e.g., London, New York"
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <div className="relative">
                    <DropdownMenu
                      trigger={
                        <button className="w-full px-3 py-2 border border-gray-300 rounded-md text-left flex items-center justify-between hover:border-gray-400">
                          <span className="text-gray-700">
                            {statusFilters.length > 0 ? `${statusFilters.length} selected` : "All statuses"}
                          </span>
                          <ChevronDown className="w-4 h-4 text-gray-400" />
                        </button>
                      }
                    >
                      <div className="p-2 space-y-1">
                        {allStatuses.map((status) => (
                          <label key={status} className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                            <input
                              type="checkbox"
                              checked={statusFilters.includes(status)}
                              onChange={(e) => handleStatusFilterChange(status, e.target.checked)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <div className="flex items-center gap-2">
                              <StatusIcon status={status} />
                              <span className="text-sm text-gray-700">{status}</span>
                            </div>
                          </label>
                        ))}
                      </div>
                    </DropdownMenu>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Interview Phase</label>
                  <div className="relative">
                    <DropdownMenu
                      trigger={
                        <button className="w-full px-3 py-2 border border-gray-300 rounded-md text-left flex items-center justify-between hover:border-gray-400">
                          <span className="text-gray-700">
                            {phaseFilter.length > 0 ? `${phaseFilter.length} selected` : "All phases"}
                          </span>
                          <ChevronDown className="w-4 h-4 text-gray-400" />
                        </button>
                      }
                    >
                      <div className="p-2 space-y-1">
                        {mockPhases.map((phase) => (
                          <label key={phase.id} className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                            <input
                              type="checkbox"
                              checked={phaseFilter.includes(phase.id)}
                              onChange={(e) => handlePhaseFilterChange(phase.id, e.target.checked)}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm text-gray-700">Phase {phase.order}: {phase.name}</span>
                          </label>
                        ))}
                      </div>
                    </DropdownMenu>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Experience (years)</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      min="0"
                      max="20"
                      value={experienceRange[0]}
                      onChange={(e) => setExperienceRange([parseInt(e.target.value) || 0, experienceRange[1]])}
                      className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Min"
                    />
                    <span className="text-sm text-gray-500">to</span>
                    <input
                      type="number"
                      min="0"
                      max="20"
                      value={experienceRange[1]}
                      onChange={(e) => setExperienceRange([experienceRange[0], parseInt(e.target.value) || 20])}
                      className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Max"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Compatibility Score (%)</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={compatibilityScoreRange[0]}
                      onChange={(e) => setCompatibilityScoreRange([parseInt(e.target.value) || 0, compatibilityScoreRange[1]])}
                      className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-500">to</span>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={compatibilityScoreRange[1]}
                      onChange={(e) => setCompatibilityScoreRange([compatibilityScoreRange[0], parseInt(e.target.value) || 100])}
                      className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bulk Interview Section */}
      {showBulkInterviewSection && candidatesReadyForInterview.length > 0 && (
        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
          <CardHeader className="border-blue-100">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Mail className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Send Interview Invitations</h3>
                <p className="text-sm text-gray-600">Select candidates and interview phase to send invitations</p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Select Interview Phase *</label>
              <select
                value={selectedPhaseForInterview}
                onChange={(e) => setSelectedPhaseForInterview(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Choose interview phase...</option>
                {mockPhases.map(phase => (
                  <option key={phase.id} value={phase.id}>
                    Phase {phase.order}: {phase.name} ({phase.estimatedDuration}min)
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Select Candidates</label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const allSelected = selectedCandidatesForInterview.length === candidatesReadyForInterview.length
                    setSelectedCandidatesForInterview(allSelected ? [] : candidatesReadyForInterview.map(c => c.id))
                  }}
                >
                  {selectedCandidatesForInterview.length === candidatesReadyForInterview.length ? 'Deselect All' : 'Select All'}
                </Button>
              </div>
              
              <div className="grid gap-2 max-h-64 overflow-y-auto border border-gray-200 rounded-lg p-3">
                {candidatesReadyForInterview.map((candidate) => (
                  <label key={candidate.id} className="flex items-center gap-3 p-2 hover:bg-white rounded-lg cursor-pointer border border-transparent hover:border-gray-200">
                    <input
                      type="checkbox"
                      checked={selectedCandidatesForInterview.includes(candidate.id)}
                      onChange={(e) => {
                        setSelectedCandidatesForInterview(prev =>
                          e.target.checked 
                            ? [...prev, candidate.id]
                            : prev.filter(id => id !== candidate.id)
                        )
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <img 
                      src={candidate.avatar} 
                      alt={candidate.name}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900">{candidate.name}</div>
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Mail className="w-3 h-3" />
                        {candidate.contact}
                      </div>
                    </div>
                    <Badge variant={candidate.compatibilityScore >= 80 ? "default" : "secondary"}>
                      {candidate.compatibilityScore}%
                    </Badge>
                  </label>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-between pt-4 border-t border-blue-100">
              <div className="text-sm text-gray-600">
                {selectedCandidatesForInterview.length} of {candidatesReadyForInterview.length} candidates selected
              </div>
              <div className="flex gap-3">
                <Button variant="outline" onClick={() => setShowBulkInterviewSection(false)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleBulkSendInterviews}
                  disabled={selectedCandidatesForInterview.length === 0 || !selectedPhaseForInterview}
                >
                  <Send className="w-4 h-4 mr-2" />
                  Send Invitations ({selectedCandidatesForInterview.length})
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Candidates Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="py-4 px-6 text-center">
                    <input
                      type="checkbox"
                      checked={selectedCandidates.length === filteredCandidates.length && filteredCandidates.length > 0}
                      onChange={(e) => handleSelectAllCandidates(e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Candidate</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Location</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Experience</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Compatibility</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Interview Score</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Status</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                {filteredCandidates.length > 0 ? (
                  filteredCandidates.map((candidate) => (
                    <tr key={candidate.id} className="hover:bg-gray-50 transition-colors">
                      <td className="py-4 px-6">
                        <input
                          type="checkbox"
                          checked={selectedCandidates.includes(candidate.id)}
                          onChange={(e) => handleSelectCandidate(candidate.id, e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-3">
                          <img 
                            src={candidate.avatar} 
                            alt={candidate.name}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                          <div>
                            <div className="font-medium text-gray-900">{candidate.name}</div>
                            <div className="text-sm text-gray-500">{candidate.contact}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-1 text-gray-600">
                          <MapPin className="w-4 h-4" />
                          <span className="text-sm">{candidate.location}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-1 text-gray-600">
                          <Briefcase className="w-4 h-4" />
                          <span className="text-sm">{candidate.experience} years</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant={candidate.compatibilityScore >= 90 ? "success" : 
                                   candidate.compatibilityScore >= 80 ? "default" : "secondary"}
                          >
                            <Star className="w-3 h-3 mr-1" />
                            {candidate.compatibilityScore}%
                          </Badge>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        {candidate.interviewScore !== null ? (
                          <Badge 
                            variant={candidate.interviewScore >= 80 ? "success" : 
                                   candidate.interviewScore >= 70 ? "default" : "secondary"}
                          >
                            {candidate.interviewScore}%
                          </Badge>
                        ) : (
                          <span className="text-sm text-gray-400">Not interviewed</span>
                        )}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-2">
                          <StatusIcon status={candidate.status} />
                          <Badge
                            variant={
                              candidate.status === "Accepted" ? "success" :
                              candidate.status === "Rejected" ? "destructive" :
                              candidate.status === "Done" ? "default" : "secondary"
                            }
                          >
                            {candidate.status}
                          </Badge>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <DropdownMenu
                          trigger={
                            <Button variant="ghost" size="sm" className="p-1 h-8 w-8">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          }
                        >
                          <DropdownMenuItem onClick={() => handleViewProfile(candidate)}>
  <Eye className="w-4 h-4 mr-2" />
  View Profile
</DropdownMenuItem>

                          <DropdownMenuItem onClick={() => handleViewFeedback(candidate)}>
                            <MessageSquare className="w-4 h-4 mr-2" />
                            View Feedback
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleSendInterview(candidate.id)}
                            disabled={candidate.status !== "Not Interviewed"}
                          >
                            <Send className="w-4 h-4 mr-2" />
                            Send Interview
                          </DropdownMenuItem>
                          <div className="border-t border-gray-100 my-1" />
                          {allStatuses.map((status) => (
                            <DropdownMenuItem
                              key={status}
                              onClick={() => handleUpdateStatus(candidate.id, status)}
                              disabled={candidate.status === status}
                            >
                              <StatusIcon status={status} />
                              <span className="ml-2">Set as {status}</span>
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="py-12 px-6 text-center">
                      <div className="flex flex-col items-center gap-3">
                        <AlertCircle className="w-12 h-12 text-gray-300" />
                        <div>
                          <h3 className="font-medium text-gray-900">No candidates found</h3>
                          <p className="text-sm text-gray-500">Try adjusting your search or filter criteria</p>
                        </div>
                        {activeFiltersCount > 0 && (
                          <Button variant="outline" onClick={clearAllFilters}>
                            Clear all filters
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Feedback Modal */}
      {selectedCandidateForFeedback && (
        <CandidateFeedbackModal
          open={showFeedbackModal}
          onOpenChange={setShowFeedbackModal}
          candidateId={selectedCandidateForFeedback.id}
          candidateName={selectedCandidateForFeedback.name}
        />
      )}
      {selectedCandidateForProfile && (
  <CandidateProfileModal
    open={showProfileModal}
    onOpenChange={setShowProfileModal}
    candidate={selectedCandidateForProfile}
    onSendInterview={(candidateId: string) => handleSendInterview(candidateId)}
    onUpdateStatus={(candidateId: string, status: Candidate["status"]) => handleUpdateStatus(candidateId, status)}
  />
)}


      {/* Footer */}
      <Card>
        <CardContent className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Showing {filteredCandidates.length} of {candidates.length} candidates
          </div>
          <Button onClick={onNext} size="lg">
            Continue to Next Step
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}