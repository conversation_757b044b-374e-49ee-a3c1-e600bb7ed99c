"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CheckCircle, FileText, XCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"

type UploadedFile = {
  id: string
  name: string
  // separate field for CV filename (especially for existing candidates)
  cvFilename?: string
  // client-side upload status
  status: "Pending" | "Uploading" | "Uploaded" | "Failed"
  // candidate's current status in the hiring process
  currentStatus?: string
  // keep original File object for uploading
  file?: File
  // server response/messages related to this file
  serverMessage?: string
  timestamp?: string
  // file hash for duplicate detection
  fileHash?: string
  fileSize?: number
}

type CvUploadSectionProps = {
  projectId: string
  onNext: () => void // Add onNext prop
}

export function CvUploadSection({ projectId, onNext }: CvUploadSectionProps) {
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([])
  const [selectedIds, setSelectedIds] = React.useState<Set<string>>(new Set())
  const [isDragging, setIsDragging] = React.useState(false)
  const [loadingExisting, setLoadingExisting] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      handleFiles(Array.from(files))
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(false)
    const files = event.dataTransfer.files
    if (files) {
      handleFiles(Array.from(files))
    }
  }

  // Generate SHA-256 hash for a file
  const generateFileHash = async (file: File): Promise<string> => {
    const buffer = await file.arrayBuffer()
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  const handleFiles = async (files: File[]) => {
    const newFiles: UploadedFile[] = []
    const duplicates: string[] = []
    
    for (const file of files) {
      try {
        // Generate hash for the new file
        const fileHash = await generateFileHash(file)
        const fileSize = file.size
        
        // Check for duplicates by name, size, and hash
        const isDuplicate = uploadedFiles.some(
          (existingFile) =>
            existingFile.name === file.name ||
            (existingFile.fileHash === fileHash && existingFile.fileSize === fileSize)
        )
        
        if (isDuplicate) {
          duplicates.push(file.name)
        } else {
          newFiles.push({
            id: `${file.name}-${Date.now()}-${Math.random()}`,
            name: file.name,
            cvFilename: file.name,
            status: "Pending",
            file,
            fileHash,
            fileSize,
          })
        }
      } catch (err) {
        console.error(`Error processing file ${file.name}:`, err)
        setError(`Failed to process ${file.name}`)
      }
    }
    
    // Show warning if duplicates were found
    if (duplicates.length > 0) {
      setError(`Duplicate files detected and skipped: ${duplicates.join(', ')}`)
      setTimeout(() => setError(null), 5000) // Clear error after 5 seconds
    }
    
    if (newFiles.length > 0) {
      setUploadedFiles((prev) => [...prev, ...newFiles])
    }
  }

  // upload a single File via FormData to POST /projects/{id}/candidates
  const uploadFile = async (file: File, existingId?: string) => {
    const id = existingId ?? `${file.name}-${Date.now()}-${Math.random()}`
    
    // Generate hash before upload to check server-side duplicates
    let fileHash: string | undefined
    let fileSize: number | undefined
    
    try {
      fileHash = await generateFileHash(file)
      fileSize = file.size
      
      // Check if this exact file (by hash) has already been uploaded
      const alreadyUploaded = uploadedFiles.find(
        (f) => f.fileHash === fileHash && f.fileSize === fileSize && f.status === "Uploaded"
      )
      
      if (alreadyUploaded) {
        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === id
              ? {
                  ...f,
                  status: "Failed",
                  serverMessage: "Duplicate file already uploaded",
                  timestamp: new Date().toISOString(),
                }
              : f
          )
        )
        return
      }
    } catch (err) {
      console.error("Error generating file hash:", err)
      // Continue with upload even if hash generation fails
    }

    // mark as uploading and ensure file is stored on the entry
    setUploadedFiles((prev) => {
      const idx = prev.findIndex((p) => p.id === id)
      if (idx >= 0) {
        const copy = [...prev]
        copy[idx] = { ...copy[idx], status: "Uploading", file, fileHash, fileSize }
        return copy
      }
      return [{ id, name: file.name, status: "Uploading", file, fileHash, fileSize }, ...prev]
    })

    const form = new FormData()
    // backend expects files array under 'files' (adapt if API expects different name)
    form.append("files", file)

    try {
      const res = await fetch(`/api-hiring/v1/projects/${projectId}/candidates`, {
        method: "POST",
        body: form,
      })

      const now = new Date().toISOString()

      if (!res.ok) {
        let msg = `Server responded ${res.status}`
        try {
          const json = await res.json()
          msg = json?.message || JSON.stringify(json)
        } catch (e) {
          // ignore
        }
        setUploadedFiles((prev) => prev.map((f) => (f.id === id ? { ...f, status: "Failed", serverMessage: msg, timestamp: now } : f)))
        return
      }

      // success - parse response to show counts/messages
      let body: any = null
      try {
        body = await res.json()
      } catch (e) {
        body = null
      }

      const serverMessage = body ? JSON.stringify(body) : "Uploaded"
      setUploadedFiles((prev) => prev.map((f) => (f.id === id ? { ...f, status: "Uploaded", serverMessage, timestamp: now } : f)))
      // optionally refresh candidates list to show newly created candidate objects
      fetchExistingCandidates()
    } catch (err: any) {
      const msg = err?.message || "Network error"
      const now = new Date().toISOString()
      setUploadedFiles((prev) => prev.map((f) => (f.id === id ? { ...f, status: "Failed", serverMessage: msg, timestamp: now } : f)))
    }
  }

  const uploadAll = () => {
    const toUpload = uploadedFiles.filter((f) => (f.status === "Pending" || f.status === "Failed") && f.file)
    toUpload.forEach((f) => {
      if (f.file) uploadFile(f.file, f.id)
    })
  }

  const uploadSelected = () => {
    const toUpload = uploadedFiles.filter((f) => selectedIds.has(f.id) && (f.status === "Pending" || f.status === "Failed") && f.file)
    toUpload.forEach((f) => f.file && uploadFile(f.file, f.id))
  }

  const retrySelected = () => {
    const toRetry = uploadedFiles.filter((f) => selectedIds.has(f.id) && f.status === "Failed")
    toRetry.forEach((f) => {
      if (f.file) uploadFile(f.file, f.id)
      else retryUpload(f.name)
    })
  }

  const removeSelected = async () => {
    const toRemove = uploadedFiles.filter((f) => selectedIds.has(f.id))
    for (const f of toRemove) {
      await new Promise<void>((res) => {
        removeFile(f.id)
        // small delay to let UI update; removeFile updates state
        setTimeout(() => res(), 100)
      })
    }
    setSelectedIds(new Set())
  }

  const toggleSelect = (id: string) => {
    setSelectedIds((prev) => {
      const copy = new Set(prev)
      if (copy.has(id)) copy.delete(id)
      else copy.add(id)
      return copy
    })
  }

  const toggleSelectAll = () => {
    setSelectedIds((prev) => {
      if (prev.size === uploadedFiles.length) return new Set()
      return new Set(uploadedFiles.map((f) => f.id))
    })
  }

  const handleConfirmImport = () => {
    // The backend handles parsing/screening; here we simply proceed
    onNext()
  }

  const retryUpload = (fileName: string) => {
    // Try to find stored File object first
    const fileEntry = uploadedFiles.find((p) => p.name === fileName)
    if (fileEntry?.file) {
      uploadFile(fileEntry.file, fileEntry.id)
      return
    }

    // fall back to prompting user to re-select file
    const prompt = window.confirm("To retry upload, please re-select the file from your computer. Open file dialog now?")
    if (prompt) {
      const input = document.createElement("input")
      input.type = "file"
      input.accept = ".pdf,.doc,.docx"
      input.onchange = () => {
        if (input.files && input.files[0]) {
          const f = input.files[0]
          if (f.name !== fileName) {
            if (!window.confirm(`You selected ${f.name}. Retry upload for this file instead of ${fileName}?`)) return
          }
          uploadFile(f)
        }
      }
      input.click()
    }
  }

  const removeFile = (id: string) => {
    const file = uploadedFiles.find((p) => p.id === id)
    if (!file) return

    // If this is a server-side candidate entry (created from existing candidates), attempt backend delete
    const matchById = file.id.startsWith("cand-") ? file.id.replace(/^cand-/, "") : null
    const matchByName = file.name.startsWith("candidate:") ? file.name.replace(/^candidate:/, "") : null

    const candidateId = matchById || matchByName

    if (candidateId) {
      // call backend to remove candidate and its CV
      ;(async () => {
        try {
          const res = await fetch(`/api-hiring/v1/projects/${projectId}/candidates/${candidateId}`, {
            method: "DELETE",
          })
          if (!res.ok) {
            let msg = `Server responded ${res.status}`
            try {
              const json = await res.json()
              msg = json?.message || JSON.stringify(json)
            } catch (e) {
              // ignore
            }
            setError(msg)
            return
          }

          // remove from UI
          setUploadedFiles((prev) => prev.filter((p) => p.id !== id))
        } catch (err: any) {
          setError(err?.message || String(err))
        }
      })()
    } else {
      // local-only entry (not yet uploaded to server) -> just remove from UI
      setUploadedFiles((prev) => prev.filter((p) => p.id !== id))
    }
  }

  const fetchExistingCandidates = async () => {
    setLoadingExisting(true)
    setError(null)
    try {
      const res = await fetch(`/api-hiring/v1/projects/${projectId}/candidates`)
      if (!res.ok) {
        throw new Error(`Failed to fetch existing candidates: ${res.status}`)
      }
      const body = await res.json()
      // body expected to be array of candidate objects; adapt as needed
      if (Array.isArray(body)) {
        // Map candidates to uploadedFiles entries for display (recent uploads)
        const mapped = body.map((c: any) => ({ 
          id: `cand-${c.id}`, 
          name: c.name || c.fullName || 'N/A' || `Candidate ${c.id}`,
          cvFilename: c.cvFilename || c.cvOriginalName || 'N/A',
          status: "Uploaded" as const,
          currentStatus: c.current_status || c.currentStatus || c.status || 'N/A',
          serverMessage: JSON.stringify(c), 
          timestamp: c.created_at || c.createdAt || c.applicationDate || new Date().toISOString(),
          fileHash: c.cvFileHash,
          fileSize: c.cvFileSize
        }))
        
        setUploadedFiles((prev) => {
          // Use hash and size for more reliable duplicate detection
          const existingHashes = new Set(
            prev
              .filter(p => p.fileHash)
              .map((p) => `${p.fileHash}-${p.fileSize}`)
          )
          
          const toAdd = mapped.filter((m: any) => {
            const hashKey = m.fileHash ? `${m.fileHash}-${m.fileSize}` : null
            return hashKey ? !existingHashes.has(hashKey) : !prev.some(p => p.name === m.name)
          })
          
          return [...toAdd, ...prev]
        })
      }
    } catch (err: any) {
      setError(err?.message || String(err))
    } finally {
      setLoadingExisting(false)
    }
  }

  // Fetch existing candidates on mount
  React.useEffect(() => {
    // call fetchExistingCandidates when component mounts
    fetchExistingCandidates()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Upload Candidate CVs</CardTitle>
        <CardDescription>Drag and drop your candidate CVs (PDF, DOC, DOCX) here. Files are uploaded to the project and processed server-side.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div
          className={`flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
            isDragging ? "border-primary bg-primary/5" : "border-muted-foreground/20"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <FileText className="mb-4 h-12 w-12 text-muted-foreground" />
          <p className="text-lg font-medium text-muted-foreground">Drag & Drop your CVs here</p>
          <p className="text-sm text-muted-foreground">or</p>
          <Label htmlFor="file-upload" className="cursor-pointer text-primary hover:underline">
            Browse files
          </Label>
          <Input id="file-upload" type="file" multiple accept=".pdf,.doc,.docx" className="hidden" onChange={handleFileChange} />
        </div>

        {loadingExisting && <div className="text-sm text-muted-foreground">Loading existing uploads...</div>}

        {error && <div className="text-sm text-destructive">{error}</div>}

        {uploadedFiles.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Uploaded Files</h3>
            <div className="flex gap-2">
              <Button onClick={uploadAll} className="" disabled={!uploadedFiles.some((f) => (f.status === "Pending" || f.status === "Failed") && f.file)}>
                Upload all
              </Button>
              <Button onClick={uploadSelected} variant="secondary" disabled={!Array.from(selectedIds).some((id) => {
                const f = uploadedFiles.find((p) => p.id === id)
                return f && (f.status === "Pending" || f.status === "Failed") && f.file
              })}
              >
                Upload selected
              </Button>
              <Button onClick={retrySelected} variant="ghost" disabled={!Array.from(selectedIds).some((id) => {
                const f = uploadedFiles.find((p) => p.id === id)
                return f && f.status === "Failed"
              })}
              >
                Retry selected
              </Button>
              <Button onClick={removeSelected} variant="destructive" disabled={selectedIds.size === 0}>
                Remove selected
              </Button>
              <Button onClick={fetchExistingCandidates} variant="ghost">
                Refresh
              </Button>
            </div>
            <div className="overflow-x-auto rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <input
                        type="checkbox"
                        checked={selectedIds.size === uploadedFiles.length && uploadedFiles.length > 0}
                        onChange={toggleSelectAll}
                        aria-label="Select all files"
                      />
                    </TableHead>
                    <TableHead className="min-w-[150px]">Candidate Name</TableHead>
                    <TableHead className="min-w-[200px]">CV File Name</TableHead>
                    <TableHead className="w-32">Upload Status</TableHead>
                    <TableHead className="min-w-[120px]">Current Status</TableHead>
                    {/* <TableHead>Server Message</TableHead> */}
                    <TableHead className="min-w-[180px]">Timestamp</TableHead>
                    <TableHead className="w-40">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {uploadedFiles.map((file) => (
                    <TableRow key={file.id}>
                      <TableCell>
                        <input type="checkbox" checked={selectedIds.has(file.id)} onChange={() => toggleSelect(file.id)} aria-label={`Select ${file.name}`} />
                      </TableCell>
                      <TableCell className="font-medium max-w-[200px] truncate" title={file.name}>
                        {file.name}
                      </TableCell>
                      <TableCell className="text-muted-foreground max-w-[300px] truncate" title={file.cvFilename || file.name}>
                        {file.cvFilename || file.name}
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            file.status === "Uploaded" ? "default" : file.status === "Failed" ? "destructive" : file.status === "Uploading" ? "secondary" : "secondary"
                          }
                        >
                          {file.status === "Uploaded" && <CheckCircle className="mr-1 h-3 w-3" />}
                          {file.status === "Failed" && <XCircle className="mr-1 h-3 w-3" />}
                          {file.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-muted-foreground">
                        {file.currentStatus || "-"}
                      </TableCell>
                      {/* <TableCell className="max-w-md truncate break-words">{file.serverMessage || "-"}</TableCell> */}
                      <TableCell className="whitespace-nowrap">{file.timestamp ? new Date(file.timestamp).toLocaleString() : "-"}</TableCell>
                      <TableCell>
                        <div className="flex gap-2 whitespace-nowrap">
                          {file.status === "Failed" && (
                            <Button variant="ghost" size="sm" onClick={() => retryUpload(file.name)}>
                              Retry
                            </Button>
                          )}
                          <Button variant="ghost" size="sm" onClick={() => removeFile(file.id)}>
                            Remove
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            {/* <Button onClick={handleConfirmImport} className="w-full">
              Continue
            </Button> */}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
