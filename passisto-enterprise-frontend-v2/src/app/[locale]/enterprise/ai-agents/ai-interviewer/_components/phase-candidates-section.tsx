"use client"

import React, { useState, useMemo } from 'react'
import { PhaseCandidateDetailModal } from './phase-candidate-detail-modal'
import { BulkActionModal } from './bulk-action-modal'
import { 
  ChevronDown, 
  Download, 
  Mail, 
  Send, 
  MoreHorizontal, 
  MapPin, 
  Briefcase, 
  Star, 
  Filter,
  Search,
  X,
  Eye,
  MessageSquare,
  CheckCircle2,
  XCircle,
  Clock,
  AlertCircle,
  ArrowRight,
  Lock,
  FileText,
  TrendingUp,
  Users,
  CheckCheck,
  Ban,
  ChevronRight,
  Activity
} from 'lucide-react'

// Types
type PhaseStatus = "PENDING" | "ACTIVE" | "COMPLETED" | "CANCELLED"
type PhaseType = "SCREENING" | "INTERVIEW"
type CandidatePhaseStatus = "PENDING" | "QUALIFIED" | "REJECTED" | "IN_PROGRESS"

type Phase = {
  id: string
  name: string
  type: PhaseType
  description: string
  order: number
  status: PhaseStatus
  isClosed: boolean
  passingScore: number
  candidateCount: number
  qualifiedCount: number
  rejectedCount: number
}

type CandidateAction = {
  id: string
  type: "EMAIL_SENT" | "SCORE_ASSIGNED" | "INTERVIEW_SENT" | "FEEDBACK_GENERATED"
  timestamp: string
  details?: string
}

type Candidate = {
  id: string
  name: string
  email: string
  location: string
  experience: number
  avatar?: string
  phaseStatus: CandidatePhaseStatus
  score?: number
  feedback?: string
  actions: CandidateAction[]
  skills: string[]
}

// Mock data
const mockPhases: Phase[] = [
  {
    id: "phase-1",
    name: "CV Screening",
    type: "SCREENING",
    description: "AI-based CV evaluation and scoring",
    order: 1,
    status: "ACTIVE",
    isClosed: false,
    passingScore: 70,
    candidateCount: 12,
    qualifiedCount: 0,
    rejectedCount: 0
  },
  {
    id: "phase-2",
    name: "AI Interview",
    type: "INTERVIEW",
    description: "Automated technical interview",
    order: 2,
    status: "PENDING",
    isClosed: false,
    passingScore: 75,
    candidateCount: 0,
    qualifiedCount: 0,
    rejectedCount: 0
  }
]

const mockCandidates: Record<string, Candidate[]> = {
  "phase-1": [
    {
      id: "cand-1",
      name: "Alice Johnson",
      email: "<EMAIL>",
      location: "London, UK",
      experience: 5,
      avatar: "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      phaseStatus: "IN_PROGRESS",
      score: 85,
      feedback: "Strong technical background with relevant experience in React and Node.js. Excellent match for senior positions.",
      actions: [
        { id: "a1", type: "SCORE_ASSIGNED", timestamp: "2025-10-07T10:30:00Z", details: "AI scored: 85/100" },
        { id: "a2", type: "EMAIL_SENT", timestamp: "2025-10-07T11:00:00Z", details: "Invitation sent" }
      ],
      skills: ["React", "Node.js", "TypeScript", "AWS"]
    },
    {
      id: "cand-2",
      name: "Bob Williams",
      email: "<EMAIL>",
      location: "New York, USA",
      experience: 10,
      avatar: "https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      phaseStatus: "IN_PROGRESS",
      score: 78,
      feedback: "Extensive experience in data science and ML. Good technical skills but limited web development background.",
      actions: [
        { id: "a3", type: "SCORE_ASSIGNED", timestamp: "2025-10-07T09:15:00Z", details: "AI scored: 78/100" }
      ],
      skills: ["Python", "Machine Learning", "Data Science"]
    },
    {
      id: "cand-3",
      name: "Charlie Brown",
      email: "<EMAIL>",
      location: "Berlin, Germany",
      experience: 2,
      avatar: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      phaseStatus: "PENDING",
      skills: ["JavaScript", "HTML", "CSS"],
      actions: []
    },
    {
      id: "cand-4",
      name: "Diana Prince",
      email: "<EMAIL>",
      location: "Paris, France",
      experience: 7,
      avatar: "https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      phaseStatus: "PENDING",
      skills: ["Vue.js", "Firebase", "UI/UX Design"],
      actions: []
    }
  ],
  "phase-2": []
}

// UI Components
const Badge = ({ children, variant = "default", className = "" }: { 
  children: React.ReactNode
  variant?: "default" | "secondary" | "destructive" | "outline" | "success" | "warning"
  className?: string 
}) => {
  const variants = {
    default: "bg-blue-100 text-blue-800 border border-blue-200",
    secondary: "bg-gray-100 text-gray-800 border border-gray-200",
    destructive: "bg-red-100 text-red-800 border border-red-200",
    outline: "bg-transparent border border-gray-300 text-gray-700",
    success: "bg-green-100 text-green-800 border border-green-200",
    warning: "bg-yellow-100 text-yellow-800 border border-yellow-200"
  }
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[variant]} ${className}`}>
      {children}
    </span>
  )
}

const Button = ({ 
  children, 
  variant = "default", 
  size = "default",
  disabled = false,
  onClick,
  className = "",
  ...props 
}: {
  children: React.ReactNode
  variant?: "default" | "outline" | "ghost" | "destructive" | "success"
  size?: "default" | "sm" | "lg"
  disabled?: boolean
  onClick?: () => void
  className?: string
  [key: string]: any
}) => {
  const variants = {
    default: "bg-blue-600 text-white hover:bg-blue-700 border border-blue-600 shadow-sm",
    outline: "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 shadow-sm",
    ghost: "bg-transparent text-gray-700 hover:bg-gray-100 border-0",
    destructive: "bg-red-600 text-white hover:bg-red-700 border border-red-600 shadow-sm",
    success: "bg-green-600 text-white hover:bg-green-700 border border-green-600 shadow-sm"
  }
  
  const sizes = {
    default: "px-4 py-2 text-sm",
    sm: "px-3 py-1.5 text-xs",
    lg: "px-6 py-3 text-base"
  }
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${variants[variant]} ${sizes[size]} ${className}`}
      {...props}
    >
      {children}
    </button>
  )
}

const Card = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <div className={`bg-white rounded-xl border border-gray-200 shadow-sm ${className}`}>
    {children}
  </div>
)

const CardHeader = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <div className={`px-6 py-5 border-b border-gray-100 ${className}`}>
    {children}
  </div>
)

const CardContent = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <div className={`px-6 py-5 ${className}`}>
    {children}
  </div>
)

// Main Component
export function PhaseCandidatesSection({ projectId }: { projectId: string }) {
  const [phases] = useState<Phase[]>(mockPhases)
  const [activePhaseId, setActivePhaseId] = useState(phases[0]?.id)
  const [candidatesByPhase, setCandidatesByPhase] = useState(mockCandidates)
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [scoreFilter, setScoreFilter] = useState<[number, number]>([0, 100])
  const [statusFilter, setStatusFilter] = useState<CandidatePhaseStatus[]>([])
  const [showFilters, setShowFilters] = useState(false)
  
  // Modal states
  const [detailModalOpen, setDetailModalOpen] = useState(false)
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null)
  const [bulkActionModalOpen, setBulkActionModalOpen] = useState(false)
  const [bulkActionType, setBulkActionType] = useState<"EMAIL" | "INTERVIEW" | "SCORE">("EMAIL")

  const activePhase = phases.find(p => p.id === activePhaseId)
  const phaseCandidates = candidatesByPhase[activePhaseId] || []

  const filteredCandidates = useMemo(() => {
    return phaseCandidates.filter(candidate => {
      const matchesSearch = searchQuery === "" || 
        candidate.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        candidate.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        candidate.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()))
      
      const matchesScore = !candidate.score || 
        (candidate.score >= scoreFilter[0] && candidate.score <= scoreFilter[1])
      
      const matchesStatus = statusFilter.length === 0 || 
        statusFilter.includes(candidate.phaseStatus)

      return matchesSearch && matchesScore && matchesStatus
    })
  }, [phaseCandidates, searchQuery, scoreFilter, statusFilter])

  const handleScoreCandidate = (candidateId: string) => {
    // Simulate AI scoring
    console.log("Scoring candidate:", candidateId)
  }

  const handleSendInterview = (candidateId: string) => {
    console.log("Sending interview to:", candidateId)
  }
  
  const handleViewCandidate = (candidate: Candidate) => {
    setSelectedCandidate(candidate)
    setDetailModalOpen(true)
  }
  
  const handleBulkAction = (actionType: "EMAIL" | "INTERVIEW" | "SCORE") => {
    setBulkActionType(actionType)
    setBulkActionModalOpen(true)
  }
  
  const handleBulkActionConfirm = async (data: any) => {
    console.log("Bulk action confirmed:", bulkActionType, data)
    // Implement bulk action logic here
    setSelectedCandidates([])
  }

  const handleQualifyCandidates = () => {
    if (selectedCandidates.length === 0) return
    
    setCandidatesByPhase(prev => {
      const updated = { ...prev }
      updated[activePhaseId] = updated[activePhaseId].map(c => 
        selectedCandidates.includes(c.id) ? { ...c, phaseStatus: "QUALIFIED" as CandidatePhaseStatus } : c
      )
      return updated
    })
    setSelectedCandidates([])
  }

  const handleRejectCandidates = () => {
    if (selectedCandidates.length === 0) return
    
    setCandidatesByPhase(prev => {
      const updated = { ...prev }
      updated[activePhaseId] = updated[activePhaseId].map(c => 
        selectedCandidates.includes(c.id) ? { ...c, phaseStatus: "REJECTED" as CandidatePhaseStatus } : c
      )
      return updated
    })
    setSelectedCandidates([])
  }

  const handleAdvanceToNextPhase = () => {
    const qualifiedCandidates = phaseCandidates.filter(c => c.phaseStatus === "QUALIFIED")
    const nextPhase = phases.find(p => p.order === (activePhase?.order || 0) + 1)
    
    if (nextPhase && qualifiedCandidates.length > 0) {
      setCandidatesByPhase(prev => ({
        ...prev,
        [nextPhase.id]: [
          ...(prev[nextPhase.id] || []),
          ...qualifiedCandidates.map(c => ({ ...c, phaseStatus: "PENDING" as CandidatePhaseStatus, score: undefined, feedback: undefined }))
        ]
      }))
    }
  }

  const handleClosePhase = () => {
    if (confirm("Are you sure you want to close this phase? It will become read-only.")) {
      console.log("Closing phase:", activePhaseId)
    }
  }

  const getStatusIcon = (status: CandidatePhaseStatus) => {
    const icons = {
      "PENDING": <Clock className="w-4 h-4 text-gray-400" />,
      "IN_PROGRESS": <Activity className="w-4 h-4 text-blue-500" />,
      "QUALIFIED": <CheckCircle2 className="w-4 h-4 text-green-500" />,
      "REJECTED": <XCircle className="w-4 h-4 text-red-500" />
    }
    return icons[status]
  }

  return (
    <div className="w-full max-w-7xl mx-auto space-y-6">
      {/* Phase Tabs */}
      <Card>
        <CardContent className="p-0">
          <div className="flex items-center overflow-x-auto">
            {phases.map((phase, index) => (
              <React.Fragment key={phase.id}>
                <button
                  onClick={() => setActivePhaseId(phase.id)}
                  className={`flex-1 min-w-[200px] px-6 py-4 text-left transition-colors ${
                    activePhaseId === phase.id 
                      ? 'bg-blue-50 border-b-2 border-blue-600' 
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-gray-900">Phase {phase.order}</span>
                        {phase.isClosed && <Lock className="w-4 h-4 text-gray-400" />}
                      </div>
                      <div className="text-sm font-medium text-gray-700 mt-1">{phase.name}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {phase.candidateCount} candidates
                        {phase.qualifiedCount > 0 && ` • ${phase.qualifiedCount} qualified`}
                      </div>
                    </div>
                    <Badge variant={
                      phase.status === "ACTIVE" ? "default" :
                      phase.status === "COMPLETED" ? "success" :
                      "secondary"
                    }>
                      {phase.status}
                    </Badge>
                  </div>
                </button>
                {index < phases.length - 1 && (
                  <ChevronRight className="w-5 h-5 text-gray-300 flex-shrink-0" />
                )}
              </React.Fragment>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Phase Actions Bar */}
      {activePhase && (
        <Card className={activePhase.isClosed ? "bg-gray-50" : ""}>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  {activePhase.name}
                  {activePhase.isClosed && (
                    <Badge variant="secondary">
                      <Lock className="w-3 h-3 mr-1" />
                      Closed
                    </Badge>
                  )}
                </h2>
                <p className="text-sm text-gray-600 mt-1">{activePhase.description}</p>
              </div>
              
              {!activePhase.isClosed && (
                <div className="flex items-center gap-3">
                  {selectedCandidates.length > 0 && (
                    <>
                      <Button variant="success" onClick={handleQualifyCandidates}>
                        <CheckCheck className="w-4 h-4 mr-2" />
                        Qualify ({selectedCandidates.length})
                      </Button>
                      <Button variant="destructive" onClick={handleRejectCandidates}>
                        <Ban className="w-4 h-4 mr-2" />
                        Reject ({selectedCandidates.length})
                      </Button>
                    </>
                  )}
                  
                  {phaseCandidates.some(c => c.phaseStatus === "QUALIFIED") && (
                    <Button onClick={handleAdvanceToNextPhase}>
                      <ArrowRight className="w-4 h-4 mr-2" />
                      Advance Qualified
                    </Button>
                  )}
                  
                  <Button variant="outline" onClick={handleClosePhase}>
                    <Lock className="w-4 h-4 mr-2" />
                    Close Phase
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Phase-specific action panels */}
      {activePhase && !activePhase.isClosed && (
        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
          <CardHeader className="border-blue-100">
            <h3 className="text-lg font-semibold text-gray-900">
              {activePhase.type === "SCREENING" ? "CV Screening Actions" : "Interview Actions"}
            </h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {activePhase.type === "SCREENING" ? (
                <>
                  <Button className="w-full justify-start" variant="outline" onClick={() => handleBulkAction("SCORE")}>
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Score Selected CVs
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <FileText className="w-4 h-4 mr-2" />
                    View AI Feedback
                  </Button>
                  <Button className="w-full justify-start" variant="outline" onClick={() => handleBulkAction("EMAIL")}>
                    <Mail className="w-4 h-4 mr-2" />
                    Send Bulk Email
                  </Button>
                </>
              ) : (
                <>
                  <Button className="w-full justify-start" variant="outline" onClick={() => handleBulkAction("INTERVIEW")}>
                    <Send className="w-4 h-4 mr-2" />
                    Send Interview Links
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <MessageSquare className="w-4 h-4 mr-2" />
                    View Interview Feedback
                  </Button>
                  <Button className="w-full justify-start" variant="outline" onClick={() => handleBulkAction("EMAIL")}>
                    <Mail className="w-4 h-4 mr-2" />
                    Send Bulk Email
                  </Button>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filters */}
      <Card>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search candidates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={activePhase?.isClosed}
              />
            </div>
            <Button 
              variant="outline" 
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? "bg-blue-50 border-blue-200 text-blue-700" : ""}
              disabled={activePhase?.isClosed}
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <Button variant="outline" disabled={activePhase?.isClosed}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          {showFilters && (
            <div className="border-t border-gray-100 pt-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Score Range</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={scoreFilter[0]}
                      onChange={(e) => setScoreFilter([parseInt(e.target.value) || 0, scoreFilter[1]])}
                      className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-500">to</span>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={scoreFilter[1]}
                      onChange={(e) => setScoreFilter([scoreFilter[0], parseInt(e.target.value) || 100])}
                      className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <div className="flex flex-wrap gap-2">
                    {(["PENDING", "IN_PROGRESS", "QUALIFIED", "REJECTED"] as CandidatePhaseStatus[]).map(status => (
                      <label key={status} className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={statusFilter.includes(status)}
                          onChange={(e) => {
                            setStatusFilter(prev => 
                              e.target.checked ? [...prev, status] : prev.filter(s => s !== status)
                            )
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">{status}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Candidates Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="py-4 px-6 text-center">
                    <input
                      type="checkbox"
                      checked={selectedCandidates.length === filteredCandidates.length && filteredCandidates.length > 0}
                      onChange={(e) => setSelectedCandidates(e.target.checked ? filteredCandidates.map(c => c.id) : [])}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      disabled={activePhase?.isClosed}
                    />
                  </th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Candidate</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Location</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Score</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Status</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Actions Taken</th>
                  <th className="text-left py-4 px-6 font-medium text-gray-900">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100">
                {filteredCandidates.length > 0 ? (
                  filteredCandidates.map((candidate) => (
                    <tr key={candidate.id} className="hover:bg-gray-50 transition-colors">
                      <td className="py-4 px-6">
                        <input
                          type="checkbox"
                          checked={selectedCandidates.includes(candidate.id)}
                          onChange={(e) => {
                            setSelectedCandidates(prev => 
                              e.target.checked ? [...prev, candidate.id] : prev.filter(id => id !== candidate.id)
                            )
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          disabled={activePhase?.isClosed}
                        />
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-3">
                          <img 
                            src={candidate.avatar} 
                            alt={candidate.name}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                          <div>
                            <div className="font-medium text-gray-900">{candidate.name}</div>
                            <div className="text-sm text-gray-500">{candidate.email}</div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-1 text-gray-600">
                          <MapPin className="w-4 h-4" />
                          <span className="text-sm">{candidate.location}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        {candidate.score ? (
                          <div className="space-y-1">
                            <Badge 
                              variant={candidate.score >= 80 ? "success" : 
                                     candidate.score >= 60 ? "default" : "warning"}
                            >
                              <Star className="w-3 h-3 mr-1" />
                              {candidate.score}/100
                            </Badge>
                            {candidate.feedback && (
                              <div className="text-xs text-gray-500 max-w-xs truncate">
                                {candidate.feedback}
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">Not scored</span>
                        )}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(candidate.phaseStatus)}
                          <Badge
                            variant={
                              candidate.phaseStatus === "QUALIFIED" ? "success" :
                              candidate.phaseStatus === "REJECTED" ? "destructive" :
                              candidate.phaseStatus === "IN_PROGRESS" ? "default" : "secondary"
                            }
                          >
                            {candidate.phaseStatus}
                          </Badge>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex flex-wrap gap-1">
                          {candidate.actions.slice(0, 2).map(action => (
                            <Badge key={action.id} variant="outline" className="text-xs">
                              {action.type.replace(/_/g, ' ')}
                            </Badge>
                          ))}
                          {candidate.actions.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{candidate.actions.length - 2}
                            </Badge>
                          )}
                          {candidate.actions.length === 0 && (
                            <span className="text-sm text-gray-400">No actions</span>
                          )}
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center gap-2">
                          {!activePhase?.isClosed && (
                            <>
                              {activePhase?.type === "SCREENING" && !candidate.score && (
                                <Button size="sm" variant="outline" onClick={() => handleScoreCandidate(candidate.id)}>
                                  <TrendingUp className="w-3 h-3 mr-1" />
                                  Score
                                </Button>
                              )}
                              {activePhase?.type === "INTERVIEW" && (
                                <Button size="sm" variant="outline" onClick={() => handleSendInterview(candidate.id)}>
                                  <Send className="w-3 h-3 mr-1" />
                                  Interview
                                </Button>
                              )}
                            </>
                          )}
                          <Button size="sm" variant="ghost" onClick={() => handleViewCandidate(candidate)}>
                            <Eye className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="py-12 px-6 text-center">
                      <div className="flex flex-col items-center gap-3">
                        <Users className="w-12 h-12 text-gray-300" />
                        <div>
                          <h3 className="font-medium text-gray-900">No candidates in this phase</h3>
                          <p className="text-sm text-gray-500">
                            {activePhase?.order === 1 
                              ? "Upload CVs to begin the screening process"
                              : "Advance qualified candidates from previous phase"}
                          </p>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <Card>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">{phaseCandidates.length}</div>
              <div className="text-sm text-gray-600 mt-1">Total Candidates</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {phaseCandidates.filter(c => c.score).length}
              </div>
              <div className="text-sm text-gray-600 mt-1">Scored</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {phaseCandidates.filter(c => c.phaseStatus === "QUALIFIED").length}
              </div>
              <div className="text-sm text-gray-600 mt-1">Qualified</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">
                {phaseCandidates.filter(c => c.phaseStatus === "REJECTED").length}
              </div>
              <div className="text-sm text-gray-600 mt-1">Rejected</div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Modals */}
      {selectedCandidate && activePhase && (
        <PhaseCandidateDetailModal
          open={detailModalOpen}
          onOpenChange={setDetailModalOpen}
          candidate={selectedCandidate}
          phase={activePhase}
          onQualify={() => {
            setCandidatesByPhase(prev => {
              const updated = { ...prev }
              updated[activePhaseId] = updated[activePhaseId].map(c => 
                c.id === selectedCandidate.id ? { ...c, phaseStatus: "QUALIFIED" as CandidatePhaseStatus } : c
              )
              return updated
            })
            setDetailModalOpen(false)
          }}
          onReject={() => {
            setCandidatesByPhase(prev => {
              const updated = { ...prev }
              updated[activePhaseId] = updated[activePhaseId].map(c => 
                c.id === selectedCandidate.id ? { ...c, phaseStatus: "REJECTED" as CandidatePhaseStatus } : c
              )
              return updated
            })
            setDetailModalOpen(false)
          }}
          onScoreCandidate={() => handleScoreCandidate(selectedCandidate.id)}
          onSendInterview={() => handleSendInterview(selectedCandidate.id)}
          onSendEmail={() => handleBulkAction("EMAIL")}
        />
      )}
      
      <BulkActionModal
        open={bulkActionModalOpen}
        onOpenChange={setBulkActionModalOpen}
        actionType={bulkActionType}
        candidates={phaseCandidates.filter(c => selectedCandidates.includes(c.id)).map(c => ({
          id: c.id,
          name: c.name,
          email: c.email,
          avatar: c.avatar
        }))}
        onConfirm={handleBulkActionConfirm}
      />
    </div>
  )
}
