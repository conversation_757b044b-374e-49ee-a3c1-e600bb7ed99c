"use client"

import React, { useState } from 'react'
import { 
  X, 
  Mail, 
  Send, 
  Users,
  CheckCircle2,
  AlertCircle,
  Loader2
} from 'lucide-react'

type ActionType = "EMAIL" | "INTERVIEW" | "SCORE"

type Candidate = {
  id: string
  name: string
  email: string
  avatar?: string
}

const Button = ({ 
  children, 
  variant = "default", 
  size = "default",
  disabled = false,
  onClick,
  className = "",
  ...props 
}: {
  children: React.ReactNode
  variant?: "default" | "outline" | "ghost" | "destructive" | "success"
  size?: "default" | "sm" | "lg"
  disabled?: boolean
  onClick?: () => void
  className?: string
  [key: string]: any
}) => {
  const variants = {
    default: "bg-blue-600 text-white hover:bg-blue-700 border border-blue-600 shadow-sm",
    outline: "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 shadow-sm",
    ghost: "bg-transparent text-gray-700 hover:bg-gray-100 border-0",
    destructive: "bg-red-600 text-white hover:bg-red-700 border border-red-600 shadow-sm",
    success: "bg-green-600 text-white hover:bg-green-700 border border-green-600 shadow-sm"
  }
  
  const sizes = {
    default: "px-4 py-2 text-sm",
    sm: "px-3 py-1.5 text-xs",
    lg: "px-6 py-3 text-base"
  }
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${variants[variant]} ${sizes[size]} ${className}`}
      {...props}
    >
      {children}
    </button>
  )
}

type BulkActionModalProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
  actionType: ActionType
  candidates: Candidate[]
  onConfirm: (data: any) => Promise<void>
}

export function BulkActionModal({
  open,
  onOpenChange,
  actionType,
  candidates,
  onConfirm
}: BulkActionModalProps) {
  const [loading, setLoading] = useState(false)
  const [emailSubject, setEmailSubject] = useState("")
  const [emailBody, setEmailBody] = useState("")
  const [interviewTemplate, setInterviewTemplate] = useState("")

  if (!open) return null

  const handleSubmit = async () => {
    setLoading(true)
    try {
      const data = actionType === "EMAIL" 
        ? { subject: emailSubject, body: emailBody }
        : actionType === "INTERVIEW"
        ? { template: interviewTemplate }
        : {}
      
      await onConfirm(data)
      onOpenChange(false)
    } catch (error) {
      console.error('Bulk action failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const getTitle = () => {
    switch (actionType) {
      case "EMAIL": return "Send Bulk Email"
      case "INTERVIEW": return "Send Interview Links"
      case "SCORE": return "Score Candidates"
      default: return "Bulk Action"
    }
  }

  const getIcon = () => {
    switch (actionType) {
      case "EMAIL": return <Mail className="w-5 h-5 text-blue-600" />
      case "INTERVIEW": return <Send className="w-5 h-5 text-purple-600" />
      case "SCORE": return <CheckCircle2 className="w-5 h-5 text-green-600" />
    }
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={() => onOpenChange(false)} />
        
        <div className="relative bg-white rounded-xl shadow-2xl max-w-2xl w-full">
          {/* Header */}
          <div className="px-6 py-5 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 rounded-lg">
                  {getIcon()}
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">{getTitle()}</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    {candidates.length} candidate{candidates.length !== 1 ? 's' : ''} selected
                  </p>
                </div>
              </div>
              <button
                onClick={() => onOpenChange(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 py-6 space-y-6">
            {/* Selected Candidates */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                <Users className="w-4 h-4" />
                Selected Candidates
              </h3>
              <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-3 space-y-2">
                {candidates.map(candidate => (
                  <div key={candidate.id} className="flex items-center gap-3 p-2 bg-gray-50 rounded-lg">
                    <img 
                      src={candidate.avatar || `https://ui-avatars.com/api/?name=${candidate.name}&background=random`}
                      alt={candidate.name}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm text-gray-900">{candidate.name}</div>
                      <div className="text-xs text-gray-500">{candidate.email}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Action-specific fields */}
            {actionType === "EMAIL" && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Subject *
                  </label>
                  <input
                    type="text"
                    value={emailSubject}
                    onChange={(e) => setEmailSubject(e.target.value)}
                    placeholder="Enter email subject"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Body *
                  </label>
                  <textarea
                    value={emailBody}
                    onChange={(e) => setEmailBody(e.target.value)}
                    placeholder="Enter email message"
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Use {'{name}'} to personalize with candidate name
                  </p>
                </div>
              </div>
            )}

            {actionType === "INTERVIEW" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Interview Template *
                </label>
                <select
                  value={interviewTemplate}
                  onChange={(e) => setInterviewTemplate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select template...</option>
                  <option value="technical">Technical Interview (45 min)</option>
                  <option value="behavioral">Behavioral Interview (30 min)</option>
                  <option value="cultural">Cultural Fit (20 min)</option>
                </select>
                <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800">
                      Interview links will be sent via email to all selected candidates. They will be valid for 7 days.
                    </div>
                  </div>
                </div>
              </div>
            )}

            {actionType === "SCORE" && (
              <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-start gap-2">
                  <CheckCircle2 className="w-5 h-5 text-green-600 mt-0.5" />
                  <div className="text-sm text-green-800">
                    AI will automatically score {candidates.length} candidate{candidates.length !== 1 ? 's' : ''} based on their CVs and the job requirements. This may take a few minutes.
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 flex items-center justify-end gap-3">
            <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
              Cancel
            </Button>
            <Button 
              onClick={handleSubmit} 
              disabled={loading || (actionType === "EMAIL" && (!emailSubject || !emailBody)) || (actionType === "INTERVIEW" && !interviewTemplate)}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  {actionType === "EMAIL" ? "Send Emails" : actionType === "INTERVIEW" ? "Send Interviews" : "Start Scoring"}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
