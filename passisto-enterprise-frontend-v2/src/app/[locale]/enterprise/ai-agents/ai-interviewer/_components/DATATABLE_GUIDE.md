# DataTable Integration Guide

## Overview

This guide explains how to integrate shadcn/ui's DataTable component with TanStack Table for advanced features like sorting, filtering, and pagination.

## Files Created

1. **`src/components/ui/data-table.tsx`** - Reusable DataTable component
2. **`src/app/[locale]/enterprise/ai-agents/ai-interviewer/_components/cv-upload-columns.tsx`** - Column definitions
3. **`src/app/[locale]/enterprise/ai-agents/ai-interviewer/_components/cv-upload-section-with-datatable.tsx`** - Refactored component using DataTable

## Features Included

### ✅ Sorting
- Click on column headers to sort ascending/descending
- Multi-column sorting support
- Visual indicators (arrows) for sort direction

### ✅ Filtering
- Search/filter by candidate name
- Column visibility toggle
- Custom filter functions for status badges

### ✅ Pagination
- Configurable page sizes
- Previous/Next navigation
- Row selection persistence across pages

### ✅ Row Selection
- Select individual rows via checkbox
- Select all rows on current page
- Bulk actions on selected rows
- Selection state management

### ✅ Responsive Design
- Horizontal scrolling for overflow
- Truncated text with tooltips
- Mobile-friendly column widths

## How to Use

### 1. Replace the Current Component

To use the new DataTable version, replace the import in your page:

```tsx
// Before
import { CvUploadSection } from "./_components/cv-upload-section"

// After
import { CvUploadSection } from "./_components/cv-upload-section-with-datatable"
```

### 2. Column Customization

Edit `cv-upload-columns.tsx` to customize columns:

```tsx
{
  accessorKey: "name",
  header: ({ column }) => {
    return (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Candidate Name
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    )
  },
  cell: ({ row }) => (
    <div className="font-medium max-w-[200px] truncate">
      {row.getValue("name")}
    </div>
  ),
}
```

### 3. Advanced Filtering

Add custom filters to columns:

```tsx
{
  accessorKey: "status",
  filterFn: (row, id, value) => {
    return value.includes(row.getValue(id))
  },
}
```

### 4. Pagination Configuration

Modify initial page size in `data-table.tsx`:

```tsx
const table = useReactTable({
  data,
  columns,
  getPaginationRowModel: getPaginationRowModel(),
  initialState: {
    pagination: {
      pageSize: 10, // Change default page size
    },
  },
  // ...
})
```

## Best Practices

### 1. Column Definitions
- Keep column definitions in a separate file
- Use `React.useMemo` for columns to prevent re-renders
- Pass action handlers via a factory function

### 2. Performance
- Use `enableRowSelection` to control which rows can be selected
- Implement virtual scrolling for large datasets (>1000 rows)
- Use `getRowId` for stable row identification

### 3. Accessibility
- Include `aria-label` on interactive elements
- Use semantic HTML in cell renderers
- Ensure keyboard navigation works

### 4. Type Safety
- Define proper TypeScript types for your data
- Use `ColumnDef<TData, TValue>` for type-safe columns
- Leverage type inference for better DX

## Advanced Features

### Multi-Select Actions

The current implementation supports:
- **Upload Selected**: Upload only selected pending/failed files
- **Retry Selected**: Retry failed uploads for selected files
- **Remove Selected**: Delete selected candidates

Access selected rows:

```tsx
const selectedRows = table.getSelectedRowModel().rows
const selectedData = selectedRows.map(row => row.original)
```

### Custom Cell Renderers

Create rich cell content:

```tsx
cell: ({ row }) => {
  const status = row.getValue("status")
  return (
    <Badge variant={status === "Uploaded" ? "default" : "destructive"}>
      {status}
    </Badge>
  )
}
```

### Server-Side Operations

For server-side pagination/sorting/filtering:

```tsx
const table = useReactTable({
  data,
  columns,
  manualPagination: true,
  manualSorting: true,
  manualFiltering: true,
  onPaginationChange: setPagination,
  onSortingChange: setSorting,
  onColumnFiltersChange: setColumnFilters,
  pageCount: serverPageCount,
  state: {
    pagination,
    sorting,
    columnFilters,
  },
})
```

## Comparison: Before vs After

### Before (Manual Table)
- ❌ No built-in sorting
- ❌ No column visibility toggle
- ❌ No search/filter UI
- ❌ Manual pagination logic
- ❌ Manual selection state management
- ✅ Simple implementation

### After (DataTable)
- ✅ Built-in sorting with visual indicators
- ✅ Column visibility toggle
- ✅ Search/filter UI built-in
- ✅ Automatic pagination
- ✅ Managed selection state
- ✅ More features, better UX

## Migration Steps

1. **Install DataTable** (Already done - `@tanstack/react-table` is installed)

2. **Create Column Definitions**
   - Move column logic to `cv-upload-columns.tsx`
   - Add sorting, filtering as needed

3. **Update Component**
   - Replace manual table with `<DataTable />`
   - Update action handlers to work with row selection
   - Remove manual state management for selection

4. **Test Features**
   - ✅ Sorting works on all columns
   - ✅ Search filters candidates
   - ✅ Pagination navigates correctly
   - ✅ Selection persists correctly
   - ✅ Bulk actions work with selected rows

5. **Customize Styling**
   - Adjust column widths in column definitions
   - Modify DataTable styles in `data-table.tsx`
   - Update Card/Container styling as needed

## Troubleshooting

### Selection Not Working
Make sure column has `enableRowSelection` set correctly:
```tsx
{
  id: "select",
  enableSorting: false,
  enableHiding: false,
}
```

### Sorting Not Applied
Ensure `getSortedRowModel` is included:
```tsx
const table = useReactTable({
  getSortedRowModel: getSortedRowModel(),
  // ...
})
```

### Data Not Updating
Use proper state updates:
```tsx
setUploadedFiles((prev) => [...prev, newFile]) // ✅
setUploadedFiles(uploadedFiles.concat(newFile)) // ❌ (mutation)
```

## Resources

- [TanStack Table Docs](https://tanstack.com/table/v8/docs/guide/introduction)
- [shadcn/ui DataTable Guide](https://ui.shadcn.com/docs/components/data-table)
- [Example Implementation](./cv-upload-section-with-datatable.tsx)
