"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { FileText } from "lucide-react"
import { DataTable } from "@/components/ui/data-table"
import { createColumns, type UploadedFile } from "./cv-upload-columns"

type CvUploadSectionProps = {
  projectId: string
  onNext: () => void
}

export function CvUploadSection({ projectId, onNext }: CvUploadSectionProps) {
  const [uploadedFiles, setUploadedFiles] = React.useState<UploadedFile[]>([])
  const [selectedFiles, setSelectedFiles] = React.useState<UploadedFile[]>([])
  const [isDragging, setIsDragging] = React.useState(false)
  const [loadingExisting, setLoadingExisting] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [autoScore, setAutoScore] = React.useState(false)
  const [hasScreeningPhase, setHasScreeningPhase] = React.useState(false)
  const [screeningPhaseId, setScreeningPhaseId] = React.useState<number | null>(null)
  const [loadingPhases, setLoadingPhases] = React.useState(false)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (files) {
      handleFiles(Array.from(files))
    }
  }

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    setIsDragging(false)
    const files = event.dataTransfer.files
    if (files) {
      handleFiles(Array.from(files))
    }
  }

  // Generate SHA-256 hash for a file
  const generateFileHash = async (file: File): Promise<string> => {
    const buffer = await file.arrayBuffer()
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  const handleFiles = async (files: File[]) => {
    const newFiles: UploadedFile[] = []
    const duplicates: string[] = []
    
    for (const file of files) {
      try {
        // Generate hash for the new file
        const fileHash = await generateFileHash(file)
        const fileSize = file.size
        
        // Check for duplicates by name, size, and hash
        const isDuplicate = uploadedFiles.some(
          (existingFile) =>
            existingFile.name === file.name ||
            (existingFile.fileHash === fileHash && existingFile.fileSize === fileSize)
        )
        
        if (isDuplicate) {
          duplicates.push(file.name)
        } else {
          newFiles.push({
            id: `${file.name}-${Date.now()}-${Math.random()}`,
            name: file.name,
            cvFilename: file.name,
            status: "Pending",
            file,
            fileHash,
            fileSize,
          })
        }
      } catch (err) {
        console.error(`Error processing file ${file.name}:`, err)
        setError(`Failed to process ${file.name}`)
      }
    }
    
    // Show warning if duplicates were found
    if (duplicates.length > 0) {
      setError(`Duplicate files detected and skipped: ${duplicates.join(', ')}`)
      setTimeout(() => setError(null), 5000)
    }
    
    if (newFiles.length > 0) {
      setUploadedFiles((prev) => [...prev, ...newFiles])
    }
  }

  // upload a single File via FormData to POST /projects/{id}/candidates
  const uploadFile = async (file: File, existingId?: string) => {
    const id = existingId ?? `${file.name}-${Date.now()}-${Math.random()}`
    
    // Generate hash before upload to check server-side duplicates
    let fileHash: string | undefined
    let fileSize: number | undefined
    
    try {
      fileHash = await generateFileHash(file)
      fileSize = file.size
      
      // Check if this exact file (by hash) has already been uploaded
      const alreadyUploaded = uploadedFiles.find(
        (f) => f.fileHash === fileHash && f.fileSize === fileSize && f.status === "Uploaded"
      )
      
      if (alreadyUploaded) {
        setUploadedFiles((prev) =>
          prev.map((f) =>
            f.id === id
              ? {
                  ...f,
                  status: "Failed",
                  serverMessage: "Duplicate file already uploaded",
                  timestamp: new Date().toISOString(),
                }
              : f
          )
        )
        return
      }
    } catch (err) {
      console.error("Error generating file hash:", err)
    }

    // mark as uploading and ensure file is stored on the entry
    setUploadedFiles((prev) => {
      const idx = prev.findIndex((p) => p.id === id)
      if (idx >= 0) {
        const copy = [...prev]
        copy[idx] = { ...copy[idx], status: "Uploading", file, fileHash, fileSize }
        return copy
      }
      return [{ id, name: file.name, status: "Uploading", file, fileHash, fileSize }, ...prev]
    })

    const form = new FormData()
    form.append("files", file)
    form.append("auto_score", autoScore.toString())

    try {
      const res = await fetch(`/api-hiring/v1/projects/${projectId}/candidates`, {
        method: "POST",
        body: form,
      })

      const now = new Date().toISOString()

      if (!res.ok) {
        let msg = `Server responded ${res.status}`
        try {
          const json = await res.json()
          msg = json?.message || JSON.stringify(json)
        } catch (e) {
          // ignore
        }
        setUploadedFiles((prev) => prev.map((f) => (f.id === id ? { ...f, status: "Failed", serverMessage: msg, timestamp: now } : f)))
        return
      }

      let body: any = null
      try {
        body = await res.json()
      } catch (e) {
        body = null
      }

      const serverMessage = body ? JSON.stringify(body) : "Uploaded"
      setUploadedFiles((prev) => prev.map((f) => (f.id === id ? { ...f, status: "Uploaded", serverMessage, timestamp: now } : f)))
      fetchExistingCandidates()
    } catch (err: any) {
      const msg = err?.message || "Network error"
      const now = new Date().toISOString()
      setUploadedFiles((prev) => prev.map((f) => (f.id === id ? { ...f, status: "Failed", serverMessage: msg, timestamp: now } : f)))
    }
  }

  const retryUpload = (fileName: string) => {
    const fileEntry = uploadedFiles.find((p) => p.name === fileName)
    if (fileEntry?.file) {
      uploadFile(fileEntry.file, fileEntry.id)
      return
    }

    const prompt = window.confirm("To retry upload, please re-select the file from your computer. Open file dialog now?")
    if (prompt) {
      const input = document.createElement("input")
      input.type = "file"
      input.accept = ".pdf,.doc,.docx"
      input.onchange = () => {
        if (input.files && input.files[0]) {
          const f = input.files[0]
          if (f.name !== fileName) {
            if (!window.confirm(`You selected ${f.name}. Retry upload for this file instead of ${fileName}?`)) return
          }
          uploadFile(f)
        }
      }
      input.click()
    }
  }

  const removeFile = (id: string) => {
    const file = uploadedFiles.find((p) => p.id === id)
    if (!file) return

    const matchById = file.id.startsWith("cand-") ? file.id.replace(/^cand-/, "") : null
    const matchByName = file.name.startsWith("candidate:") ? file.name.replace(/^candidate:/, "") : null
    const candidateId = matchById || matchByName

    if (candidateId) {
      ;(async () => {
        try {
          const res = await fetch(`/api-hiring/v1/projects/${projectId}/candidates/${candidateId}`, {
            method: "DELETE",
          })
          if (!res.ok) {
            let msg = `Server responded ${res.status}`
            try {
              const json = await res.json()
              msg = json?.message || JSON.stringify(json)
            } catch (e) {
              // ignore
            }
            setError(msg)
            return
          }

          setUploadedFiles((prev) => prev.filter((p) => p.id !== id))
        } catch (err: any) {
          setError(err?.message || String(err))
        }
      })()
    } else {
      setUploadedFiles((prev) => prev.filter((p) => p.id !== id))
    }
  }

  const fetchExistingCandidates = async () => {
    setLoadingExisting(true)
    setError(null)
    try {
      const res = await fetch(`/api-hiring/v1/projects/${projectId}/candidates`)
      if (!res.ok) {
        throw new Error(`Failed to fetch existing candidates: ${res.status}`)
      }
      const body = await res.json()
      if (Array.isArray(body)) {
        const mapped = body.map((c: any) => {
          // Extract screening score if it exists
          let screeningScore: number | null = null
          if (screeningPhaseId && c.phaseScores && Array.isArray(c.phaseScores)) {
            console.log('Candidate:', c.name, 'phaseScores:', c.phaseScores, 'screeningPhaseId:', screeningPhaseId)
            const screeningPhaseScore = c.phaseScores.find((ps: any) => ps.phaseId === screeningPhaseId)
            console.log('Found screening score:', screeningPhaseScore)
            screeningScore = screeningPhaseScore?.score ?? null
          } else {
            console.log('Skipping score extraction - screeningPhaseId:', screeningPhaseId, 'phaseScores:', c.phaseScores)
          }
          
          return {
            id: `cand-${c.id}`, 
            name: c.name || c.fullName || 'N/A' ||`Candidate ${c.id}`,
            cvFilename: c.cvFilename || c.cvOriginalName || 'N/A',
            status: "Uploaded" as const,
            currentStatus: c.current_status || c.currentStatus || c.status || 'N/A',
            serverMessage: JSON.stringify(c), 
            timestamp: c.created_at || c.createdAt || c.applicationDate || new Date().toISOString(),
            fileHash: c.cvFileHash,
            fileSize: c.cvFileSize,
            screeningScore: screeningScore
          }
        })
        
        setUploadedFiles((prev) => {
          // Create a map of existing files for quick lookup
          const existingMap = new Map(prev.map(f => [f.id, f]))
          
          // Update existing files with scores or add new ones
          mapped.forEach((m: any) => {
            if (existingMap.has(m.id)) {
              // Update existing file with screening score
              const existing = existingMap.get(m.id)!
              existingMap.set(m.id, { ...existing, screeningScore: m.screeningScore })
            } else {
              // Add new file
              existingMap.set(m.id, m)
            }
          })
          
          const newState = Array.from(existingMap.values())
          console.log('Updated uploadedFiles with scores:', newState.map(f => ({ name: f.name, score: f.screeningScore })))
          return newState
        })
      }
    } catch (err: any) {
      setError(err?.message || String(err))
    } finally {
      setLoadingExisting(false)
    }
  }

  // Fetch project phases to determine if screening phase exists
  const fetchProjectPhases = async () => {
    setLoadingPhases(true)
    try {
      const res = await fetch(`/api-hiring/v1/phases/project/${projectId}`)
      if (!res.ok) {
        throw new Error(`Failed to fetch project phases: ${res.status}`)
      }
      const phases = await res.json()
      console.log('Fetched phases:', phases)
      if (Array.isArray(phases) && phases.length > 0) {
        // Sort by phaseOrder to get the first phase
        const sortedPhases = phases.sort((a: any, b: any) => a.phaseOrder - b.phaseOrder)
        const firstPhase = sortedPhases[0]
        console.log('First phase:', firstPhase, 'Type:', firstPhase.phaseType)
        
        // Check if first phase is a SCREENING phase
        if (firstPhase.phaseType === 'SCREENING') {
          setHasScreeningPhase(true)
          setScreeningPhaseId(firstPhase.id)
          console.log('Screening phase found with ID:', firstPhase.id)
        } else {
          setHasScreeningPhase(false)
          setScreeningPhaseId(null)
          console.log('First phase is not SCREENING, it is:', firstPhase.phaseType)
        }
      }
    } catch (err: any) {
      console.error('Failed to fetch phases:', err)
      setHasScreeningPhase(false)
    } finally {
      setLoadingPhases(false)
    }
  }

  // Handle row selection changes from DataTable
  const handleRowSelectionChange = React.useCallback((selected: UploadedFile[]) => {
    setSelectedFiles(selected)
  }, [])

  const uploadAll = () => {
    const toUpload = uploadedFiles.filter((f) => (f.status === "Pending" || f.status === "Failed") && f.file)
    toUpload.forEach((f) => {
      if (f.file) uploadFile(f.file, f.id)
    })
  }

  const uploadSelected = () => {
    const toUpload = selectedFiles.filter((f) => (f.status === "Pending" || f.status === "Failed") && f.file)
    toUpload.forEach((f) => f.file && uploadFile(f.file, f.id))
  }

  const retrySelected = () => {
    const toRetry = selectedFiles.filter((f) => f.status === "Failed")
    toRetry.forEach((f) => {
      if (f.file) uploadFile(f.file, f.id)
      else retryUpload(f.name)
    })
  }

  const removeSelected = async () => {
    for (const f of selectedFiles) {
      await new Promise<void>((res) => {
        removeFile(f.id)
        setTimeout(() => res(), 100)
      })
    }
  }

  React.useEffect(() => {
    fetchProjectPhases()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  React.useEffect(() => {
    if (screeningPhaseId !== null || loadingPhases === false) {
      fetchExistingCandidates()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [screeningPhaseId, loadingPhases])

  // Handler to view CV
  const viewCV = (file: UploadedFile) => {
    if (file.file) {
      // Create a temporary URL for the file blob
      const url = URL.createObjectURL(file.file)
      window.open(url, '_blank')
      // Clean up the URL after some time
      setTimeout(() => URL.revokeObjectURL(url), 60000)
    } else if (file.cvUrl || file.cvPath) {
      // Open the server-hosted CV
      const cvUrl = file.cvUrl || file.cvPath
      window.open(cvUrl, '_blank')
    }
  }

  // Create columns with action handlers
  const columns = React.useMemo(
    () => createColumns({
      onRetry: retryUpload,
      onRemove: removeFile,
      onViewCV: viewCV,
      showScreeningScore: hasScreeningPhase,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [hasScreeningPhase]
  )

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Upload Candidate CVs</CardTitle>
        <CardDescription>
          Drag and drop your candidate CVs (PDF, DOC, DOCX) here. Files are uploaded to the project and processed server-side.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-2">
          <Checkbox 
            id="auto-score" 
            checked={autoScore}
            disabled={!hasScreeningPhase}
            onCheckedChange={(checked) => setAutoScore(checked === true)}
          />
          <Label 
            htmlFor="auto-score" 
            className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${hasScreeningPhase ? 'cursor-pointer' : 'cursor-not-allowed opacity-70'}`}
            title={!hasScreeningPhase ? 'Auto-scoring is only available when the project has a screening phase as the first phase' : undefined}
          >
            Automatically score candidates after parsing CVs
            {!hasScreeningPhase && <span className="text-muted-foreground ml-2">(Requires screening phase)</span>}
          </Label>
        </div>
        
        <div
          className={`flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
            isDragging ? "border-primary bg-primary/5" : "border-muted-foreground/20"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <FileText className="mb-4 h-12 w-12 text-muted-foreground" />
          <p className="text-lg font-medium text-muted-foreground">Drag & Drop your CVs here</p>
          <p className="text-sm text-muted-foreground">or</p>
          <Label htmlFor="file-upload" className="cursor-pointer text-primary hover:underline">
            Browse files
          </Label>
          <Input id="file-upload" type="file" multiple accept=".pdf,.doc,.docx" className="hidden" onChange={handleFileChange} />
        </div>

        {loadingExisting && <div className="text-sm text-muted-foreground">Loading existing uploads...</div>}

        {error && <div className="text-sm text-destructive">{error}</div>}

        {uploadedFiles.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Uploaded Files</h3>
              <div className="flex gap-2">
                <Button 
                  onClick={uploadAll} 
                  size="sm"
                  disabled={!uploadedFiles.some((f) => (f.status === "Pending" || f.status === "Failed") && f.file)}
                >
                  Upload All
                </Button>
                <Button 
                  onClick={uploadSelected} 
                  variant="secondary" 
                  size="sm"
                  disabled={selectedFiles.length === 0 || !selectedFiles.some((f) => (f.status === "Pending" || f.status === "Failed") && f.file)}
                >
                  Upload Selected
                </Button>
                <Button 
                  onClick={retrySelected} 
                  variant="ghost" 
                  size="sm"
                  disabled={selectedFiles.filter(f => f.status === "Failed").length === 0}
                >
                  Retry Selected
                </Button>
                <Button 
                  onClick={removeSelected} 
                  variant="destructive" 
                  size="sm"
                  disabled={selectedFiles.length === 0}
                >
                  Remove Selected
                </Button>
                <Button 
                  onClick={fetchExistingCandidates} 
                  variant="ghost"
                  size="sm"
                >
                  Refresh
                </Button>
              </div>
            </div>
            
            <DataTable 
              columns={columns} 
              data={uploadedFiles}
              searchKey="name"
              searchPlaceholder="Search by candidate name..."
              onRowSelectionChange={handleRowSelectionChange}
            />
          </div>
        )}
      </CardContent>
    </Card>
  )
}
