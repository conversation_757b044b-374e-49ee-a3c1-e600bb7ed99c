# Batch Actions Fix - Summary

## Problem
The batch actions (Upload Selected, Retry Selected, Remove Selected) were not working because:
1. The `tableRef` was attempting to access the table instance, but the DataTable component didn't expose it
2. The `getSelectedFiles()` function couldn't properly access the row selection state from the DataTable

## Solution
Implemented a proper callback pattern to communicate selected rows from DataTable to parent component:

### Changes to `data-table.tsx`

1. **Added `onRowSelectionChange` prop:**
```tsx
interface DataTableProps<TData, TValue> {
  // ... existing props
  onRowSelectionChange?: (selectedRows: TData[]) => void
}
```

2. **Added useEffect to notify parent of selection changes:**
```tsx
React.useEffect(() => {
  if (onRowSelectionChange) {
    const selectedRows = table.getFilteredSelectedRowModel().rows.map(row => row.original)
    onRowSelectionChange(selectedRows)
  }
}, [rowSelection, table, onRowSelectionChange])
```

### Changes to `cv-upload-section-with-datatable.tsx`

1. **Replaced `tableRef` with `selectedFiles` state:**
```tsx
const [selectedFiles, setSelectedFiles] = React.useState<UploadedFile[]>([])
```

2. **Removed the broken `getSelectedFiles()` function**

3. **Added callback handler:**
```tsx
const handleRowSelectionChange = React.useCallback((selected: UploadedFile[]) => {
  setSelectedFiles(selected)
}, [])
```

4. **Updated all batch action buttons to use `selectedFiles`:**
   - `uploadSelected`: Uses `selectedFiles` instead of `getSelectedFiles()`
   - `retrySelected`: Uses `selectedFiles` instead of `getSelectedFiles()`
   - `removeSelected`: Uses `selectedFiles` instead of `getSelectedFiles()`

5. **Connected DataTable to callback:**
```tsx
<DataTable 
  columns={columns} 
  data={uploadedFiles}
  searchKey="name"
  searchPlaceholder="Search by candidate name..."
  onRowSelectionChange={handleRowSelectionChange}
/>
```

## How It Works Now

1. User selects rows via checkboxes in the DataTable
2. DataTable's internal `rowSelection` state updates
3. The `useEffect` in DataTable detects the change
4. Callback `onRowSelectionChange` is invoked with selected row data
5. Parent component receives selected rows and updates `selectedFiles` state
6. Batch action buttons react to `selectedFiles` state changes
7. User can perform batch actions on the selected files

## Features Working

✅ **Upload Selected** - Uploads only selected pending/failed files
✅ **Retry Selected** - Retries failed uploads for selected files  
✅ **Remove Selected** - Deletes selected candidates
✅ **Selection State** - Properly tracked and synchronized
✅ **Button States** - Correctly enabled/disabled based on selection
✅ **Row Count Display** - Shows "X of Y row(s) selected" at bottom of table

## Testing Checklist

- [ ] Select individual rows and verify batch actions work
- [ ] Select all rows and verify batch actions work
- [ ] Upload selected files (only pending/failed)
- [ ] Retry selected failed uploads
- [ ] Remove selected files
- [ ] Verify selection persists when switching pages
- [ ] Verify selection updates when data changes
- [ ] Check that disabled states work correctly
