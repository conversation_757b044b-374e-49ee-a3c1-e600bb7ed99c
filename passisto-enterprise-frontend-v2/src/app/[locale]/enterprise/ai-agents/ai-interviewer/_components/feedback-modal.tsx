"use client";

import React from 'react';
import { X, MessageSquare } from 'lucide-react';

type FeedbackModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  feedback: any;
  candidateName: string;
};

const renderFeedbackContent = (feedback: any) => {
    if (!feedback) {
      return <p className="text-sm text-gray-500">No feedback available.</p>;
    }

    if (typeof feedback === 'string') {
      return <p className="text-sm text-gray-700 leading-relaxed">{feedback}</p>;
    }

    if (typeof feedback === 'object') {
      return (
        <div className="space-y-4 text-sm">
          {/* Handle score and reasoning structure */}
          {feedback.score !== undefined && (
            <div>
              <h4 className="font-semibold text-gray-800">Score</h4>
              <p className="mt-1 text-gray-600">{feedback.score}/100</p>
            </div>
          )}
          {feedback.reasoning && (
            <div>
              <h4 className="font-semibold text-gray-800">Reasoning</h4>
              <p className="mt-1 text-gray-600 whitespace-pre-wrap">{typeof feedback.reasoning === 'string' ? feedback.reasoning : JSON.stringify(feedback.reasoning, null, 2)}</p>
            </div>
          )}
          
          {/* Handle recommendation structure */}
          {feedback.recommendation && (
            <div>
              <h4 className="font-semibold text-gray-800">Recommendation</h4>
              <p className="mt-1 text-gray-600">{typeof feedback.recommendation === 'string' ? feedback.recommendation : JSON.stringify(feedback.recommendation, null, 2)}</p>
            </div>
          )}
          
          {/* Handle pros and cons */}
          {feedback.pros && Array.isArray(feedback.pros) && (
            <div>
              <h4 className="font-semibold text-gray-800">Pros</h4>
              <ul className="list-disc list-inside mt-1 text-gray-600 space-y-1">
                {feedback.pros.map((pro: string, index: number) => <li key={index}>{pro}</li>)}
              </ul>
            </div>
          )}
          {feedback.cons && Array.isArray(feedback.cons) && (
            <div>
              <h4 className="font-semibold text-gray-800">Cons</h4>
              <ul className="list-disc list-inside mt-1 text-gray-600 space-y-1">
                {feedback.cons.map((con: string, index: number) => <li key={index}>{con}</li>)}
              </ul>
            </div>
          )}
          
          {/* Handle detailed scores */}
          {feedback.scores && typeof feedback.scores === 'object' && (
             <div>
              <h4 className="font-semibold text-gray-800">Detailed Scores</h4>
              <div className="grid grid-cols-2 gap-x-4 gap-y-2 mt-1">
                {Object.entries(feedback.scores).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-gray-600 capitalize">{key.replace(/_/g, ' ')}:</span>
                    <span className="font-medium text-gray-800">{value as number}/10</span>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* Handle flags */}
          {feedback.flags && Array.isArray(feedback.flags) && feedback.flags.length > 0 && (
            <div>
              <h4 className="font-semibold text-gray-800">Flags</h4>
              <div className="flex flex-wrap gap-2 mt-1">
                {feedback.flags.map((flag: string, index: number) => (
                    <span key={index} className="text-xs font-medium bg-red-100 text-red-800 px-2 py-1 rounded-full">{flag}</span>
                ))}
              </div>
            </div>
          )}
          
          {/* Fallback: Display raw JSON if none of the above matched */}
          {!feedback.score && !feedback.reasoning && !feedback.recommendation && !feedback.pros && !feedback.cons && !feedback.scores && !feedback.flags && (
            <div>
              <h4 className="font-semibold text-gray-800">Raw Feedback</h4>
              <pre className="mt-1 text-xs text-gray-600 bg-gray-100 p-3 rounded overflow-x-auto">
                {JSON.stringify(feedback, null, 2)}
              </pre>
            </div>
          )}
        </div>
      );
    }

    return <p className="text-sm text-gray-500">Feedback format not supported.</p>;
  };


export function FeedbackModal({ open, onOpenChange, feedback, candidateName }: FeedbackModalProps) {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={() => onOpenChange(false)} />
        
        <div className="relative bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
          {/* Header */}
          <div className="px-6 py-5 border-b border-gray-200">
            <div className="flex items-start justify-between">
                <div>
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                        <MessageSquare className="w-5 h-5 text-blue-600" />
                        AI Feedback
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">For {candidateName}</p>
                </div>
              <button
                onClick={() => onOpenChange(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto px-6 py-6 bg-gray-50/50">
            {renderFeedbackContent(feedback)}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex justify-end">
                <button
                    onClick={() => onOpenChange(false)}
                    className="px-4 py-2 text-sm font-medium bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 shadow-sm rounded-lg"
                >
                    Close
                </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
