"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Plus,
  Trash2,
  GripVertical,
  Clock,
  CheckCircle,
  AlertCircle,
  Settings,
  ArrowUp,
  ArrowDown,
  Calendar
} from "lucide-react"
import { toast } from "sonner";
import {
  InterviewPhase,
  InterviewPhaseType,
  InterviewPhaseConfiguration,
  InterviewPhaseStatus,
  generatePhaseId,
  calculateTotalDuration,
  getPhaseTypeLabel
} from "@/lib/utils"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useSelector, useDispatch } from 'react-redux'
import { RootState, AppDispatch } from '@/store'
import { PHASE_CREATE, PHASE_DELETE, PHASE_LIST, PHASE_PATCH, PHASE_SUGGEST } from "@/utils/hiring.routes"
import axiosHiringInstance from "@/config/axios-hiring"
import { createPhase, deletePhase, fetchPhases, suggestPhaseDates, updatePhase } from "@/store/slices/phaseSlice"
import { useAuth } from "@clerk/nextjs";



type InterviewPhaseConfigurationProps = {
  projectId: string
  onNext: () => void
  onSave?: (configuration: InterviewPhaseConfiguration) => void
  onPhasesConfigured?: (phases: InterviewPhase[]) => void
}

export function InterviewPhaseConfigurationSection({
  projectId,
  onNext,
  onSave,
  onPhasesConfigured
}: InterviewPhaseConfigurationProps) {
  const [phases, setPhases] = React.useState<InterviewPhase[]>([])
    const dispatch = useDispatch<AppDispatch>()
   const phasesFromStore = useSelector((state: RootState) => state.phases.phases)
  const loading = useSelector((state: RootState) => state.phases.loading)
  const error = useSelector((state: RootState) => state.phases.error)
   const { getToken } = useAuth();


  // 🔹 Synchroniser le state local avec Redux
React.useEffect(() => {
  if (!projectId) return;

  const fetchData = async () => {
    try {
      const token = await getToken(); // <-- récupère le token de façon asynchrone
      if (!token) throw new Error("No authentication token");
      // dispatch avec token et projectId
      dispatch(fetchPhases({ token, projectId }));
    } catch (err) {
      console.error("❌ Failed to fetch phases:", err);
      toast.error("Failed to fetch phases");
    }
  };

  fetchData();
}, [projectId, dispatch, getToken]);


  React.useEffect(() => {
    setPhases(phasesFromStore)
  }, [phasesFromStore])


  const [selectedPhaseId, setSelectedPhaseId] = React.useState<string | null>(null)
  const [showAdvancedSettings, setShowAdvancedSettings] = React.useState(false)

  const totalDuration = React.useMemo(() => calculateTotalDuration(phases), [phases])
  const selectedPhase = phases.find(p => p.id === selectedPhaseId)

  const [showErrors, setShowErrors] = React.useState(false);

  // Helper function to get date range info
  const getDateRangeInfo = React.useMemo(() => {
    const phasesWithDates = phases.filter(p => p.startDate && p.endDate)
    if (phasesWithDates.length === 0) return null

    const startDates = phasesWithDates.map(p => new Date(p.startDate!))
    const endDates = phasesWithDates.map(p => new Date(p.endDate!))

    const earliestStart = new Date(Math.min(...startDates.map(d => d.getTime())))
    const latestEnd = new Date(Math.max(...endDates.map(d => d.getTime())))

    return {
      start: earliestStart,
      end: latestEnd,
      duration: Math.ceil((latestEnd.getTime() - earliestStart.getTime()) / (1000 * 60 * 60 * 24))
    }
  }, [phases])


  // const fetchProjectPhases = async (projectId: string) => {
  //   try {
  //     const res = await axiosHiringInstance.get(PHASE_LIST(Number(projectId)));
  //     return res.data.map((phase: any) => ({
  //       id: String(phase.id),
  //       name: phase.name,
  //       description: phase.description,
  //       type: phase.phaseType,
  //       order: phase.phaseOrder,
  //       isRequired: phase.isRequired,
  //       estimatedDuration: phase.estimatedDuration,
  //       startDate: phase.startDate ? phase.startDate.slice(0, 16) : '',
  //       endDate: phase.endDate ? phase.endDate.slice(0, 16) : '',
  //       allowSkip: phase.allowSkip,
  //       requiresApproval: phase.requiresApproval,
  //       autoAdvance: phase.autoAdvance,
  //       passingScore: phase.passingScore,
  //       maxAttempts: phase.maxAttempts,
  //       status: phase.status,
  //       createdAt: phase.createdAt,
  //       updatedAt: phase.updatedAt,
  //     }));
  //   } catch (err) {
  //     console.error("❌ Error fetching project phases:", err);
  //     return [];
  //   }
  // };


  const handleAddPhase = () => {
    const newPhase: Partial<InterviewPhase> = {
      id: generatePhaseId(),
      name: "New Phase",
     type: 'APPLICATION',
     estimatedDuration: 30,
     passingScore: 50,
      maxAttempts: 1,
      status: 'PENDING',
      order: phases.length + 1,
    }
    setPhases(prev => [...prev, newPhase as InterviewPhase])
    setSelectedPhaseId(newPhase.id!)
  }

const handleDeletePhase = async (phaseId: string) => {
  // 1️⃣ Supprimer la phase localement pour le rendu immédiat
  setPhases(prev => {
    const filtered = prev.filter(p => p.id !== phaseId);
    return filtered.map((phase, index) => ({
      ...phase,
      order: index + 1,
      updatedAt: new Date().toISOString(),
    }));
  });

  // 2️⃣ Si ce n'est pas une phase locale, dispatcher vers le backend
  if (!phaseId.startsWith("local-")) {
    try {
      const token = await getToken(); // <-- récupérer le token ici
      if (!token) throw new Error("No authentication token");

      await dispatch(deletePhase({ token, phaseId })).unwrap();
      toast.success("Phase deleted successfully");
    } catch (err: any) {
      console.error(`Failed to delete phase ${phaseId}:`, err);
    }
  } else {
    // Notification pour les phases locales
    toast.success("Local phase deleted successfully");
  }

  // 3️⃣ Déselectionner si la phase supprimée était sélectionnée
  if (selectedPhaseId === phaseId) {
    setSelectedPhaseId(null);
  }
};




const handleMovePhase = async (phaseId: string, direction: "up" | "down") => {
  const currentIndex = phases.findIndex((p) => p.id === phaseId);
  if (currentIndex === -1) return;

  const newIndex = direction === "up" ? currentIndex - 1 : currentIndex + 1;
  if (newIndex < 0 || newIndex >= phases.length) return;

  // Déplacer la phase localement
  const newPhases = [...phases];
  const [movedPhase] = newPhases.splice(currentIndex, 1);
  newPhases.splice(newIndex, 0, movedPhase);

  // Réordonner l'ordre des phases
  const reorderedPhases = newPhases.map((phase, index) => ({
    ...phase,
    order: index + 1,
    updatedAt: new Date().toISOString(),
  }));

  setPhases(reorderedPhases);

  try {
    const token = await getToken(); // 🔹 récupérer le token
    if (!token) throw new Error("No authentication token");

    // Patch temporaire +1000 pour éviter conflits
    const patchTemp = reorderedPhases
      .filter(p => !p.id.startsWith("local-"))
      .map(p =>
        axiosHiringInstance.patch(
          PHASE_PATCH(Number(p.id)),
          { phaseOrder: p.order + 1000 },
          { headers: { Authorization: `Bearer ${token}` } }
        )
      );
    await Promise.all(patchTemp);

    // Patch final avec les ordres corrects
    const patchFinal = reorderedPhases
      .filter(p => !p.id.startsWith("local-"))
      .map(p =>
        axiosHiringInstance.patch(
          PHASE_PATCH(Number(p.id)),
          { phaseOrder: p.order },
          { headers: { Authorization: `Bearer ${token}` } }
        )
      );
    await Promise.all(patchFinal);
  } catch (err) {
    console.error("Failed to update phase order, refetching phases:", err);

    try {
      const token = await getToken();
      if (!token) throw new Error("No authentication token");
      const loadedPhases = await dispatch(fetchPhases({ token, projectId })).unwrap();
      setPhases(loadedPhases);
    } catch (err) {
      console.error("Failed to reload phases after move error:", err);
    }
  }
};




  const handleUpdatePhase = (phaseId: string, updates: Partial<InterviewPhase>) => {
    setPhases(prev => prev.map(phase =>
      phase.id === phaseId
        ? { ...phase, ...updates, updatedAt: new Date().toISOString() }
        : phase
    ))
  }


const suggestDatesForPhase = async (phaseId: string) => {
  try {
    const token = await getToken(); // 🔹 récupère le token
    if (!token) throw new Error("No authentication token");

    // Dispatch le thunk avec token et projectId
    const suggestedPhase = await dispatch(
      suggestPhaseDates({ token, projectId, phaseId })
    ).unwrap();

    const formatForInput = (dateStr?: string) => {
      if (!dateStr) return "";
      if (dateStr.length === 10) return `${dateStr}T00:00`;
      if (dateStr.length === 16) return dateStr;
      return dateStr.slice(0, 16);
    };

    const updatedPhase = {
      startDate: formatForInput(suggestedPhase.startDate),
      endDate: formatForInput(suggestedPhase.endDate)
    };

    setPhases(prev =>
      prev.map(p =>
        p.id === phaseId
          ? { ...p, ...updatedPhase, updatedAt: new Date().toISOString() }
          : p
      )
    );

  } catch (err: any) {
    console.error("❌ Failed to fetch suggested dates:", err);
    toast.error(err?.message || "Failed to fetch suggested dates");
  }
};






  const formatDateForBackend = (dateStr?: string) => {
    if (!dateStr) return null;

    // Si dateStr = "2026-01-01T00:00"
    // on complète les secondes
    if (!dateStr.includes('T')) {
      // date sans heure, par ex "2026-01-01"
      return `${dateStr}T00:00:00`;
    } else if (dateStr.length === 16) {
      // "YYYY-MM-DDTHH:mm" -> ajouter les secondes
      return `${dateStr}:00`;
    }
    return dateStr; // déjà au bon format
  }


  const mapPhaseForBackend = (phase: InterviewPhase) => ({
    name: phase.name || "",
    description: phase.description || "",
    phaseType: phase.type,
    phaseOrder: phase.order,
    isRequired: !!phase.isRequired,
    estimatedDuration: Number(phase.estimatedDuration) || 0,
    startDate: formatDateForBackend(phase.startDate),
    endDate: formatDateForBackend(phase.endDate),
    allowSkip: !!phase.allowSkip,
    requiresApproval: !!phase.requiresApproval,
    autoAdvance: !!phase.autoAdvance,
    passingScore: phase.passingScore ?? 0,
    maxAttempts: phase.maxAttempts ?? 1,
    status: phase.status 
  })
  const validatePhase = (phase: InterviewPhase) => {
    const errors: Record<string, string> = {};

    if (!phase.name?.trim()) errors.name = "Phase name is required";
    if (!phase.type) errors.type = "Phase type is required";
    if (!phase.description) errors.description = "Description is required";
    if (!phase.status) errors.status = "Status is required"
    if (!phase.startDate) errors.startDate = "Start date is required";
    if (!phase.endDate) errors.endDate = "End date is required";
    if (!phase.estimatedDuration || phase.estimatedDuration <= 0)
      errors.estimatedDuration = "Duration is required";
    if (!phase.passingScore) errors.passingScore = "PassingScore is required";
    if (!phase.maxAttempts) errors.maxAttempts = "maxAttempts is required";


    return errors;
  };


const handleSaveConfiguration = async () => {
  setShowErrors(true); // Affiche les erreurs côté UI

  // 1️⃣ Validation locale
  const hasError = phases.some(phase => {
    const errors = validatePhase(phase);
    return Object.keys(errors).length > 0;
  });

  if (hasError) {
    toast.error("Veuillez remplir tous les champs requis avant de sauvegarder.");
    return;
  }

  try {
    const token = await getToken(); // 🔹 Récupération du token
    if (!token) throw new Error("No authentication token");

    // 2️⃣ Sauvegarde des phases via Redux
    for (const phase of phases) {
      const backendPhase = mapPhaseForBackend(phase);

      if (!phase.id || isNaN(Number(phase.id)) || phase.id.startsWith("local-")) {
        // Création
        await dispatch(createPhase({ token, projectId, phase: backendPhase })).unwrap();
      } else {
        // Mise à jour
        await dispatch(updatePhase({ token, phaseId: phase.id, phase: backendPhase })).unwrap();
      }
    }

    toast.success("Phases saved successfully");

    // Callback après sauvegarde
    onSave?.({
      projectId,
      phases,
      totalEstimatedDuration: totalDuration,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
    onPhasesConfigured?.(phases);
    onNext();

  } catch (err: any) {
    console.error("❌ Error saving phases:", err);

    // 3️⃣ Gestion des erreurs du backend
    const status = err.response?.status;
    const data = err.response?.data;
    const message = data?.message || "Une erreur inattendue est survenue";

    switch (status) {
      case 400:
        toast.error(message || "Phase dates are outside the project bounds");
        break;
      case 404:
        toast.error(message || "Phase not found");
        break;
      case 409:
        toast.error(message || "Phase overlaps with another phase");
        break;
      default:
        toast.error(message);
        break;
    }

    // 4️⃣ Gestion des erreurs par champ si le backend les renvoie
    if (data?.fieldErrors) {
      const fieldErrors = data.fieldErrors; // { [phaseId]: { fieldName: message } }
      setPhases(prev =>
        prev.map(p => ({
          ...p,
          errors: fieldErrors[p.id] || {}
        }))
      );
    }
  }
};



  const phaseTypeOptions = [
    { value: "APPLICATION" as const, label: "Application" },
    { value: "SCREENING" as const, label: "Screening" },
    { value: "INTERVIEW" as const, label: "Interview" },
    { value: "ASSESSMENT" as const, label: "Assessment" },
    { value: "FINAL_REVIEW" as const, label: "Final Review" },
  ]

  const phaseStatusOptions = [
    { value: "PENDING" as const, label: "Pending" },
    { value: "ACTIVE" as const, label: "Active" },
    { value: "COMPLETED" as const, label: "Completed" },
    { value: "CANCELLED" as const, label: "Cancelled" },
  ]

  return (
    <div className="w-full max-w-6xl space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configure Interview Phases
          </CardTitle>
          <CardDescription>
            Set up multiple interview phases for your recruitment process. Each phase can have different
            requirements, scoring criteria, and approval workflows.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4 flex-wrap">
              <Badge variant="outline" className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                Total Duration: {totalDuration} minutes
              </Badge>
              <Badge variant="outline">
                {phases.length} Phase{phases.length !== 1 ? 's' : ''}
              </Badge>
              {getDateRangeInfo && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {getDateRangeInfo.start.toLocaleDateString()} - {getDateRangeInfo.end.toLocaleDateString()}
                  <span className="ml-1">({getDateRangeInfo.duration} days)</span>
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
            
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
              >
                <Settings className="h-4 w-4 mr-1" />
                {showAdvancedSettings ? 'Hide' : 'Show'} Advanced
              </Button>
              <Button onClick={handleAddPhase} size="sm">
                <Plus className="h-4 w-4 mr-1" />
                Add Phase
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Phase List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Interview Phases</CardTitle>
              <CardDescription>Click a phase to configure its settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              {phases.map((phase, index) => (
                <div
                  key={phase.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${selectedPhaseId === phase.id
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                    }`}
                  onClick={() => setSelectedPhaseId(phase.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <GripVertical className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium text-sm">{phase.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {getPhaseTypeLabel(phase.type)} • {phase.estimatedDuration}min
                        </div>
                        {(phase.startDate || phase.endDate) && (
                          <div className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                            <Calendar className="h-3 w-3" />
                            {phase.startDate && new Date(phase.startDate).toLocaleDateString()}
                            {phase.startDate && phase.endDate && " - "}
                            {phase.endDate && new Date(phase.endDate).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-1">
                      {phase.isRequired && (
                        <AlertCircle className="h-3 w-3 text-orange-500" />
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleMovePhase(phase.id, 'up')
                        }}
                        disabled={index === 0}
                        className="h-6 w-6 p-0"
                      >
                        <ArrowUp className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleMovePhase(phase.id, 'down')
                        }}
                        disabled={index === phases.length - 1}
                        className="h-6 w-6 p-0"
                      >
                        <ArrowDown className="h-3 w-3" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => e.stopPropagation()}
                            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete phase</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete <strong>{phase.name}</strong>?
                              This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              className="bg-destructive text-white hover:bg-destructive/90"
                              onClick={() => handleDeletePhase(phase.id)}
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>

                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Phase Configuration */}
        <div className="lg:col-span-2">
          {selectedPhase ? (
            <Card>
              <CardHeader>
                <CardTitle>Configure: {selectedPhase.name}</CardTitle>
                <CardDescription>
                  Customize the settings for this interview phase
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Settings */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* <div className="space-y-2">
                    <Label htmlFor="phase-name">Phase Name</Label>
                    <Input
                      id="phase-name"
                      value={selectedPhase.name}
                      onChange={(e) => handleUpdatePhase(selectedPhase.id, { name: e.target.value })}
                      placeholder="Enter phase name"
                    />
                  </div> */}
                  <div className="space-y-2">
                    <Label htmlFor="phase-name">Phase Name</Label>
                    <Input
                      id="phase-name"
                      value={selectedPhase.name}
                      onChange={(e) => handleUpdatePhase(selectedPhase.id, { name: e.target.value })}
                      placeholder="Enter phase name"
                      className={showErrors && !selectedPhase.name?.trim() ? "border-red-500" : ""}
                    />
                    {showErrors && !selectedPhase.name?.trim() && (
                      <p className="text-red-500 text-xs">Phase name is required</p>
                    )}
                  </div>

                  {/* <div className="space-y-2">
                    <Label htmlFor="phase-type">Phase Type</Label>
                    <Select
                      value={selectedPhase.type}
                      onValueChange={(value: InterviewPhaseType) =>
                        handleUpdatePhase(selectedPhase.id, { type: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {phaseTypeOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div> */}
                  <div className="space-y-2">
                    <Label htmlFor="phase-type">Phase Type</Label>
                    <Select
                      value={selectedPhase.type}
                      onValueChange={(value: InterviewPhaseType) =>
                        handleUpdatePhase(selectedPhase.id, { type: value })
                      }
                    >
                      <SelectTrigger
                        className={showErrors && !selectedPhase.type ? "border-red-500" : ""}
                      >
                        <SelectValue placeholder="Select a phase type" />
                      </SelectTrigger>
                      <SelectContent>
                        {phaseTypeOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {showErrors && !selectedPhase.type && (
                      <p className="text-red-500 text-xs">Phase type is required</p>
                    )}
                  </div>

                </div>

                <div className="space-y-2">
                  <Label htmlFor="phase-description">Description</Label>
                  <Textarea
                    id="phase-description"
                    value={selectedPhase.description || ""}
                    onChange={(e) =>
                      handleUpdatePhase(selectedPhase.id, { description: e.target.value })
                    }
                    placeholder="Describe what this phase evaluates"
                    rows={3}
                    className={showErrors && !selectedPhase.description?.trim() ? "border-red-500" : ""}
                  />
                  {showErrors && !selectedPhase.description?.trim() && (
                    <p className="text-red-500 text-xs">Description is required</p>
                  )}
                </div>


                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="duration">Estimated Duration (minutes)</Label>
                    <Input
                      id="duration"
                      type="number"
                      min="5"
                      max="180"
                      value={selectedPhase.estimatedDuration}
                      onChange={(e) =>
                        handleUpdatePhase(selectedPhase.id, {
                          estimatedDuration: parseInt(e.target.value) || 0
                        })
                      }
                      className={
                        showErrors && (!selectedPhase.estimatedDuration || selectedPhase.estimatedDuration <= 0)
                          ? "border-red-500"
                          : ""
                      }
                    />
                    {showErrors && (!selectedPhase.estimatedDuration || selectedPhase.estimatedDuration <= 0) && (
                      <p className="text-red-500 text-xs">Duration is required</p>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 pt-6">
                    <Switch
                      id="required"
                      checked={selectedPhase.isRequired}
                      onCheckedChange={(checked) => handleUpdatePhase(selectedPhase.id, { isRequired: checked })}
                    />
                    <Label htmlFor="required">Required Phase</Label>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phase-status">Phase Status</Label>
                  <Select
                    value={selectedPhase.status}
                    onValueChange={(value: InterviewPhaseStatus) =>
                      handleUpdatePhase(selectedPhase.id, { status: value })
                    }
                  >
                    <SelectTrigger
                      className={showErrors && !selectedPhase.status ? "border-red-500" : ""}
                    >
                      <SelectValue placeholder="Select phase status" />
                    </SelectTrigger>
                    <SelectContent>
                      {phaseStatusOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {showErrors && !selectedPhase.status && (
                    <p className="text-red-500 text-xs">Phase status is required</p>
                  )}
                  <p className="text-xs text-muted-foreground ml-1">
                    Set the current status of this phase
                  </p>
                </div>


                {/* Date Configuration */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-primary" />
                      <h4 className="font-medium">Schedule</h4>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => suggestDatesForPhase(selectedPhase.id)}
                    >
                      Auto-suggest Dates
                    </Button>


                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Start Date */}
                    <div className="space-y-2">
                      <Label htmlFor="start-date">Start Date & Time</Label>
                      <Input
                        id="start-date"
                        type="datetime-local"
                        value={selectedPhase.startDate || ""}
                        onChange={(e) => handleUpdatePhase(selectedPhase.id, { startDate: e.target.value })}
                        className={showErrors && !selectedPhase.startDate ? "border-red-500" : ""}
                      />
                      {showErrors && !selectedPhase.startDate && (
                        <p className="text-red-500 text-xs">Start date is required</p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        When this phase should begin
                      </p>
                    </div>

                    {/* End Date */}
                    <div className="space-y-2">
                      <Label htmlFor="end-date">End Date & Time</Label>
                      <Input
                        id="end-date"
                        type="datetime-local"
                        value={selectedPhase.endDate || ""}
                        onChange={(e) => handleUpdatePhase(selectedPhase.id, { endDate: e.target.value })}
                        min={selectedPhase.startDate || ""}
                        className={showErrors && !selectedPhase.endDate ? "border-red-500" : ""}
                      />
                      {showErrors && !selectedPhase.endDate && (
                        <p className="text-red-500 text-xs">End date is required</p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        Deadline for completing this phase
                      </p>
                    </div>
                  </div>

                  {selectedPhase.startDate && selectedPhase.endDate && (
                    <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                      <div className="font-medium mb-1">Phase Duration:</div>
                      <div>
                        {Math.ceil(
                          (new Date(selectedPhase.endDate).getTime() - new Date(selectedPhase.startDate).getTime())
                          / (1000 * 60 * 60 * 24)
                        )} days ({Math.ceil(
                          (new Date(selectedPhase.endDate).getTime() - new Date(selectedPhase.startDate).getTime())
                          / (1000 * 60 * 60)
                        )} hours)
                      </div>
                    </div>
                  )}
                </div>

                {showAdvancedSettings && (
                  <>
                    <Separator />
                    <div className="space-y-4">
                      <h4 className="font-medium">Advanced Configuration</h4>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Passing Score */}
                        <div className="space-y-2">
                          <Label htmlFor="passing-score">Passing Score (%)</Label>
                          <Input
                            id="passing-score"
                            type="number"
                            min="0"
                            max="100"
                            value={selectedPhase.passingScore || ""}
                            onChange={(e) =>
                              handleUpdatePhase(selectedPhase.id, {
                                passingScore: parseInt(e.target.value) || undefined
                              })
                            }
                            className={showErrors && (selectedPhase.passingScore === undefined || selectedPhase.passingScore === null)
                              ? "border-red-500"
                              : ""}
                          />
                          {showErrors && (selectedPhase.passingScore === undefined || selectedPhase.passingScore === null) && (
                            <p className="text-red-500 text-xs">Passing Score is required</p>
                          )}
                        </div>

                        {/* Max Attempts */}
                        <div className="space-y-2">
                          <Label htmlFor="max-attempts">Max Attempts</Label>
                          <Input
                            id="max-attempts"
                            type="number"
                            min="1"
                            max="5"
                            value={selectedPhase.maxAttempts || ""}
                            onChange={(e) =>
                              handleUpdatePhase(selectedPhase.id, {
                                maxAttempts: parseInt(e.target.value) || undefined
                              })
                            }
                            className={showErrors && (selectedPhase.maxAttempts === undefined || selectedPhase.maxAttempts === null)
                              ? "border-red-500"
                              : ""}
                          />
                          {showErrors && (selectedPhase.maxAttempts === undefined || selectedPhase.maxAttempts === null) && (
                            <p className="text-red-500 text-xs">Max Attempts is required</p>
                          )}
                        </div>
                      </div>


                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="allow-skip"
                            checked={selectedPhase.allowSkip}
                            onCheckedChange={(checked) => handleUpdatePhase(selectedPhase.id, {
                              allowSkip: checked
                            })}
                          />
                          <Label htmlFor="allow-skip">Allow candidates to skip this phase</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="requires-approval"
                            checked={selectedPhase.requiresApproval}
                            onCheckedChange={(checked) => handleUpdatePhase(selectedPhase.id, {
                              requiresApproval: checked
                            })}
                          />
                          <Label htmlFor="requires-approval">Requires manual approval to proceed</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="auto-advance"
                            checked={selectedPhase.autoAdvance}
                            onCheckedChange={(checked) => handleUpdatePhase(selectedPhase.id, {
                              autoAdvance: checked
                            })}
                          />
                          <Label htmlFor="auto-advance">Auto-advance to next phase on completion</Label>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center text-muted-foreground">
                  <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select a phase from the list to configure its settings</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center">
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground">
                {phases.filter(p => p.isRequired).length} required phases, {phases.filter(p => !p.isRequired).length} optional phases
              </div>
              <div className="text-sm text-muted-foreground">
                {phases.filter(p => p.startDate && p.endDate).length} phases scheduled, {phases.filter(p => !p.startDate || !p.endDate).length} unscheduled
              </div>
            </div>
            <Button onClick={handleSaveConfiguration} className="px-8">
              <CheckCircle className="h-4 w-4 mr-2" />
              Save Configuration & Continue
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}