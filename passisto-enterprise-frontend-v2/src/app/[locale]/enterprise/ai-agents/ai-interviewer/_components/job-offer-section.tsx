"use client";

import * as React from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchJobOfferByProject,
  create<PERSON>ob<PERSON>ffer,
  UpdateJobOfferDTO,
  update<PERSON>obOffer,
  JobOffer,
  CreateJobOfferDTO,
  setCurrentJobOfferField,
} from "@/store/slices/hiring-system/jobOfferSlice";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";

import { Edit, DollarSign, MapPin, Clock, 
      GraduationCap, Award, Heart, Calendar, 
      ExternalLink, Plus, Refresh<PERSON><PERSON>,
      <PERSON><PERSON>, CheckCircle2, <PERSON><PERSON><PERSON>t, Link2 
  } from "lucide-react";

import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { useAuth } from "@clerk/nextjs";
import { FORM_BUILDER_GET_ALL } from "@/utils/routes";
import axiosInstance from "@/config/axios";
import { Form } from "@/types/form";
import { useBroadcastChannel } from '@/hooks/useBroadcastChannel';


type JobOfferSectionProps = {
  projectId: string;
  onNext: () => void;
};

export function JobOfferSection({ projectId, onNext }: JobOfferSectionProps) {
  const dispatch = useAppDispatch();
  const { getToken } = useAuth();
  
  const { currentJobOffer, status, error } = useAppSelector((state) => state.jobOffer);

  // Forms state
  const [forms, setForms] = React.useState<Form[]>([]);
  const [copied, setCopied] = React.useState(false);

  const { postMessage } = useBroadcastChannel('job-offer-form-builder');

  const [alert, setAlert] = React.useState<{
    title: string;
    description: string;
    onConfirm: () => void;
  } | null>(null);

  // Helper function to update a field in Redux state
  const updateField = React.useCallback((field: keyof JobOffer, value: any) => {
    dispatch(setCurrentJobOfferField({ field, value }));
  }, [dispatch]);

  // Fetch job offer for the project on component mount
  React.useEffect(() => {
    if (projectId) {
      const fetchData = async () => {
        const token = await getToken();
        if (!token) throw new Error("No authentication token");
        dispatch(fetchJobOfferByProject({ token, projectId: Number(projectId) }));
      };
      fetchData();
    }
  }, [projectId, dispatch, getToken]);


  // Update the "Create New Form" button click handler
  const handleCreateNewForm = () => {
    // Send job offer data to other tabs
    if (currentJobOffer) {
      const jobOfferData = {
        type: 'JOB_OFFER_DATA',
        payload: {
          title: currentJobOffer.title,
          description: currentJobOffer.description || undefined,
          location: currentJobOffer.location || undefined,
          department: currentJobOffer.department || undefined,
          experienceLevel: currentJobOffer.experienceLevel || undefined,
          workArrangement: currentJobOffer.workArrangement || undefined,
          requiredSkills: currentJobOffer.requiredSkills || undefined,
          languages: currentJobOffer.languages || undefined,
          educationLevel: currentJobOffer.educationLevel || undefined,
          yearsOfExperience: currentJobOffer.yearsOfExperience || undefined,
          benefits: currentJobOffer.benefits || undefined,
          additionalBenefits: currentJobOffer.additionalBenefits || undefined,
          applicationInstructions: currentJobOffer.applicationInstructions || undefined,
        },
        timestamp: Date.now()
      };
      
      // Send via BroadcastChannel for already-open tabs
      postMessage(jobOfferData);
      
      // Also store in localStorage for tabs that open later
      localStorage.setItem('pending-job-offer-data', JSON.stringify(jobOfferData));
    }
    
    // Open form builder in new tab
    window.open("/enterprise/ai-agents/form-builder", "_blank");
  };



  const fetchForms = React.useCallback(async () => {
    try {
      const token = await getToken();
      if (!token) throw new Error("No authentication token");
      const response = await axiosInstance.get<Form[]>(FORM_BUILDER_GET_ALL, {
        headers: { Authorization: `Bearer ${token}` },
      });
      setForms(response.data);
    } catch (err) {
      console.error("Failed to fetch forms:", err);
      toast.error("Failed to fetch forms");
    }
  }, [getToken]);
  
  React.useEffect(() => {
    fetchForms();
  }, [fetchForms]);

  const formatDateForInput = (dateValue: Date | string | null | undefined): string => {
    if (!dateValue) return "";
    
    if (dateValue instanceof Date) {
      return dateValue.toISOString().split('T')[0];
    }
    
    if (typeof dateValue === 'string') {
      const date = new Date(dateValue);
      return date.toISOString().split('T')[0];
    }
    
    return "";
  };

  const handleSaveOffer = async () => {
    if (!currentJobOffer) return;

    const jobOfferData: CreateJobOfferDTO = {
      title: currentJobOffer.title,
      description: currentJobOffer.description || undefined,
      location: currentJobOffer.location || undefined,
      department: currentJobOffer.department || undefined,
      workArrangement: currentJobOffer.workArrangement || undefined,
      experienceLevel: currentJobOffer.experienceLevel || undefined,
      salaryType: currentJobOffer.salaryType || undefined,
      minSalary: currentJobOffer.minSalary || undefined,
      maxSalary: currentJobOffer.maxSalary || undefined,
      salary: currentJobOffer.salary || undefined,
      currency: currentJobOffer.currency || undefined,
      salaryPeriod: currentJobOffer.salaryPeriod || undefined,
      applicationDeadline: currentJobOffer.applicationDeadline ? 
        formatDateForInput(currentJobOffer.applicationDeadline) : undefined,
      expectedStartDate: currentJobOffer.expectedStartDate ? 
        formatDateForInput(currentJobOffer.expectedStartDate) : undefined,
      contactEmail: currentJobOffer.contactEmail || undefined,
      applicationInstructions: currentJobOffer.applicationInstructions || undefined,
      bonusStructure: currentJobOffer.bonusStructure || undefined,
      additionalBenefits: currentJobOffer.additionalBenefits || undefined,
      benefits: currentJobOffer.benefits || undefined,
      educationLevel: currentJobOffer.educationLevel || undefined,
      yearsOfExperience: currentJobOffer.yearsOfExperience || undefined,
      requiredCertifications: currentJobOffer.requiredCertifications || undefined,
      languages: currentJobOffer.languages || undefined,
      requiredSkills: currentJobOffer.requiredSkills || undefined,
      projectId: Number(projectId),
      isActive: true,
      formId: currentJobOffer.formId || undefined,
    };

    setAlert({
      title: "Create Job Offer",
      description: "Are you sure you want to create this job offer?",
      onConfirm: async () => {
        try {
          const token = await getToken();
          if (!token) throw new Error("No authentication token");
          await dispatch(createJobOffer({ token, jobOfferData })).unwrap();
          toast.success("Job offer created successfully!");
          onNext();
        } catch (err) {
          console.error("Failed to create job offer:", err);
          toast.error("Failed to create job offer");
        }
        setAlert(null);
      },
    });
  };

  const handleCopy = async () => {
    if (currentJobOffer?.formId) {
      await navigator.clipboard.writeText(
        `https://pe.passisto.com/public-page/form/${currentJobOffer.formId}`
      );
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const getSelectedForm = () => {
    return forms.find(form => form.id === currentJobOffer?.formId);
  };

  const handleUpdateOffer = async () => {
    if (!currentJobOffer) return;
    
    const jobOfferData: UpdateJobOfferDTO = {
      title: currentJobOffer.title,
      description: currentJobOffer.description || undefined,
      location: currentJobOffer.location || undefined,
      department: currentJobOffer.department || undefined,
      workArrangement: currentJobOffer.workArrangement || undefined,
      experienceLevel: currentJobOffer.experienceLevel || undefined,
      salaryType: currentJobOffer.salaryType || undefined,
      minSalary: currentJobOffer.minSalary || undefined,
      maxSalary: currentJobOffer.maxSalary || undefined,
      salary: currentJobOffer.salary || undefined,
      currency: currentJobOffer.currency || undefined,
      salaryPeriod: currentJobOffer.salaryPeriod || undefined,
      applicationDeadline: currentJobOffer.applicationDeadline ? 
        formatDateForInput(currentJobOffer.applicationDeadline) : undefined,
      expectedStartDate: currentJobOffer.expectedStartDate ? 
        formatDateForInput(currentJobOffer.expectedStartDate) : undefined,
      contactEmail: currentJobOffer.contactEmail || undefined,
      applicationInstructions: currentJobOffer.applicationInstructions || undefined,
      bonusStructure: currentJobOffer.bonusStructure || undefined,
      additionalBenefits: currentJobOffer.additionalBenefits || undefined,
      benefits: currentJobOffer.benefits || undefined,
      educationLevel: currentJobOffer.educationLevel || undefined,
      yearsOfExperience: currentJobOffer.yearsOfExperience || undefined,
      requiredCertifications: currentJobOffer.requiredCertifications || undefined,
      languages: currentJobOffer.languages || undefined,
      requiredSkills: currentJobOffer.requiredSkills || undefined,
      isActive: true,
      formId: currentJobOffer.formId || undefined,
    };

    setAlert({
      title: "Update Job Offer",
      description: "Are you sure you want to update this job offer?",
      onConfirm: async () => {
        try {
          const token = await getToken();
          if (!token) throw new Error("No authentication token");
          await dispatch(updateJobOffer({ 
            token, 
            id: currentJobOffer.id, 
            jobOfferData 
          })).unwrap();
          toast.success("Job offer updated successfully!");
          onNext();
        } catch (err) {
          console.error("Failed to update job offer:", err);
          toast.error("Failed to update job offer");
        }
        setAlert(null);
      },
    });
  };

  // Determine if we're in create or update mode
  const isUpdateMode = Boolean(currentJobOffer?.id);

  return (
    <>
      {alert && (
        <Alert className="fixed bottom-4 right-4 max-w-sm bg-white shadow-lg">
          <AlertTitle>{alert.title}</AlertTitle>
          <AlertDescription>{alert.description}</AlertDescription>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setAlert(null)}>
              Cancel
            </Button>
            <Button onClick={alert.onConfirm}>Confirm</Button>
          </div>
        </Alert>
      )}

      <Card className="w-full max-w-4xl">
        <CardHeader>
          <CardTitle>
            {isUpdateMode ? "Edit Job Offer" : "Define Job Offer"}
          </CardTitle>
          <CardDescription>
            {isUpdateMode 
              ? "Update the details of your job position." 
              : "Provide details about the job position. Our AI will help extract key requirements."
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Basic Job Information */}
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <Award className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Basic Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="jobTitle">Job Title *</Label>
                <Input
                  id="jobTitle"
                  placeholder="e.g., Senior Software Engineer"
                  value={currentJobOffer?.title || ""}
                  onChange={(e) => updateField("title", e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="location">
                  <MapPin className="h-4 w-4 inline mr-1" />
                  Location
                </Label>
                <Input
                  id="location"
                  placeholder="e.g., New York, USA or Remote"
                  value={currentJobOffer?.location || ""}
                  onChange={(e) => updateField("location", e.target.value)}
                />
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="jobDescription">Job Description</Label>
              <Textarea
                id="jobDescription"
                placeholder="Provide a detailed job description, responsibilities, and what makes this role exciting..."
                rows={6}
                value={currentJobOffer?.description || ""}
                onChange={(e) => updateField("description", e.target.value)}
              />
            </div>
          </div>

          <Separator />

          {/* Employment Details */}
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Employment Details</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="grid gap-2">
                <Label>Work Arrangement</Label>
                <Select 
                  value={currentJobOffer?.workArrangement || ""} 
                  onValueChange={(value: "ONSITE" | "REMOTE" | "HYBRID") => 
                    updateField("workArrangement", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select arrangement" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ONSITE">On-site</SelectItem>
                    <SelectItem value="REMOTE">Remote</SelectItem>
                    <SelectItem value="HYBRID">Hybrid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label>Experience Level</Label>
                <Select 
                  value={currentJobOffer?.experienceLevel || ""} 
                  onValueChange={(value: "ENTRY" | "JUNIOR" | "MID" | "SENIOR" | "EXECUTIVE") => 
                    updateField("experienceLevel", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ENTRY">Entry Level</SelectItem>
                    <SelectItem value="JUNIOR">Junior</SelectItem>
                    <SelectItem value="MID">Mid Level</SelectItem>
                    <SelectItem value="SENIOR">Senior</SelectItem>
                    <SelectItem value="EXECUTIVE">Executive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="department">Department</Label>
                <Input
                  id="department"
                  placeholder="e.g., Engineering, Marketing"
                  value={currentJobOffer?.department || ""}
                  onChange={(e) => updateField("department", e.target.value)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Compensation */}
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Compensation</h3>
            </div>
            
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label>Salary Type</Label>
                <Select 
                  value={currentJobOffer?.salaryType || ""} 
                  onValueChange={(value: "RANGE" | "FIXED_AMOUNT") => 
                    updateField("salaryType", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select salary type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="RANGE">Range</SelectItem>
                    <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {currentJobOffer?.salaryType === "RANGE" ? (
                  <>
                    <div className="grid gap-2">
                      <Label htmlFor="minSalary">Minimum Salary</Label>
                      <Input
                        id="minSalary"
                        type="number"
                        placeholder="50000"
                        value={currentJobOffer?.minSalary?.toString() || ""}
                        onChange={(e) => updateField("minSalary", parseFloat(e.target.value) || undefined)}
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="maxSalary">Maximum Salary</Label>
                      <Input
                        id="maxSalary"
                        type="number"
                        placeholder="80000"
                        value={currentJobOffer?.maxSalary?.toString() || ""}
                        onChange={(e) => updateField("maxSalary", parseFloat(e.target.value) || undefined)}
                      />
                    </div>
                  </>
                ) : currentJobOffer?.salaryType === "FIXED_AMOUNT" ? (
                  <div className="grid gap-2">
                    <Label htmlFor="salary">Salary Amount</Label>
                    <Input
                      id="salary"
                      type="number"
                      placeholder="65000"
                      value={currentJobOffer?.salary || ""}
                      onChange={(e) => updateField("salary", e.target.value)}
                    />
                  </div>
                ) : null}
                
                <div className="grid gap-2">
                  <Label>Currency</Label>
                  <Select 
                    value={currentJobOffer?.currency || "USD"} 
                    onValueChange={(value) => updateField("currency", value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                      <SelectItem value="CAD">CAD</SelectItem>
                      <SelectItem value="AUD">AUD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid gap-2">
                  <Label>Period</Label>
                  <Select 
                    value={currentJobOffer?.salaryPeriod || "MONTHLY"} 
                    onValueChange={(value: "HOURLY" | "DAILY" | "MONTHLY") => 
                      updateField("salaryPeriod", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="HOURLY">Hourly</SelectItem>
                      <SelectItem value="DAILY">Daily</SelectItem>
                      <SelectItem value="MONTHLY">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="bonusStructure">Bonus Structure</Label>
                <Input
                  id="bonusStructure"
                  placeholder="e.g., Annual performance bonus up to 20%"
                  value={currentJobOffer?.bonusStructure || ""}
                  onChange={(e) => updateField("bonusStructure", e.target.value)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Benefits */}
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Benefits & Perks</h3>
            </div>
            
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="benefits">Benefits</Label>
                <Textarea
                  id="benefits"
                  placeholder="List primary benefits like health insurance, retirement plans, etc."
                  rows={3}
                  value={currentJobOffer?.benefits || ""}
                  onChange={(e) => updateField("benefits", e.target.value)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="additionalBenefits">Additional Benefits</Label>
                <Textarea
                  id="additionalBenefits"
                  placeholder="List any other benefits like gym membership, food allowance, transportation, etc."
                  rows={3}
                  value={currentJobOffer?.additionalBenefits || ""}
                  onChange={(e) => updateField("additionalBenefits", e.target.value)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Requirements */}
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <GraduationCap className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Requirements</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="educationLevel">Education Level</Label>
                <Input
                  id="educationLevel"
                  placeholder="e.g., Bachelor's Degree in Computer Science"
                  value={currentJobOffer?.educationLevel || ""}
                  onChange={(e) => updateField("educationLevel", e.target.value)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="yearsOfExperience">Years of Experience</Label>
                <Input
                  id="yearsOfExperience"
                  type="number"
                  placeholder="e.g., 3"
                  value={currentJobOffer?.yearsOfExperience?.toString() || ""}
                  onChange={(e) => updateField("yearsOfExperience", parseInt(e.target.value) || undefined)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="requiredCertifications">Required Certifications</Label>
                <Input
                  id="requiredCertifications"
                  placeholder="e.g., AWS Certified, PMP, etc."
                  value={currentJobOffer?.requiredCertifications || ""}
                  onChange={(e) => updateField("requiredCertifications", e.target.value)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="languages">Languages</Label>
                <Input
                  id="languages"
                  placeholder="e.g., English (fluent), Spanish (conversational)"
                  value={currentJobOffer?.languages || ""}
                  onChange={(e) => updateField("languages", e.target.value)}
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="requiredSkills">Required Skills</Label>
              <Textarea
                id="requiredSkills"
                placeholder="List required technical and soft skills, separated by commas or line breaks"
                rows={3}
                value={currentJobOffer?.requiredSkills || ""}
                onChange={(e) => updateField("requiredSkills", e.target.value)}
              />
            </div>
          </div>

          <Separator />

          {/* Application Details */}
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Application Details</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="applicationDeadline">Application Deadline</Label>
                <Input
                  id="applicationDeadline"
                  type="date"
                  value={formatDateForInput(currentJobOffer?.applicationDeadline)}
                  onChange={(e) => updateField("applicationDeadline", e.target.value ? new Date(e.target.value) : null)}
                />
              </div>
              
              <div className="grid gap-2">
                <Label htmlFor="expectedStartDate">Expected Start Date</Label>
                <Input
                  id="expectedStartDate"
                  type="date"
                  value={formatDateForInput(currentJobOffer?.expectedStartDate)}
                  onChange={(e) => updateField("expectedStartDate", e.target.value ? new Date(e.target.value) : null)}
                />
              </div>
              
              <div className="grid gap-2 md:col-span-2">
                <Label htmlFor="contactEmail">Contact Email</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  placeholder="<EMAIL>"
                  value={currentJobOffer?.contactEmail || ""}
                  onChange={(e) => updateField("contactEmail", e.target.value)}
                />
              </div>
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="applicationInstructions">Application Instructions</Label>
              <Textarea
                id="applicationInstructions"
                placeholder="Special instructions for applicants, documents to include, etc."
                rows={3}
                value={currentJobOffer?.applicationInstructions || ""}
                onChange={(e) => updateField("applicationInstructions", e.target.value)}
              />
            </div>
          </div>

          <Separator />

          {/* Form Selection Section */}
          <Card className="border-border/50 bg-gradient-to-br from-card to-muted/20">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="h-5 w-5 text-primary" />
                Application Form
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Form Selection */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Select Existing Form</Label>
                <Select 
                  value={currentJobOffer?.formId || ""} 
                  onValueChange={(value) => updateField("formId", value)}
                >
                  <SelectTrigger className="w-full h-11 bg-background border-border/60 hover:border-border transition-colors">
                    <SelectValue placeholder="Choose a form to associate with this job offer" />
                  </SelectTrigger>
                  <SelectContent>
                    {forms.map((form) => (
                      <SelectItem key={form.id} value={form.id} className="cursor-pointer">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          {form.title}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  variant="default"
                  className="flex items-center gap-2 flex-1"
                  onClick={handleCreateNewForm}
                >
                  <Plus className="h-4 w-4" />
                  Create New Form
                </Button>
                <Button 
                  variant="outline" 
                  className="flex items-center gap-2"
                  onClick={fetchForms}
                >
                  <RefreshCw className="h-4 w-4" />
                  Refresh
                </Button>
              </div>

              {/* Enhanced Form Preview */}
              {currentJobOffer?.formId && (
                <div className="mt-6 p-4 rounded-lg border bg-background/50 backdrop-blur-sm">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <Link2 className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium text-foreground">Form Preview Link</span>
                      </div>
                      <div className="flex items-center gap-2 p-2 rounded border bg-muted/30">
                        <code className="text-xs text-primary break-all flex-1">
                          https://pe.passisto.com/public-page/form/{currentJobOffer.formId}
                        </code>
                      </div>
                    </div>
                    <div className="flex gap-2 flex-shrink-0">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleCopy}
                        className="h-8 px-3"
                      >
                        {copied ? (
                          <CheckCircle2 className="h-3.5 w-3.5 text-green-600" />
                        ) : (
                          <Copy className="h-3.5 w-3.5" />
                        )}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        asChild
                        className="h-8 px-3"
                      >
                        <a
                          href={`https://pe.passisto.com/public-page/form/${currentJobOffer.formId}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <ExternalLink className="h-3.5 w-3.5" />
                        </a>
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Separator />

          {/* AI Extracted Metadata Preview */}
          <div className="space-y-4 rounded-md border p-4 bg-muted/20">
            <h3 className="text-lg font-semibold">Preview</h3>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label>Job Title</Label>
                <div className="flex items-center gap-2">
                  {currentJobOffer?.title ? (
                    <Badge variant="secondary">{currentJobOffer.title}</Badge>
                  ) : (
                    <span className="text-sm text-muted-foreground">No title specified</span>
                  )}
                </div>
              </div>
              
              <div className="grid gap-2">
                <Label>Experience Level & Work Arrangement</Label>
                <div className="flex flex-wrap gap-2">
                  {currentJobOffer?.experienceLevel && <Badge variant="outline">{currentJobOffer.experienceLevel}</Badge>}
                  {currentJobOffer?.workArrangement && <Badge variant="outline">{currentJobOffer.workArrangement}</Badge>}
                  {!currentJobOffer?.experienceLevel && !currentJobOffer?.workArrangement && (
                    <span className="text-sm text-muted-foreground">Not specified</span>
                  )}
                </div>
              </div>
              
              <div className="grid gap-2">
                <Label>Salary Information</Label>
                <div className="flex flex-wrap gap-2">
                  {currentJobOffer?.salaryType === "RANGE" && currentJobOffer?.minSalary && currentJobOffer?.maxSalary ? (
                    <Badge variant="secondary">
                      {currentJobOffer.minSalary} - {currentJobOffer.maxSalary} {currentJobOffer.currency || "USD"} / {(currentJobOffer.salaryPeriod || "MONTHLY").toLowerCase()}
                    </Badge>
                  ) : currentJobOffer?.salaryType === "FIXED_AMOUNT" && currentJobOffer?.salary ? (
                    <Badge variant="secondary">
                      {currentJobOffer.salary} {currentJobOffer.currency || "USD"} / {(currentJobOffer.salaryPeriod || "MONTHLY").toLowerCase()}
                    </Badge>
                  ) : (
                    <span className="text-sm text-muted-foreground">No salary information</span>
                  )}
                </div>
              </div>
              
              <div className="grid gap-2">
                <Label>Key Details</Label>
                <div className="flex flex-wrap gap-2">
                  {currentJobOffer?.location && <Badge variant="outline">{currentJobOffer.location}</Badge>}
                  {currentJobOffer?.department && <Badge variant="outline">{currentJobOffer.department}</Badge>}
                  {currentJobOffer?.yearsOfExperience && <Badge variant="outline">{currentJobOffer.yearsOfExperience} years exp.</Badge>}
                  {!currentJobOffer?.location && !currentJobOffer?.department && !currentJobOffer?.yearsOfExperience && (
                    <span className="text-sm text-muted-foreground">No additional details</span>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-medium">Application Form Status</Label>
                {currentJobOffer?.formId ? (
                  <div className="flex items-center gap-3 p-3 rounded-lg border bg-green-50/50 border-green-200/50">
                    <CheckCircle2 className="h-5 w-5 text-green-600" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
                          Form #{currentJobOffer.formId}
                        </Badge>
                        <span className="text-sm font-medium text-green-900">
                          {getSelectedForm()?.title}
                        </span>
                      </div>
                      <p className="text-xs text-green-700 mt-1">
                        Form successfully linked to this job offer
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-3 p-3 rounded-lg border bg-amber-50/50 border-amber-200/50">
                    <div className="h-5 w-5 rounded-full border-2 border-amber-400 flex items-center justify-center">
                      <div className="h-2 w-2 rounded-full bg-amber-400" />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-amber-900">No form selected</span>
                      <p className="text-xs text-amber-700">
                        Select a form above to enable applications for this job offer
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              This preview shows how your job offer information will be structured.
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="rounded-md border border-red-200 bg-red-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="flex gap-4">
            {!isUpdateMode ? (
              <Button 
                onClick={handleSaveOffer} 
                className="flex-1" 
                size="lg"
                disabled={!currentJobOffer?.title || status === "loading"}
              >
                {status === "loading" ? "Saving..." : "Save Job Offer & Continue"}
              </Button>
            ) : (
              <Button 
                onClick={handleUpdateOffer} 
                className="flex-1" 
                size="lg"
                disabled={!currentJobOffer?.title || status === "loading"}
              >
                {status === "loading" ? "Updating..." : "Update Job Offer"}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </>
  );
}