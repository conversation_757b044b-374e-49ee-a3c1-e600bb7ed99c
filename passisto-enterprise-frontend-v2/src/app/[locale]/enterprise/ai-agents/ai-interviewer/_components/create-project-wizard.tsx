"use client"

import * as React from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import { Check } from "lucide-react"
import { useAuth } from "@clerk/nextjs"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert"
import { toast } from "sonner"
import { useAppDispatch } from "@/store/hooks"
import { createProject } from "@/store/slices/hiring-system/projectSlice"

type ProjectFormData = {
  projectName: string
  description: string
  endDate: string
}

const initialFormData: ProjectFormData = {
  projectName: "",
  description: "",
  endDate: "",
}

const steps = [
  { id: "basic-info", title: "Basic Information" },
  { id: "project-description", title: "Project Description" },
  { id: "review", title: "Review & Create" },
]

export function CreateProjectWizard() {
  const [currentStep, setCurrentStep] = React.useState(0)
  const [formData, setFormData] = React.useState<ProjectFormData>(initialFormData)
  const router = useRouter()
  const { getToken } = useAuth()
  const dispatch = useAppDispatch()
  const [alert, setAlert] = React.useState<{
    title: string
    description: string
    onConfirm?: () => void
  } | null>(null)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData((prev) => ({ ...prev, [id]: value }))
  }

  const handleNext = () => {
    if (currentStep === 0) {
      if (!formData.projectName || !formData.endDate) {
        toast.error("Missing Information", {
          description: "Please fill in all basic information fields."
        })
        return
      }
    } else if (currentStep === 1) {
      if (!formData.description) {
        toast.error("Missing Description", {
          description: "Please provide a Project Description."
        })
        return
      }
    }
    setCurrentStep((prev) => prev + 1)
  }

  const handlePrevious = () => {
    setCurrentStep((prev) => prev - 1)
  }

  const handleSubmit = async () => {
    try {
      const token = await getToken()
      if (!token) throw new Error("No authentication token")

      await dispatch(createProject({ 
        token,
        projectData: {
          name: formData.projectName,
          description: formData.description,
          startDate: new Date().toISOString(),
          endDate: formData.endDate,
          status: "DRAFT"
        }
      })).unwrap()

      toast.success("Project Created", {
        description: `Project "${formData.projectName}" created successfully!`
      })
      
      router.push("/enterprise/ai-agents/ai-interviewer/projects")
    } catch (error) {
      console.error("Error creating project:", error)
      toast.error("Error", {
        description: "Failed to create project. Please try again."
      })
    }
  }

  const renderStepContent = () => {
    switch (steps[currentStep].id) {
      case "basic-info":
        return (
          <div className="grid gap-6">
            <div className="grid gap-2">
              <Label htmlFor="projectName">Project Name</Label>
              <Input
                id="projectName"
                placeholder="e.g., Senior Software Engineer Hiring"
                value={formData.projectName}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="endDate">End Date</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate}
                onChange={handleChange}
                required
              />
            </div>
          </div>
        )
      case "project-description":
        return (
          <div className="grid gap-6">
            <div className="grid gap-2">
              <Label htmlFor="description">Project Description</Label>
              <Textarea
                id="description"
                placeholder="Provide a detailed Project Description. Our AI will help extract key requirements from this."
                rows={10}
                value={formData.description}
                onChange={handleChange}
                required
              />
              <p className="text-sm text-muted-foreground">
                This description will be used by our AI to extract skills, experience, and education requirements.
              </p>
            </div>
          </div>
        )
      case "review":
        return (
          <div className="grid gap-4">
            <h3 className="text-lg font-semibold">Review Project Details</h3>
            <div className="grid gap-2">
              <p className="text-sm font-medium">Project Name:</p>
              <p className="text-muted-foreground">{formData.projectName || "N/A"}</p>
            </div>
            <div className="grid gap-2">
              <p className="text-sm font-medium">End Date:</p>
              <p className="text-muted-foreground">{formData.endDate || "N/A"}</p>
            </div>
            <div className="grid gap-2">
              <p className="text-sm font-medium">Project Description:</p>
              <p className="text-muted-foreground line-clamp-6">{formData.description || "N/A"}</p>
            </div>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <>
      <Card className="w-full max-w-7xl shadow-lg">
        <CardHeader className="pb-6">
          <CardTitle className="text-2xl font-bold">Create New Project</CardTitle>
          <CardDescription>Follow the steps below to set up your new recruitment project.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          {/* Stepper Progress */}
          <div className="flex items-center justify-between space-x-4">
            {steps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div className="flex flex-col items-center">
                  <div
                    className={cn(
                      "flex h-10 w-10 items-center justify-center rounded-full border-2",
                      index === currentStep
                        ? "border-foreground bg-foreground text-background"
                        : index < currentStep
                        ? "border-foreground bg-foreground text-background"
                        : "border-muted-foreground text-muted-foreground",
                    )}
                  >
                    {index < currentStep ? <Check className="h-5 w-5" /> : index + 1}
                  </div>
                  <span
                    className={cn(
                      "mt-2 whitespace-nowrap text-center text-sm",
                      index === currentStep ? "font-semibold text-foreground" : "text-muted-foreground",
                    )}
                  >
                    {step.title}
                  </span>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "h-0.5 flex-1 rounded-full",
                      index < currentStep ? "bg-foreground" : "bg-muted-foreground/30",
                    )}
                  />
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Step Content */}
          <div className="min-h-[250px]">{renderStepContent()}</div>

          {/* Navigation Buttons */}
          <div className="flex justify-between">
            <Button onClick={handlePrevious} disabled={currentStep === 0} variant="outline">
              Previous
            </Button>
            {currentStep < steps.length - 1 ? (
              <Button onClick={handleNext}>Next</Button>
            ) : (
              <Button onClick={handleSubmit}>Create Project</Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Add Alert component at the end */}
      {alert && (
        <Alert className="fixed bottom-4 right-4 max-w-sm">
          <AlertTitle>{alert.title}</AlertTitle>
          <AlertDescription>{alert.description}</AlertDescription>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={() => setAlert(null)}>
              Close
            </Button>
            {alert.onConfirm && (
              <Button onClick={() => {
                alert.onConfirm?.()
                setAlert(null)
              }}>
                Confirm
              </Button>
            )}
          </div>
        </Alert>
      )}
    </>
  )
}
