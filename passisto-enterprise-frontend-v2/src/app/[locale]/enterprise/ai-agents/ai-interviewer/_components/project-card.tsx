"use client";
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@clerk/nextjs";
import { 
  MoreVertical, 
  Edit3, 
  Eye, 
  Archive, 
  Trash2, 
  Users
} from "lucide-react";
import { useState } from "react";
import { EditProjectCard } from "./EditProjectCard";
import { useAppDispatch, useAppSelector } from "@/store/hooks"; // Adjust import path as needed
import { deleteProject, updateProject, selectProjectsLoading, Project } from "@/store/slices/hiring-system/projectSlice"; // Adjust import path

interface ProjectCardProps {
  project: Project;
  onUpdate: any;
  onDelete: any
}

export function ProjectCard({ project }: ProjectCardProps) {
  const { getToken } = useAuth();
  const dispatch = useAppDispatch();
  const isLoading = useAppSelector(selectProjectsLoading);
  
  const [alert, setAlert] = useState<{ title: string; description: string; onConfirm: () => void } | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const formatDateForInput = (dateValue: Date | string | null | undefined): string => {
    if (!dateValue) return "";
    
    if (dateValue instanceof Date) {
      return dateValue.toISOString().split('T')[0];
    }
    
    if (typeof dateValue === 'string') {
      const date = new Date(dateValue);
      return date.toISOString().split('T')[0];
    }
    
    return "";
  };

  const handleDelete = () => {
    setAlert({
      title: "Delete Project",
      description: `Are you sure you want to delete the project "${project.name}"? This action cannot be undone.`,
      onConfirm: async () => {
        try {
          const token = await getToken();
          if (token) {
            await dispatch(deleteProject({ token, projectId: project.id })).unwrap();
          }
        } catch (error) {
          console.error("Error deleting project:", error);
        } finally {
          setAlert(null);
        }
      },
    });
  };

  const handleArchive = () => {
    setAlert({
      title: "Archive Project",
      description: `Are you sure you want to archive the project "${project.name}"?`,
      onConfirm: async () => {
        try {
          const token = await getToken();
          if (token) {
            await dispatch(updateProject({ 
              token, 
              projectId: project.id, 
              projectData: { status: "ARCHIVED" } 
            })).unwrap();
          }
        } catch (error) {
          console.error("Error archiving project:", error);
        } finally {
          setAlert(null);
        }
      },
    });
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "COMPLETE": return "text-green-600";
      case "IN_PROGRESS": return "text-blue-600";
      case "DRAFT": return "text-yellow-600";
      case "ARCHIVED": return "text-gray-600";
      default: return "text-gray-600";
    }
  };

  const isCompleteOrArchived = project.status === "COMPLETE" || project.status === "ARCHIVED";

  return (
    <>
      <Card className="flex flex-col justify-between hover:shadow-lg transition-shadow duration-200">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="truncate">{project.name}</span>
            <span className={`text-xs font-medium px-2 py-1 rounded-full ${getStatusColor(project.status)} bg-opacity-10`}>
              {project.status}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-muted-foreground space-y-1">
            <p className="flex items-center gap-2">
              <span className="font-medium">Created:</span> {formatDateForInput(project.createdAt)}
            </p>
            <p className="flex items-center gap-2">
              <span className="font-medium">Finished:</span> {formatDateForInput(project.endDate)}
            </p>
          </div>
        </CardContent>
        <div className="p-4 pt-0">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full hover:bg-muted">
                <MoreVertical className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem asChild>
                <Link href={`/enterprise/ai-agents/ai-interviewer/projects/${project.id}`} className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  View Project
                </Link>
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              {!isCompleteOrArchived && (
                <DropdownMenuItem onClick={handleEdit} className="flex items-center gap-2">
                  <Edit3 className="h-4 w-4" />
                  Edit Project
                </DropdownMenuItem>
              )}
              
              <DropdownMenuSeparator />
              
              {project.status !== "ARCHIVED" && (
                <DropdownMenuItem 
                  onClick={handleArchive} 
                  className="flex items-center gap-2"
                  disabled={isLoading}
                >
                  <Archive className="h-4 w-4" />
                  Archive Project
                </DropdownMenuItem>
              )}
              
              <DropdownMenuSeparator />
              
              <DropdownMenuItem 
                onClick={handleDelete} 
                className="flex items-center gap-2 text-red-600 focus:text-red-600 focus:bg-red-50"
                disabled={isLoading}
              >
                <Trash2 className="h-4 w-4" />
                Delete Project
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        {alert && (
          <Alert className="fixed bottom-4 right-4 max-w-sm">
            <AlertTitle>{alert.title}</AlertTitle>
            <AlertDescription>{alert.description}</AlertDescription>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setAlert(null)}>
                Cancel
              </Button>
              <Button onClick={alert.onConfirm}>Confirm</Button>
            </div>
          </Alert>
        )}
      </Card>
      {isEditing && (
        <EditProjectCard
          projectId={project.id}
          onClose={() => setIsEditing(false)}
          onSave={() => {}} // EditProjectCard should also dispatch updateProject
        />
      )}
    </>
  );
}