"use client";

import { Label } from "@/components/ui/label";
import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  MoreHorizontal,
  Sparkles,
  FileText,
  Mail,
  Code,
  Plus,
  Edit,
  Trash2,
  X,
  Save,
  ArrowRight,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import type { InterviewPhase, InterviewPhaseType } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { TemplateAssignmentSection } from "./template-assignment-section";
import {
  createEmailTemplate,
  createStructuralTemplate,
  deleteTemplate,
  fetchActiveTemplates,
  fetchTemplateById,
  fetchTemplates,
  generateQuestions,
  updateTemplate,
} from "@/store/slices/interviewTemplateSlice";
import { fetchPhases } from "@/store/slices/phaseSlice";
// import { useDispatch } from "react-redux"
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { useAuth } from "@clerk/nextjs";

interface InterviewQuestion {
  // metadata?: {
  //   role: string;
  //   level: string;
  //   techStack: string[];
  //   totalDuration: number;
  //   questionCount: number;
  // };
  sections?: Array<{
    name: string;
    description: string;
    questions: Array<{
      question: string;
      followUp: string[];
      difficulty: string;
      category: string;
      // expectedAnswer: string;
    }>;
  }>;
  evaluationCriteria?: {
    technical: string;
    communication: string;
    problemSolving: string;
  };
}

type InterviewTemplate = {
  id: string;
  name: string;
  type:
    | "Technical"
    | "Behavioral"
    | "Mixed"
    | "Custom"
    | "Sales"
    | "Leadership";
  dateModified: string;
  // New fields for creation
  templateType: "structural";
  role?: string;
  level?: string;
  techStack?: string[];
  questionCount?: number;
  questions?: InterviewQuestion;
  phaseId?: number; // Which phases this template applies to
  interviewDuration: number;
};

type EmailTemplate = {
  id: string;
  name: string;
  phaseId: number; // Phase selection
  subject: string;
  body: string;
  platform: "HackerRank" | "Codility" | "LeetCode" | "Custom" | "Generic";
  dateModified: string;
  // Email configuration
  isActive: boolean;
  variables: string[]; // Available variables for injection
};

type InterviewTemplateSectionProps = {
  projectId: string;
  phases?: InterviewPhase[];
  onNext: () => void;
};

export function InterviewTemplateSection({
  projectId,
  phases = [],
  onNext,
}: InterviewTemplateSectionProps) {
  const dispatch = useAppDispatch();
  const { getToken } = useAuth();
  const phasesFromStore = useAppSelector((state) => state.phases.phases);
  const phasesLoading = useAppSelector((state) => state.phases.loading);
  const [editingTemplateId, setEditingTemplateId] = React.useState<
    number | null
  >(null);

  // Use phases from store if available, otherwise fall back to props
  const availablePhases = phasesFromStore.length > 0 ? phasesFromStore : phases;

  const [currentStep, setCurrentStep] = React.useState<"create" | "assign">(
    "create"
  );
  const [templates, setTemplates] = React.useState<InterviewTemplate[]>([]);
  const [emailTemplates, setEmailTemplates] = React.useState<EmailTemplate[]>(
    []
  );
  const [viewMode, setViewMode] = React.useState<"create" | "email">("create");

  const fetchData = async () => {
    try {
      const token = await getToken();
      if (!token) {
        console.error("No token available");
        return;
      }
      
      // Fetch phases for this project
      if (projectId) {
        dispatch(fetchPhases({ projectId, token }));
      }
      
      const response = await dispatch(fetchTemplates({ projectId: parseInt(projectId), token }));
      if (true) {
        const fetchedTemplates: any = response.payload;

        // Separate interview and email templates based on template.type
        const interviewTemplates: InterviewTemplate[] = [];
        const emailTemplates: EmailTemplate[] = [];

        fetchedTemplates.forEach((template: any) => {
          if (template.template.type === "email") {
            emailTemplates.push({
              id: template.id.toString(),
              name: template.name,
              phaseId: template.phaseId,
              subject: template.template.subject,
              body: template.template.body,
              platform: "Generic", // Default platform since it's not in the response
              dateModified: new Date(template.createdAt)
                .toISOString()
                .split("T")[0],
              isActive: template.template.isActive || true,
              variables: template.template.variables || [],
            });
          } else if (template.template.type === "structural") {
            // console.log(template);
            interviewTemplates.push({
              id: template.id.toString(),
              name: template.name,
              type: "Technical", // map structural → Technical
              dateModified: new Date(template.updatedAt)
                .toISOString()
                .split("T")[0], // ✅ now included
              templateType: "structural",
              role: template.template.role,
              level: template.template.level,
              techStack: template.template.techStack, // ✅ keep it as string[] (not joined to string)
              questionCount: template.template.questionCount,
              questions: template.template.questions || {}, // ✅ match InterviewQuestion type
              phaseId: template.phaseId,
              interviewDuration: template.durationMinutes || 60, // ✅ required
            });
          }
        });

        setTemplates(interviewTemplates);
        setEmailTemplates(emailTemplates);
      }
    } catch (error) {
      console.error("Failed to fetch templates:", error);
    }
  };
  // Initialize with sample templates for demonstration
  React.useEffect(() => {
    fetchData();
  }, [dispatch, projectId]);

  // These arrays are also used in the template type dropdowns

  const [newTemplateData, setNewTemplateData] = React.useState({
    role: "",
    level: "",
    type: "Technical" as InterviewTemplate["type"],
    templateType: "structural",
    techStack: "", // Changed from array to string for form input
    questionCount: 0,
    interviewDuration: 0, // Default 60 minutes
    phaseId: "", // Phase selection
    questions: {} as InterviewQuestion,
  });

  const [newEmailTemplateData, setNewEmailTemplateData] = React.useState({
    name: "",
    subject: "",
    body: "",
    platform: "Generic" as EmailTemplate["platform"],
    phaseId: "", // Phase selection
  });

  const [isGeneratingQuestions, setIsGeneratingQuestions] =
    React.useState(false);
  const [showQuestions, setShowQuestions] = React.useState(false);
  const [editingQuestion, setEditingQuestion] =
    React.useState<any | null>(null);
  const [editingQuestionIndex, setEditingQuestionIndex] = 
    React.useState<{ sectionIndex: number; questionIndex: number } | null>(null);
  const [editingEvaluationCriteria, setEditingEvaluationCriteria] = 
    React.useState<boolean>(false);
  const [newQuestion, setNewQuestion] = React.useState({
    question: "",
    difficulty: "Medium",
    category: "",
    followUp: [] as string[],
  });

  const [isInterviewModalOpen, setIsInterviewModalOpen] = React.useState(false);
  const [isEmailModalOpen, setIsEmailModalOpen] = React.useState(false);
  const [selectedTemplate, setSelectedTemplate] = React.useState<
    InterviewTemplate | EmailTemplate | null
  >(null);

  const handleNewTemplateChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { id, value } = e.target;
    setNewTemplateData((prev) => ({ ...prev, [id]: value }));
  };

  const handleNewTemplateTypeChange = (value: InterviewTemplate["type"]) => {
    setNewTemplateData((prev) => ({ ...prev, type: value }));
  };

  const handleNewEmailTemplateChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { id, value } = e.target;
    setNewEmailTemplateData((prev) => ({ ...prev, [id]: value }));
  };

  const handleNewEmailTemplatePlatformChange = (
    value: EmailTemplate["platform"]
  ) => {
    setNewEmailTemplateData((prev) => ({ ...prev, platform: value }));
  };

  const handlePhaseSelectionChange = (phaseId: string) => {
    setNewTemplateData((prev) => ({ ...prev, phaseId: phaseId }));
  };

  const handleEmailPhaseSelectionChange = (phaseId: string) => {
    setNewEmailTemplateData((prev) => ({ ...prev, phaseId: phaseId }));
  };

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById("body") as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const before = text.substring(0, start);
      const after = text.substring(end, text.length);
      const newText = before + variable + after;

      setNewEmailTemplateData((prev) => ({ ...prev, body: newText }));

      // Set cursor position after inserted variable
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd =
          start + variable.length;
        textarea.focus();
      }, 0);
    }
  };

  const handleGenerateQuestions = async () => {
    setIsGeneratingQuestions(true);

    // Use actual user input from the form
    const token = await getToken();
    if (!token) {
      console.error("No token available");
      setIsGeneratingQuestions(false);
      return;
    }

    const requestBody = {
      projectId: parseInt(projectId), // Use actual project ID
      params: {
        role: newTemplateData.role,
        level: newTemplateData.level,
        techStack: typeof newTemplateData.techStack === 'string' 
          ? newTemplateData.techStack.split(",").map((tech) => tech.trim())
          : [],
        questionCount: newTemplateData.questionCount,
        interviewDuration: newTemplateData.interviewDuration,
      },
      token: token
    };

    const response = await dispatch(generateQuestions(requestBody));
    const generatedQuestions: InterviewQuestion = response.payload;
    // console.log(generatedQuestions);
    setNewTemplateData((prev) => ({
      ...prev,
      questions: generatedQuestions,
    }));

    setIsGeneratingQuestions(false);
    setShowQuestions(true);
  };

  const handleAddQuestion = () => {
    if (!newQuestion.question.trim() || !newQuestion.category.trim()) {
      alert("Please fill in the question and category fields.");
      return;
    }

    const newQuestionObj = {
      question: newQuestion.question,
      difficulty: newQuestion.difficulty,
      category: newQuestion.category,
      followUp: newQuestion.followUp || []
    };

    // Create a default section if none exists
    setNewTemplateData((prev) => {
      const updatedQuestions = { ...prev.questions };
      
      // If no sections exist, create one
      if (!updatedQuestions.sections || updatedQuestions.sections.length === 0) {
        updatedQuestions.sections = [{
          name: "General Questions",
          description: "Default section for interview questions",
          questions: [newQuestionObj]
        }];
      } else {
        // Otherwise add to the first section
        updatedQuestions.sections[0].questions.push(newQuestionObj);
      }
      
      return {
        ...prev,
        questions: updatedQuestions,
        questionCount: prev.questionCount + 1
      };
    });

    setNewQuestion({
      question: "",
      difficulty: "Medium",
      category: "",
      followUp: []
    });
  };

  const handleEditQuestion = (question: any, sectionIndex: number, questionIndex: number) => {
    // Toggle editing mode for this question
    if (editingQuestionIndex?.sectionIndex === sectionIndex && 
        editingQuestionIndex?.questionIndex === questionIndex) {
      setEditingQuestionIndex(null);
      setEditingQuestion(null);
    } else {
      setEditingQuestionIndex({ sectionIndex, questionIndex });
      setEditingQuestion(question);
    }
  };

  const handleUpdateQuestion = (sectionIndex: number, qIndex: number, questionText: string) => {
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Check if sections exist and the indexes are valid
    if (updatedQuestions.sections && 
        updatedQuestions.sections[sectionIndex] && 
        updatedQuestions.sections[sectionIndex].questions &&
        updatedQuestions.sections[sectionIndex].questions[qIndex]) {
      
      // Update the question text
      updatedQuestions.sections[sectionIndex].questions[qIndex].question = questionText;
      
      // Update the state
      setNewTemplateData(prev => ({ ...prev, questions: updatedQuestions }));
    }
  };
  
  const handleUpdateQuestionField = (sectionIndex: number, qIndex: number, field: 'difficulty' | 'category', value: string) => {
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Check if sections exist and the indexes are valid
    if (updatedQuestions.sections && 
        updatedQuestions.sections[sectionIndex] && 
        updatedQuestions.sections[sectionIndex].questions &&
        updatedQuestions.sections[sectionIndex].questions[qIndex]) {
      
      // Type-safe update of the specific field
      if (field === 'difficulty') {
        updatedQuestions.sections[sectionIndex].questions[qIndex].difficulty = value;
      } else if (field === 'category') {
        updatedQuestions.sections[sectionIndex].questions[qIndex].category = value;
      }
      
      // Update the state
      setNewTemplateData(prev => ({ ...prev, questions: updatedQuestions }));
    }
  };
  
  const handleUpdateFollowUp = (sectionIndex: number, qIndex: number, followUpIndex: number, value: string) => {
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Check if sections, questions, and followUps exist
    if (updatedQuestions.sections && 
        updatedQuestions.sections[sectionIndex] && 
        updatedQuestions.sections[sectionIndex].questions &&
        updatedQuestions.sections[sectionIndex].questions[qIndex] &&
        updatedQuestions.sections[sectionIndex].questions[qIndex].followUp) {
      
      // Create a new follow-ups array with the updated value
      const updatedFollowUps = [...updatedQuestions.sections[sectionIndex].questions[qIndex].followUp];
      updatedFollowUps[followUpIndex] = value;
      
      // Update the question's followUp array
      updatedQuestions.sections[sectionIndex].questions[qIndex].followUp = updatedFollowUps;
      
      // Update the state
      setNewTemplateData(prev => ({ ...prev, questions: updatedQuestions }));
    }
  };
  
  const handleAddFollowUp = (sectionIndex: number, qIndex: number) => {
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Check if sections and questions exist
    if (updatedQuestions.sections && 
        updatedQuestions.sections[sectionIndex] && 
        updatedQuestions.sections[sectionIndex].questions &&
        updatedQuestions.sections[sectionIndex].questions[qIndex]) {
      
      // Initialize followUp array if it doesn't exist
      if (!updatedQuestions.sections[sectionIndex].questions[qIndex].followUp) {
        updatedQuestions.sections[sectionIndex].questions[qIndex].followUp = [];
      }
      
      // Add a new empty follow-up question
      updatedQuestions.sections[sectionIndex].questions[qIndex].followUp.push("New follow-up question");
      
      // Update the state
      setNewTemplateData(prev => ({ ...prev, questions: updatedQuestions }));
    }
  };
  
  const handleDeleteFollowUp = (sectionIndex: number, qIndex: number, followUpIndex: number) => {
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Check if sections, questions, and followUps exist
    if (updatedQuestions.sections && 
        updatedQuestions.sections[sectionIndex] && 
        updatedQuestions.sections[sectionIndex].questions &&
        updatedQuestions.sections[sectionIndex].questions[qIndex] &&
        updatedQuestions.sections[sectionIndex].questions[qIndex].followUp) {
      
      // Filter out the follow-up at the specified index
      updatedQuestions.sections[sectionIndex].questions[qIndex].followUp = 
        updatedQuestions.sections[sectionIndex].questions[qIndex].followUp.filter((_, i) => i !== followUpIndex);
      
      // Update the state
      setNewTemplateData(prev => ({ ...prev, questions: updatedQuestions }));
    }
  };

  const handleAddNewSection = () => {
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Initialize sections array if it doesn't exist
    if (!updatedQuestions.sections) {
      updatedQuestions.sections = [];
    }
    
    // Add a new section with a default question
    updatedQuestions.sections.push({
      name: `Section ${updatedQuestions.sections.length + 1}`,
      description: "New interview section",
      questions: [{
        question: "New interview question?",
        difficulty: "",
        category: "",
        followUp: ["FolowUp questions 1?", "FollowUp question 2?"]
      }]
    });
    
    // Update the state
    setNewTemplateData(prev => ({ ...prev, questions: updatedQuestions }));
    
    // Increment the question count
    setNewTemplateData(prev => ({ 
      ...prev, 
      questionCount: prev.questionCount + 1 
    }));
  };

  const handleAddNewQuestion = (sectionIndex: number) => {
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Check if sections exist and the index is valid
    if (updatedQuestions.sections && updatedQuestions.sections[sectionIndex]) {
      // Add a new question to the specified section
      updatedQuestions.sections[sectionIndex].questions.push({
        question: "New interview question",
        difficulty: "Medium",
        category: "Technical",
        followUp: ["What experience do you have with this?"]
      });
      
      // Update the state
      setNewTemplateData(prev => ({ ...prev, questions: updatedQuestions }));
      
      // Increment the question count
      setNewTemplateData(prev => ({ 
        ...prev, 
        questionCount: prev.questionCount + 1 
      }));
    }
  };
  
  const handleUpdateEvaluationCriteria = (field: string, value: string) => {
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Check if evaluation criteria exists
    if (updatedQuestions.evaluationCriteria) {
      // Update the specific evaluation criteria field
      updatedQuestions.evaluationCriteria = {
        ...updatedQuestions.evaluationCriteria,
        [field]: value
      };
      
      // Update the state
      setNewTemplateData(prev => ({ ...prev, questions: updatedQuestions }));
    }
  };

  const handleSaveEditedQuestion = () => {
    // Early return if no question is being edited
    if (!editingQuestion || !editingQuestionIndex) return;
    
    const { sectionIndex, questionIndex } = editingQuestionIndex;
    
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Check if sections exist and the indexes are valid
    if (updatedQuestions.sections && 
        updatedQuestions.sections[sectionIndex] && 
        updatedQuestions.sections[sectionIndex].questions &&
        updatedQuestions.sections[sectionIndex].questions[questionIndex]) {
      
      // Update the specific question with the new values
      updatedQuestions.sections[sectionIndex].questions[questionIndex] = {
        ...updatedQuestions.sections[sectionIndex].questions[questionIndex],
        question: newQuestion.question,
        difficulty: newQuestion.difficulty,
        category: newQuestion.category,
      };
      
      // Update the state
      setNewTemplateData(prev => ({
        ...prev,
        questions: updatedQuestions
      }));
    }

    // Reset editing state
    setEditingQuestion(null);
    setEditingQuestionIndex(null);
    setNewQuestion({
      question: "",
      difficulty: "Medium",
      category: "",
      followUp: [],
    });
  };

  const handleDeleteQuestion = (questionId: string) => {
    // Parse the questionId which is in the format "sectionIndex-questionIndex"
    const [sectionIndex, questionIndex] = questionId.split('-').map(Number);
    
    // Clone the template questions to safely update the nested structure
    const updatedQuestions = { ...newTemplateData.questions };
    
    // Check if sections exist and the indexes are valid
    if (updatedQuestions.sections && 
        updatedQuestions.sections[sectionIndex] && 
        updatedQuestions.sections[sectionIndex].questions) {
      
      // Remove the question at the specified index
      updatedQuestions.sections[sectionIndex].questions = 
        updatedQuestions.sections[sectionIndex].questions.filter((_, index) => index !== questionIndex);
      
      // If the section is now empty of questions, consider removing the section too
      if (updatedQuestions.sections[sectionIndex].questions.length === 0) {
        updatedQuestions.sections = updatedQuestions.sections.filter((_, index) => index !== sectionIndex);
      }
      
      // Calculate the new total question count
      const newQuestionCount = updatedQuestions.sections.reduce(
        (acc, section) => acc + section.questions.length,
        0
      );
      
      // Update the state with both the updated questions and the new count
      setNewTemplateData(prev => ({
        ...prev,
        questions: updatedQuestions,
        questionCount: newQuestionCount,
      }));
    }
  };

  const handleCancelEdit = () => {
    setEditingQuestion(null);
    setNewQuestion({
      question: "",
      difficulty: "Medium",
      category: "",
      followUp: [],
    });
  };

  const handleSaveNewEmailTemplate = async () => {
    if (
      !newEmailTemplateData.name ||
      !newEmailTemplateData.subject ||
      !newEmailTemplateData.body ||
      !newEmailTemplateData.phaseId
    ) {
      alert(
        "Please fill all required fields for the email template including phase assignment."
      );
      return;
    }

    const availableVariables = [
      "{candidate_name}",
      "{position_title}",
      "{company_name}",
      "{assessment_duration}",
      "{tech_stack}",
      "{deadline_date}",
      "{assessment_link}",
      "{contact_email}",
      "{recruiter_name}",
      "{platform_name}",
      "{programming_languages}",
      "{assessment_topics}",
      "{custom_instructions}",
      "{time_limit}",
    ];

    // Prepare data in the required format
    const emailTemplateData = {
      phaseId: parseInt(newEmailTemplateData.phaseId) || 1,
      name: newEmailTemplateData.name,
      description: `Email template for ${newEmailTemplateData.platform} platform`,
      templateType: "email" as const,
      subject: newEmailTemplateData.subject,
      body: newEmailTemplateData.body,
      variables: availableVariables,
      durationMinutes: 60, // Default duration for email templates
    };

    // console.log("Email template data to send:", emailTemplateData);
    const pid = parseInt(projectId);
    const token = await getToken();
    if (!token) {
      console.error("No token available");
      return;
    }

    try {
      if (editingTemplateId) {
        // Update existing template
        await dispatch(
          updateTemplate({
            projectId: pid,
            id: editingTemplateId,
            data: emailTemplateData,
            templatetype: "email",
            token: token
          })
        );
        // console.log("Email template updated successfully");
        setEditingTemplateId(null); // Clear editing state
      } else {
        // Create new template
        await dispatch(
          createEmailTemplate({
            projectId: pid,
            data: emailTemplateData,
            token: token
          })
        );
        // console.log("New email template created successfully");
      }

      // Refresh template list
      fetchData();

      // Reset form
      setNewEmailTemplateData({
        name: "",
        subject: "",
        body: "",
        platform: "Generic",
        phaseId: "",
      });
    } catch (error) {
      console.error("Error saving email template:", error);
    }
  };

  const handleSaveNewTemplate = async () => {
    if (
      !newTemplateData.role ||
      !newTemplateData.level ||
      !newTemplateData.techStack ||
      newTemplateData.questionCount === 0
    ) {
      alert("Please fill all fields and generate question count.");
      return;
    }

    const templateName = `${newTemplateData.role} ${newTemplateData.level} - ${newTemplateData.type}`;
    const pid = parseInt(projectId);
    const token = await getToken();
    if (!token) {
      console.error("No token available");
      return;
    }

    // Prepare template data in the format expected by the API
    const templateData = {
      name: templateName,
      phaseId: parseInt(newTemplateData.phaseId || "0"), // Ensure phaseId is a number
      templateType: "structural" as const, // Type assertion to match DTO
      type: "structural" as const, // Type assertion to match DTO
      role: newTemplateData.role,
      level: newTemplateData.level,
      // Convert techStack string to array for API
      techStack: typeof newTemplateData.techStack === 'string' 
        ? newTemplateData.techStack.split(",").map(tech => tech.trim())
        : [],
      questionCount: newTemplateData.questionCount,
      // Convert questions object to array format expected by API
      questions: newTemplateData.questions,
      durationMinutes: newTemplateData.interviewDuration,
    };

    try {
      if (editingTemplateId) {
        // Update existing template
        await dispatch(
          updateTemplate({
            projectId: pid,
            id: editingTemplateId,
            data: templateData,
            templatetype: "structural",
            token: token
          })
        );
        // console.log("Template updated successfully");
        setEditingTemplateId(null); // Clear editing state
      } else {
        // Create new template
        await dispatch(
          createStructuralTemplate({
            projectId: pid,
            data: templateData,
            token: token
          })
        );
        // console.log("New template created successfully");
      }

      // Refresh template list
      fetchData();

      // Reset form
      setNewTemplateData({
        role: "",
        level: "",
        type: "Technical",
        templateType: "structural",
        techStack: "",
        questionCount: 0,
        interviewDuration: 60,
        phaseId: "",
        questions: {} as InterviewQuestion,
      });
      setEditingQuestionIndex(null);
      setEditingQuestion(null);
      setEditingEvaluationCriteria(false);
      setShowQuestions(false);
    } catch (error) {
      console.error("Error saving template:", error);
    }
  };

  const handleContinue = () => {
    onNext(); // Proceed to the next section directly
  };

  const handleGoBack = () => {
    setCurrentStep("create");
  };

  const handleViewQuestions = (template: InterviewTemplate) => {
    if (template.questions) {
      setSelectedTemplate(template);
      setIsInterviewModalOpen(true);
    }
  };

  const handleViewTemplateDetails = (template: InterviewTemplate) => {
    setSelectedTemplate(template);
    // console.log("selected template: ", template);
    // console.log("selectedTemplate.questions:", template.questions);

    setIsInterviewModalOpen(true);
  };
  const handleDeleteTemplate = async (templateId: string | number) => {
    const idNum =
      typeof templateId === "string" ? parseInt(templateId) : templateId;
    const token = await getToken();
    if (!token) {
      console.error("No token available");
      return;
    }
    await dispatch(
      deleteTemplate({ projectId: parseInt(projectId), id: idNum, token })
    );
    fetchData();
  };

  const handleEditTemplate = async (templateId: string | number) => {
    const pid = parseInt(projectId);
    const idNum =
      typeof templateId === "string" ? parseInt(templateId) : templateId;
    const token = await getToken();
    if (!token) {
      console.error("No token available");
      return;
    }
    try {
      const response = await dispatch(
        fetchTemplateById({ projectId: pid, id: idNum, token })
      );
      // console.log("template payload by id: ", response.payload);

      const templateData: any = response.payload;

      if (
        templateData &&
        templateData.template &&
        templateData.template.type === "structural"
      ) {
        // Populate the structural template form
        setNewTemplateData({
          role: templateData.template.role || "",
          level: templateData.template.level || "",
          type: "Technical", // Default to technical
          templateType: "structural",
          techStack: templateData.template.techStack
            ? templateData.template.techStack.join(", ")
            : "",
          questionCount: templateData.template.questionCount || 0,
          interviewDuration: templateData.durationMinutes || 60,
          phaseId: templateData.phaseId ? templateData.phaseId.toString() : "",
          // Handle questions coming from API - transform to expected format if needed
          questions: templateData.template.questions
            ? Array.isArray(templateData.template.questions)
              ? {
                  sections: [{
                    name: "Main Section",
                    questions: templateData.template.questions
                  }],
                  evaluationCriteria: templateData.template.evaluationCriteria || {
                    technical: "",
                    communication: "",
                    problemSolving: ""
                  }
                }
              : templateData.template.questions || {}
            : {},
        });

        // Show questions if they exist
        if (templateData.template.questions) {
          setShowQuestions(true);
        }

        // Set editing mode
        setEditingTemplateId(templateData.id);

        // Switch to create template view
        setViewMode("create");

        // Scroll to form
        document
          .querySelector("[id='role']")
          ?.scrollIntoView({ behavior: "smooth" });
      }
    } catch (error) {
      console.error("Error fetching template:", error);
    }
  };
  const handleDeleteEmailTemplate = async (templateId: string | number) => {
    const idNum =
      typeof templateId === "string" ? parseInt(templateId) : templateId;
    const token = await getToken();
    if (!token) {
      console.error("No token available");
      return;
    }
    await dispatch(
      deleteTemplate({ projectId: parseInt(projectId), id: idNum, token })
    );
    fetchData();
  };

  const handleEditEmailTemplate = async (templateId: string | number) => {
    const pid = parseInt(projectId);
    const idNum =
      typeof templateId === "string" ? parseInt(templateId) : templateId;
    const token = await getToken();
    if (!token) {
      console.error("No token available");
      return;
    }
    try {
      const response = await dispatch(
        fetchTemplateById({ projectId: pid, id: idNum, token })
      );
      // console.log("email template payload by id: ", response.payload);

      const templateData: any = response.payload;

      if (
        templateData &&
        templateData.template &&
        templateData.template.type === "email"
      ) {
        // Populate the email template form
        setNewEmailTemplateData({
          name: templateData.name || "",
          subject: templateData.template.subject || "",
          body: templateData.template.body || "",
          platform: "Generic", // Default platform
          phaseId: templateData.phaseId ? templateData.phaseId.toString() : "",
        });

        // Set editing mode
        setEditingTemplateId(templateData.id);

        // Switch to email template view
        setViewMode("email");

        // Scroll to form
        document
          .querySelector("[id='name']")
          ?.scrollIntoView({ behavior: "smooth" });
      }
    } catch (error) {
      console.error("Error fetching email template:", error);
    }
  };

  const handleViewEmailTemplateDetails = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setIsEmailModalOpen(true);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy":
        return "bg-green-100 text-green-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      case "Hard":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Technical":
        return "bg-blue-100 text-blue-800";
      case "Behavioral":
        return "bg-purple-100 text-purple-800";
      case "System Design":
        return "bg-orange-100 text-orange-800";
      case "Problem-Solving":
        return "bg-pink-100 text-pink-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (currentStep === "assign") {
    return (
      <Card className="w-full max-w-4xl">
        <CardHeader>
          <CardTitle>Templates Created</CardTitle>
          <CardDescription>
            Templates have been created with phase assignments. Continue to the
            next step.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={onNext} className="w-full">
            Continue to Next Step
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </CardContent>
      </Card>
    );
  }
  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle>Create Interview Templates</CardTitle>
        <CardDescription>
          Create interview templates and assign them directly to specific phases
          in your project.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Step Indicator */}
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                1
              </div>
              <span className="font-medium">
                Create Templates with Phase Assignment
              </span>
            </div>
          </div>
          <Badge variant="outline">Template Creation</Badge>
        </div>

        {/* Available Templates Section - Always Visible */}
        <div className="space-y-4" data-templates-section>
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Available Templates</h3>
            <div className="flex items-center gap-2">
              {templates.length > 0 && (
                <Badge variant="outline">
                  {templates.length} interview template
                  {templates.length !== 1 ? "s" : ""}
                </Badge>
              )}
              {emailTemplates.length > 0 && (
                <Badge variant="outline">
                  {emailTemplates.length} email template
                  {emailTemplates.length !== 1 ? "s" : ""}
                </Badge>
              )}
            </div>
          </div>

          {/* Interview Templates */}
          {templates.length > 0 ? (
            <div className="space-y-3">
              <h4 className="font-medium text-sm text-muted-foreground">
                Interview Templates
              </h4>
              <div className="grid gap-3">
                {templates.map((template) => (
                  <Card key={template.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{template.name}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="outline">{template.type}</Badge>
                          <span className="text-sm text-muted-foreground">
                            {template.questionCount} questions
                          </span>
                          <span className="text-sm text-muted-foreground">
                            •
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {template.dateModified}
                          </span>
                        </div>
                        {template.role && template.level && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {template.role} • {template.level} •{" "}
                            {template.techStack}
                          </p>
                        )}
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewTemplateDetails(template)}
                          >
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEditTemplate(template.id)}
                          >
                            Edit Template
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteTemplate(template.id)}
                            className="text-red-600"
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 bg-muted/30 rounded-lg border-2 border-dashed">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h4 className="font-medium mb-2">No Interview Templates Yet</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Create your first interview template to get started with the
                assignment process.
              </p>
            </div>
          )}

          {/* Email Templates */}
          {emailTemplates.length > 0 && (
            <div className="space-y-3">
              <h4 className="font-medium text-sm text-muted-foreground">
                Email Templates
              </h4>
              <div className="grid gap-3">
                {emailTemplates.map((template) => (
                  <Card key={template.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{template.name}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary">{template.platform}</Badge>
                          <span className="text-sm text-muted-foreground">
                            {template.dateModified}
                          </span>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() =>
                              handleViewEmailTemplateDetails(template)
                            }
                          >
                            Preview Email
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEditEmailTemplate(template.id)}
                          >
                            Edit Template
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              handleDeleteEmailTemplate(template.id)
                            }
                            className="text-red-600"
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Template Creation Section */}
        <div className="border-t pt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Create New Templates</h3>
          </div>
          <div className="flex space-x-2 mb-4">
            <Button
              variant={viewMode === "create" ? "default" : "outline"}
              onClick={() => setViewMode("create")}
              className="flex-1"
            >
              <Code className="h-4 w-4 mr-2" />
              Create Interview Template
            </Button>
            <Button
              variant={viewMode === "email" ? "default" : "outline"}
              onClick={() => setViewMode("email")}
              className="flex-1"
            >
              <Mail className="h-4 w-4 mr-2" />
              Create Email Template
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                // Scroll to templates section
                document
                  .querySelector("[data-templates-section]")
                  ?.scrollIntoView({ behavior: "smooth" });
              }}
              className="flex-none"
            >
              <FileText className="h-4 w-4 mr-2" />
              View Templates ({templates.length + emailTemplates.length})
            </Button>
          </div>
        </div>

        {viewMode === "email" ? (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                Create Email Template for External Platforms
              </h3>
              <Badge variant="outline" className="flex items-center gap-1">
                <Mail className="h-3 w-3" />
                External Assessment
              </Badge>
            </div>

            <div className="space-y-6">
              <h4 className="text-lg font-semibold">
                Create New Email Template
              </h4>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="grid gap-2">
                  <Label htmlFor="email-name">Template Name *</Label>
                  <Input
                    id="name"
                    placeholder="e.g., HackerRank Frontend Assessment"
                    value={newEmailTemplateData.name}
                    onChange={handleNewEmailTemplateChange}
                    required
                  />
                </div>
                {/* <div className="grid gap-2">
                  <Label htmlFor="email-platform">Platform</Label>
                  <Select
                    onValueChange={handleNewEmailTemplatePlatformChange}
                    value={newEmailTemplateData.platform}
                  >
                    <SelectTrigger id="email-platform">
                      <SelectValue placeholder="Select platform" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="HackerRank">HackerRank</SelectItem>
                      <SelectItem value="Codility">Codility</SelectItem>
                      <SelectItem value="LeetCode">LeetCode</SelectItem>
                      <SelectItem value="Custom">Custom Platform</SelectItem>
                      <SelectItem value="Generic">Generic</SelectItem>
                    </SelectContent>
                  </Select>
                </div> */}
              </div>

              {/* Phase Assignment Section for Email Templates */}
              <div className="grid gap-2">
                <Label htmlFor="emailAssignedPhase">Assign to Phase</Label>
                <Select
                  onValueChange={handleEmailPhaseSelectionChange}
                  value={newEmailTemplateData.phaseId || ""}
                  disabled={phasesLoading}
                >
                  <SelectTrigger id="emailAssignedPhase">
                    <SelectValue placeholder={phasesLoading ? "Loading phases..." : "Select a phase for this email template"} />
                  </SelectTrigger>
                  <SelectContent>
                    {availablePhases.length > 0 ? (
                      availablePhases.map((phase) => (
                        <SelectItem key={phase.id} value={phase.id}>
                          {phase.name} - {phase.description}
                        </SelectItem>
                      ))
                    ) : (
                      <SelectItem value="" disabled>
                        {phasesLoading ? "Loading phases..." : "No phases available"}
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Select which phase this email template will be used for.
                </p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email-subject">Email Subject *</Label>
                <Input
                  id="subject"
                  placeholder="e.g., Technical Assessment Invitation - {position_title}"
                  value={newEmailTemplateData.subject}
                  onChange={handleNewEmailTemplateChange}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Use variables like {"{position_title}"}, {"{candidate_name}"},{" "}
                  {"{company_name}"}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email-body">Email Body *</Label>
                <div className="space-y-2">
                  <div className="flex flex-wrap gap-1">
                    <span className="text-sm text-muted-foreground mr-2">
                      Quick Insert:
                    </span>
                    {[
                      "{candidate_name}",
                      "{position_title}",
                      "{company_name}",
                      "{assessment_duration}",
                      "{deadline_date}",
                      "{assessment_link}",
                      "{contact_email}",
                      "{recruiter_name}",
                    ].map((variable) => (
                      <Button
                        key={variable}
                        variant="outline"
                        size="sm"
                        onClick={() => insertVariable(variable)}
                        className="text-xs h-6 px-2"
                      >
                        {variable}
                      </Button>
                    ))}
                  </div>
                  <Textarea
                    id="body"
                    placeholder="Write your email template here. Use variables like {candidate_name} for personalization..."
                    value={newEmailTemplateData.body}
                    onChange={handleNewEmailTemplateChange}
                    rows={12}
                    required
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  Available variables: {"{candidate_name}"},{" "}
                  {"{position_title}"}, {"{company_name}"},{" "}
                  {"{assessment_duration}"},{"{tech_stack}"},{" "}
                  {"{deadline_date}"}, {"{assessment_link}"},{" "}
                  {"{contact_email}"}, {"{recruiter_name}"}
                </p>
              </div>

              <Button onClick={handleSaveNewEmailTemplate} className="w-full">
                <Mail className="h-4 w-4 mr-2" />
                {editingTemplateId
                  ? "Update Email Template"
                  : "Save Email Template"}
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">
              Create New Interview Template
            </h3>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="grid gap-2">
                <Label htmlFor="role">Role</Label>
                <Input
                  id="role"
                  placeholder="e.g., Software Engineer"
                  value={newTemplateData.role}
                  onChange={handleNewTemplateChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="level">Level</Label>
                <Select
                  onValueChange={(value) => setNewTemplateData(prev => ({ ...prev, level: value }))}
                  value={newTemplateData.level}
                >
                  <SelectTrigger id="level">
                    <SelectValue placeholder="Select experience level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ENTRY">ENTRY</SelectItem>
                    <SelectItem value="JUNIOR">JUNIOR</SelectItem>
                    <SelectItem value="MID">MID</SelectItem>
                    <SelectItem value="SENIOR">SENIOR</SelectItem>
                    <SelectItem value="EXECUTIVE">EXECUTIVE</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="type">Type</Label>
              <Select
                onValueChange={handleNewTemplateTypeChange}
                value={newTemplateData.type}
              >
                <SelectTrigger id="type">
                  <SelectValue placeholder="Select interview type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Technical">Technical</SelectItem>
                  <SelectItem value="Behavioral">Behavioral</SelectItem>
                  <SelectItem value="Mixed">Mixed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="techStack">Tech Stack (comma-separated)</Label>
              <Textarea
                id="techStack"
                placeholder="e.g., React, Node.js, AWS, Python"
                value={newTemplateData.techStack}
                onChange={handleNewTemplateChange}
                rows={3}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="interviewDuration">
                Interview Duration (minutes)
              </Label>
              <Input
                id="interviewDuration"
                type="number"
                min="15"
                max="240"
                placeholder="e.g., 60"
                value={newTemplateData.interviewDuration}
                onChange={handleNewTemplateChange}
                required
              />
              <p className="text-sm text-muted-foreground">
                Recommended: 30-90 minutes depending on the role level and
                complexity.
              </p>
            </div>

            {/* Phase Assignment Section */}
            <div className="grid gap-2">
              <Label htmlFor="assignedPhase">Assign to Phase</Label>
              <Select
                onValueChange={handlePhaseSelectionChange}
                value={newTemplateData.phaseId || ""}
                disabled={phasesLoading}
              >
                <SelectTrigger id="assignedPhase">
                  <SelectValue placeholder={phasesLoading ? "Loading phases..." : "Select a phase for this template"} />
                </SelectTrigger>
                <SelectContent>
                  {availablePhases.length > 0 ? (
                    availablePhases.map((phase) => (
                      <SelectItem key={phase.id} value={phase.id}>
                        {phase.name} - {phase.description}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>
                      {phasesLoading ? "Loading phases..." : "No phases available"}
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">
                Select which phase this template will be used for.
              </p>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="questionCount">AI Generated Question Count</Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="questionCount"
                  type="number"
                  value={newTemplateData.questionCount}
                  onChange={handleNewTemplateChange}
                  // readOnly
                  className="flex-1"
                />
                <Button
                  onClick={handleGenerateQuestions}
                  disabled={isGeneratingQuestions}
                >
                  {isGeneratingQuestions ? (
                    "Generating..."
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" /> Generate Questions
                    </>
                  )}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Our AI will generate a recommended number of questions based on
                the role, level, and tech stack.
              </p>
            </div>

            {/* Questions Management Section */}
            {showQuestions && newTemplateData.questions && (
              <div className="space-y-4">
                <div className="space-y-8 mt-4">
                  {/* ---------- Metadata ---------- */}
                  {newTemplateData && (
                    <Card className="p-4">
                      <h4 className="text-lg font-semibold mb-3">
                        Interview Metadata
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-muted-foreground">
                        <div>
                          <span className="font-medium">Role:</span>{" "}
                          {newTemplateData.role}
                        </div>
                        <div>
                          <span className="font-medium">Level:</span>{" "}
                          {newTemplateData.level}
                        </div>
                        <div>
                          <span className="font-medium">Tech Stack:</span>{" "}
                          {/* {newTemplateData.techStack.join(", ")} */}
                          {newTemplateData.techStack}
                        </div>
                        <div>
                          <span className="font-medium">Total Duration:</span>{" "}
                          {newTemplateData.interviewDuration} mins
                        </div>
                        <div>
                          <span className="font-medium">Question Count:</span>{" "}
                          {newTemplateData.questionCount}
                        </div>
                      </div>
                    </Card>
                  )}

                  {/* ---------- Sections & Questions ---------- */}
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-semibold">
                        Sections & Questions
                      </h4>
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">
                          {newTemplateData.questions.sections?.reduce(
                            (acc, section) => acc + section.questions.length,
                            0
                          ) || 0}{" "}
                          questions
                        </Badge>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={handleAddNewSection}
                        >
                          <Plus className="h-4 w-4 mr-2" /> Add New Section
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-6">
                      {newTemplateData.questions.sections?.map(
                        (section, sectionIndex) => (
                          <div key={sectionIndex} className="space-y-3">
                            {/* Section Header */}
                            <div className="flex items-center justify-between">
                              <div>
                                <h5 className="text-md font-semibold">
                                  {section.name}
                                </h5>
                                {section.description && (
                                  <p className="text-sm text-muted-foreground">
                                    {section.description}
                                  </p>
                                )}
                              </div>
                              <div className="flex items-center gap-3">
                                <Badge variant="secondary">
                                  {section.questions.length} questions
                                </Badge>
                                <Button 
                                  variant="outline" 
                                  size="sm" 
                                  onClick={() => handleAddNewQuestion(sectionIndex)}
                                >
                                  <Plus className="h-4 w-4 mr-2" /> Add Question
                                </Button>
                              </div>
                            </div>

                            {/* Questions inside each section */}
                            <div className="space-y-3">
                              {section.questions.map((q, qIndex) => (
                                <Card
                                  key={`${sectionIndex}-${qIndex}`}
                                  className="p-4"
                                >
                                  <div className="space-y-3">
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-2">
                                          <span className="text-sm font-medium">
                                            Question {qIndex + 1}
                                          </span>
                                          {editingQuestionIndex?.sectionIndex === sectionIndex && 
                                           editingQuestionIndex?.questionIndex === qIndex ? (
                                            <Select
                                              value={q.difficulty}
                                              onValueChange={(value) => handleUpdateQuestionField(sectionIndex, qIndex, 'difficulty', value)}
                                            >
                                              <SelectTrigger className="w-24 h-7">
                                                <SelectValue />
                                              </SelectTrigger>
                                              <SelectContent>
                                                {['Easy', 'Medium', 'Hard'].map((difficulty) => (
                                                  <SelectItem key={difficulty} value={difficulty}>
                                                    {difficulty}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                          ) : (
                                            <Badge
                                              className={getDifficultyColor(
                                                q.difficulty
                                              )}
                                              variant="secondary"
                                            >
                                              {q.difficulty}
                                            </Badge>
                                          )}
                                          {editingQuestionIndex?.sectionIndex === sectionIndex && 
                                           editingQuestionIndex?.questionIndex === qIndex ? (
                                            <Select
                                              value={q.category}
                                              onValueChange={(value) => handleUpdateQuestionField(sectionIndex, qIndex, 'category', value)}
                                            >
                                              <SelectTrigger className="w-32 h-7">
                                                <SelectValue />
                                              </SelectTrigger>
                                              <SelectContent>
                                                {['Technical', 'Behavioral', 'Problem-Solving', 'System Design'].map((type) => (
                                                  <SelectItem key={type} value={type}>
                                                    {type}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                          ) : (
                                            <Badge variant="outline">
                                              {q.category}
                                            </Badge>
                                          )}
                                        </div>

                                        <Textarea
                                          className="text-sm text-muted-foreground mb-2"
                                          value={q.question}
                                          onChange={(e) => handleUpdateQuestion(sectionIndex, qIndex, e.target.value)}
                                          disabled={!(editingQuestionIndex?.sectionIndex === sectionIndex && 
                                                     editingQuestionIndex?.questionIndex === qIndex)}
                                          rows={3}
                                        />

                                        {/* Follow-up questions */}
                                        <div className="ml-4 mt-2 space-y-1">
                                          <p className="text-xs font-medium text-muted-foreground">
                                            Follow-ups:
                                          </p>
                                          {editingQuestionIndex?.sectionIndex === sectionIndex && 
                                           editingQuestionIndex?.questionIndex === qIndex ? (
                                            <>
                                              {q.followUp?.map((followUp, followUpIndex) => (
                                                <div key={followUpIndex} className="flex gap-2 items-center mb-2">
                                                  <Textarea
                                                    className="text-sm text-muted-foreground flex-1"
                                                    value={followUp}
                                                    onChange={(e) => handleUpdateFollowUp(sectionIndex, qIndex, followUpIndex, e.target.value)}
                                                    rows={2}
                                                  />
                                                  <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleDeleteFollowUp(sectionIndex, qIndex, followUpIndex)}
                                                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                                  >
                                                    <Trash2 className="h-4 w-4" />
                                                  </Button>
                                                </div>
                                              ))}
                                              <Button 
                                                variant="outline" 
                                                size="sm" 
                                                onClick={() => handleAddFollowUp(sectionIndex, qIndex)}
                                                className="mt-2"
                                              >
                                                <Plus className="h-4 w-4 mr-2" />
                                                Add Follow-up
                                              </Button>
                                            </>
                                          ) : (
                                            <ul className="list-disc list-inside text-sm text-muted-foreground">
                                              {q.followUp?.map((f, i) => (
                                                <li key={i}>{f}</li>
                                              ))}
                                            </ul>
                                          )}
                                        </div>
                                      </div>

                                      {/* Actions */}
                                      <div className="flex items-center gap-1 ml-4">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleEditQuestion(q, sectionIndex, qIndex)}
                                          className="h-8 w-8 p-0"
                                        >
                                          {editingQuestionIndex?.sectionIndex === sectionIndex && 
                                           editingQuestionIndex?.questionIndex === qIndex ? (
                                            <Save className="h-4 w-4" />
                                          ) : (
                                            <Edit className="h-4 w-4" />
                                          )}
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() =>
                                            handleDeleteQuestion(
                                              `${sectionIndex}-${qIndex}`
                                            )
                                          }
                                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                        >
                                          <Trash2 className="h-4 w-4" />
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                </Card>
                              ))}
                            </div>
                          </div>
                        )
                      )}
                    </div>
                  </div>

                  {/* ---------- Evaluation Criteria ---------- */}
                  {newTemplateData.questions.evaluationCriteria && (
                    <Card className="p-4">
                      <h4 className="text-lg font-semibold mb-3">
                        Evaluation Criteria
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm text-muted-foreground">
                        <div>
                          <span className="font-medium">Technical:</span>{" "}
                          <Textarea
                            className="mt-2"
                            value={newTemplateData.questions.evaluationCriteria?.technical || ""}
                            onChange={(e) => handleUpdateEvaluationCriteria("technical", e.target.value)}
                            disabled={!editingEvaluationCriteria}
                            rows={3}
                          />
                        </div>
                        <div>
                          <span className="font-medium">Communication:</span>{" "}
                          <Textarea
                            className="mt-2"
                            value={newTemplateData.questions.evaluationCriteria?.communication || ""}
                            onChange={(e) => handleUpdateEvaluationCriteria("communication", e.target.value)}
                            disabled={!editingEvaluationCriteria}
                            rows={3}
                          />
                        </div>
                        <div>
                          <span className="font-medium">Problem Solving:</span>{" "}
                          <Textarea
                            className="mt-2"
                            value={newTemplateData.questions.evaluationCriteria?.problemSolving || ""}
                            onChange={(e) => handleUpdateEvaluationCriteria("problemSolving", e.target.value)}
                            disabled={!editingEvaluationCriteria}
                            rows={3}
                          />
                        </div>
                      </div>
                      <div className="flex justify-end mt-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingEvaluationCriteria(!editingEvaluationCriteria)}
                        >
                          {editingEvaluationCriteria ? (
                            <>
                              <Save className="h-4 w-4 mr-2" />
                              Save Criteria
                            </>
                          ) : (
                            <>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Criteria
                            </>
                          )}
                        </Button>
                      </div>
                    </Card>
                  )}
                </div>
              </div>
            )}

            <Button onClick={handleSaveNewTemplate} className="w-full">
              {editingTemplateId ? (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Template
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save New Template
                </>
              )}
            </Button>
          </div>
        )}

        <Button onClick={handleContinue} className="w-full">
          Continue to Next Step
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>

        {/* Dialog for Viewing Interview Template Details */}
        <Dialog
          open={isInterviewModalOpen}
          onOpenChange={setIsInterviewModalOpen}
        >
          <DialogContent className="bg-gray-50 p-6 rounded-lg shadow-lg max-h-[80vh] overflow-y-auto w-[90vw] max-w-4xl">
            <DialogHeader>
              <DialogTitle className="text-xl font-bold text-gray-800">
                Template Details
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-600">
                Detailed information about the selected template.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              {selectedTemplate &&
                "questions" in selectedTemplate &&
                selectedTemplate.questions && (
                  <>
                    {/* Metadata Section */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-700">
                        Metadata
                      </h3>
                      <p className="text-sm text-gray-600">
                        Role:{" "}
                        {selectedTemplate &&
                          selectedTemplate.role}
                      </p>
                      <p className="text-sm text-gray-600">
                        Level:{" "}
                        {selectedTemplate &&
                          selectedTemplate.level}
                      </p>
                      <p className="text-sm text-gray-600">
                        Tech Stack:{" "}
                        {selectedTemplate.techStack &&
                          selectedTemplate.techStack.join(
                            ", "
                          )}
                      </p>
                      <p className="text-sm text-gray-600">
                        Total Duration:{" "}
                        {selectedTemplate &&
                          selectedTemplate.interviewDuration}{" "}
                        minutes
                      </p>
                      <p className="text-sm text-gray-600">
                        Question Count:{" "}
                        {selectedTemplate &&
                          selectedTemplate.questionCount}
                      </p>
                    </div>

                    {/* Sections */}
                    {
                    selectedTemplate.questions.sections &&
                      selectedTemplate.questions.sections.map(
                        (section, sectionIndex) => (
                          <div key={sectionIndex} className="mt-4">
                            <h4 className="text-md font-medium text-gray-700">
                              {section.name}
                            </h4>
                            <p className="text-sm text-gray-600">
                              {section.description}
                            </p>
                            <div className="mt-2 space-y-4">
                              {section.questions.map(
                                (question, questionIndex) => (
                                  <div
                                    key={questionIndex}
                                    className="p-4 border rounded-lg bg-white shadow-sm hover:shadow-md transition-shadow"
                                  >
                                    <h5 className="font-medium text-gray-700">
                                      {questionIndex + 1}. {question.question}
                                    </h5>
                                    <div className="flex items-center gap-2 mt-2">
                                      <span
                                        className={`px-2 py-1 text-xs font-semibold rounded ${getTypeColor(
                                          question.category
                                        )}`}
                                      >
                                        {question.category}
                                      </span>
                                      <span
                                        className={`px-2 py-1 text-xs font-semibold rounded ${getDifficultyColor(
                                          question.difficulty
                                        )}`}
                                      >
                                        {question.difficulty}
                                      </span>
                                    </div>
                                    {/* <p className="text-sm text-gray-600 mt-2">
                                    Expected Answer: {question.expectedAnswer}
                                  </p> */}
                                    {question.followUp.length > 0 && (
                                      <div className="mt-2">
                                        <h6 className="text-sm font-medium text-gray-700">
                                          Follow-Up Questions:
                                        </h6>
                                        <ul className="list-disc list-inside text-sm text-gray-600">
                                          {question.followUp.map(
                                            (followUp, followUpIndex) => (
                                              <li key={followUpIndex}>
                                                {followUp}
                                              </li>
                                            )
                                          )}
                                        </ul>
                                      </div>
                                    )}
                                  </div>
                                )
                              )}
                            </div>
                          </div>
                        )
                      )}

                    {/* Evaluation Criteria */}
                    <div className="mt-4">
                      <h3 className="text-lg font-semibold text-gray-700">
                        Evaluation Criteria
                      </h3>
                      <p className="text-sm text-gray-600">
                        Technical:{" "}
                        {selectedTemplate.questions.evaluationCriteria &&
                          selectedTemplate.questions.evaluationCriteria
                            .technical}
                      </p>
                      <p className="text-sm text-gray-600">
                        Communication:{" "}
                        {selectedTemplate.questions.evaluationCriteria &&
                          selectedTemplate.questions.evaluationCriteria
                            .communication}
                      </p>
                      <p className="text-sm text-gray-600">
                        Problem Solving:{" "}
                        {selectedTemplate.questions.evaluationCriteria &&
                          selectedTemplate.questions.evaluationCriteria
                            .problemSolving}
                      </p>
                    </div>
                  </>
                )}
            </div>
            <div className="flex justify-end mt-4">
              <Button
                variant="outline"
                onClick={() => setIsInterviewModalOpen(false)}
              >
                Close
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Dialog for Viewing Email Template Details */}
        <Dialog open={isEmailModalOpen} onOpenChange={setIsEmailModalOpen}>
          <DialogContent className="bg-gray-50 p-6 rounded-lg shadow-lg max-h-[80vh] overflow-y-auto w-[90vw] max-w-4xl">
            <DialogHeader>
              <DialogTitle className="text-xl font-bold text-gray-800">
                Email Template Details
              </DialogTitle>
              <DialogDescription className="text-sm text-gray-600">
                Detailed information about the selected email template.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {selectedTemplate && "variables" in selectedTemplate && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-700">
                    {selectedTemplate.name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    Subject: {selectedTemplate.subject}
                  </p>
                  <p className="text-sm text-gray-600">
                    Platform: {selectedTemplate.platform}
                  </p>
                  <p className="text-sm text-gray-600">
                    Date Modified: {selectedTemplate.dateModified}
                  </p>
                  <div className="mt-4">
                    <h4 className="text-md font-medium text-gray-700">Body:</h4>
                    <p className="text-sm text-gray-600 whitespace-pre-wrap">
                      {selectedTemplate.body}
                    </p>
                  </div>
                  <div className="mt-4">
                    <h4 className="text-md font-medium text-gray-700">
                      Available Variables:
                    </h4>
                    <ul className="list-disc list-inside text-sm text-gray-600">
                      {selectedTemplate.variables.map((variable, index) => (
                        <li key={index}>{variable}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}
            </div>
            <div className="flex justify-end mt-4">
              <Button
                variant="outline"
                onClick={() => setIsEmailModalOpen(false)}
              >
                Close
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
