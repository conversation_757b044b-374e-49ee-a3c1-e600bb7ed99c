"use client";

import React, { useState, useMemo, useEffect } from 'react';
import { usePhases, Phase } from '../../../../../../hooks/usePhases';
import { usePhaseCandidates, PhaseCandidate } from '../../../../../../hooks/usePhaseCandidates';
import { usePhaseActions } from '../../../../../../hooks/usePhaseActions';
import { PhaseCandidateDetailModal } from './phase-candidate-detail-modal';
import { BulkActionModal } from './bulk-action-modal';
import { WorkflowStepper } from './workflow-stepper';
import {
  Download,
  Mail,
  Send,
  MapPin,
  Briefcase,
  Star,
  Filter,
  Search,
  X,
  Eye,
  CheckCircle2,
  XCircle,
  Clock,
  AlertCircle,
  ArrowRight,
  Lock,
  FileText,
  TrendingUp,
  Users,
  CheckCheck,
  Ban,
  ChevronRight,
  Activity,
  Loader2,
  RefreshCw,
} from 'lucide-react';

type CandidatePhaseStatus = "PENDING" | "QUALIFIED" | "REJECTED" | "IN_PROGRESS";

// UI Components (reusing from original)
const Badge = ({
  children,
  variant = "default",
  className = "",
}: {
  children: React.ReactNode;
  variant?: "default" | "secondary" | "destructive" | "outline" | "success" | "warning";
  className?: string;
}) => {
  const variants = {
    default: "bg-blue-100 text-blue-800 border border-blue-200",
    secondary: "bg-gray-100 text-gray-800 border border-gray-200",
    destructive: "bg-red-100 text-red-800 border border-red-200",
    outline: "bg-transparent border border-gray-300 text-gray-700",
    success: "bg-green-100 text-green-800 border border-green-200",
    warning: "bg-yellow-100 text-yellow-800 border border-yellow-200",
  };

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[variant]} ${className}`}
    >
      {children}
    </span>
  );
};

const Button = ({
  children,
  variant = "default",
  size = "default",
  disabled = false,
  onClick,
  className = "",
  ...props
}: {
  children: React.ReactNode;
  variant?: "default" | "outline" | "ghost" | "destructive" | "success";
  size?: "default" | "sm" | "lg";
  disabled?: boolean;
  onClick?: (e?: React.MouseEvent<HTMLButtonElement>) => void;
  className?: string;
  [key: string]: any;
}) => {
  const variants = {
    default: "bg-blue-600 text-white hover:bg-blue-700 border border-blue-600 shadow-sm",
    outline: "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 shadow-sm",
    ghost: "bg-transparent text-gray-700 hover:bg-gray-100 border-0",
    destructive: "bg-red-600 text-white hover:bg-red-700 border border-red-600 shadow-sm",
    success: "bg-green-600 text-white hover:bg-green-700 border border-green-600 shadow-sm",
  };

  const sizes = {
    default: "px-4 py-2 text-sm",
    sm: "px-3 py-1.5 text-xs",
    lg: "px-6 py-3 text-base",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${variants[variant]} ${sizes[size]} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

const Card = ({ children, className = "" }: { children: React.ReactNode; className?: string }) => (
  <div className={`bg-white rounded-xl border border-gray-200 shadow-sm ${className}`}>{children}</div>
);

const CardHeader = ({ children, className = "" }: { children: React.ReactNode; className?: string }) => (
  <div className={`px-6 py-5 border-b border-gray-100 ${className}`}>{children}</div>
);

const CardContent = ({ children, className = "" }: { children: React.ReactNode; className?: string }) => (
  <div className={`px-6 py-5 ${className}`}>{children}</div>
);

// Main Component with API Integration
export function PhaseCandidatesSectionConnected({ projectId }: { projectId: string }) {
  // Define workflow steps
  const workflowSteps = [
    { id: "job-offer", title: "Define Job Offer" },
    { id: "interview-phases", title: "Configure Interview Phases" },
    { id: "interview-template", title: "Choose Interview Template" },
    { id: "cv-upload", title: "Upload Candidate CVs" },
    { id: "candidates", title: "Manage Candidates" },
    { id: "project-overview", title: "Project Overview" },
  ];
  const currentStepIndex = 4; // Manage Candidates is step 5 (index 4)

  const { phases, loading: phasesLoading, error: phasesError } = usePhases(projectId);
  const [activePhaseId, setActivePhaseId] = useState<number | null>(null);
  const {
    candidates,
    loading: candidatesLoading,
    scoreCandidate,
    bulkScoreCandidates,
    updateCandidateStatus,
    refetch,
  } = usePhaseCandidates(activePhaseId);
  const { sendBulkEmail, sendBulkInterview, advanceCandidates, rejectCandidates } = usePhaseActions(
    activePhaseId || undefined
  );

  const [selectedCandidates, setSelectedCandidates] = useState<number[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [scoreFilter, setScoreFilter] = useState<[number, number]>([0, 100]);
  const [statusFilter, setStatusFilter] = useState<CandidatePhaseStatus[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Modal states
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<PhaseCandidate | null>(null);
  const [bulkActionModalOpen, setBulkActionModalOpen] = useState(false);
  const [bulkActionType, setBulkActionType] = useState<"EMAIL" | "INTERVIEW" | "SCORE">("EMAIL");
  const [actionLoading, setActionLoading] = useState(false);

  // Set active phase when phases load
  useEffect(() => {
    if (phases.length > 0 && !activePhaseId) {
      setActivePhaseId(phases[0].id);
    }
  }, [phases, activePhaseId]);

  const activePhase = phases.find((p: Phase) => p.id === activePhaseId);

  const filteredCandidates = useMemo(() => {
    return candidates.filter((candidate: PhaseCandidate) => {
      const matchesSearch =
        searchQuery === "" ||
        candidate.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        candidate.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        candidate.skills?.some((skill: string) => skill.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesScore =
        !candidate.phaseScore || (candidate.phaseScore >= scoreFilter[0] && candidate.phaseScore <= scoreFilter[1]);

      const matchesStatus =
        statusFilter.length === 0 || statusFilter.includes(candidate.phaseStatus || "PENDING");

      return matchesSearch && matchesScore && matchesStatus;
    });
  }, [candidates, searchQuery, scoreFilter, statusFilter]);

  const handleScoreCandidate = async (candidateId: number) => {
    try {
      setActionLoading(true);
      await scoreCandidate(candidateId);
    } catch (err) {
      console.error('Failed to score candidate:', err);
      alert('Failed to score candidate. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleBulkScore = async () => {
    try {
      setActionLoading(true);
      await bulkScoreCandidates();
      alert('Bulk scoring completed successfully!');
    } catch (err) {
      console.error('Failed to bulk score:', err);
      alert('Failed to bulk score candidates. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleQualifyCandidates = async () => {
    if (selectedCandidates.length === 0) return;

    try {
      setActionLoading(true);
      // Update each candidate's status to indicate they're qualified
      await Promise.all(selectedCandidates.map((id: number) => updateCandidateStatus(id, 'IN_REVIEW')));
      setSelectedCandidates([]);
    } catch (err) {
      console.error('Failed to qualify candidates:', err);
      alert('Failed to qualify candidates. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleRejectCandidates = async () => {
    if (selectedCandidates.length === 0) return;

    try {
      setActionLoading(true);
      await rejectCandidates(selectedCandidates);
      setSelectedCandidates([]);
      refetch();
    } catch (err) {
      console.error('Failed to reject candidates:', err);
      alert('Failed to reject candidates. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleAdvanceToNextPhase = async () => {
    const qualifiedCandidates = candidates.filter((c: PhaseCandidate) => c.phaseStatus === "QUALIFIED");

    if (qualifiedCandidates.length === 0) {
      alert('No qualified candidates to advance');
      return;
    }

    try {
      setActionLoading(true);
      await advanceCandidates(qualifiedCandidates.map((c: PhaseCandidate) => c.id));
      alert(`${qualifiedCandidates.length} candidates advanced to next phase!`);
      refetch();
    } catch (err) {
      console.error('Failed to advance candidates:', err);
      alert('Failed to advance candidates. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleClosePhase = async () => {
    if (!activePhaseId) return;
    if (!confirm("Are you sure you want to close this phase? It will become read-only.")) return;

    try {
      setActionLoading(true);
      // TODO: Implement closePhase API endpoint
      // await closePhase(activePhaseId)
      console.log('Close phase functionality to be implemented');
      alert('Phase closing feature coming soon!');
    } catch (err) {
      console.error('Failed to close phase:', err);
      alert('Failed to close phase. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const handleBulkAction = (actionType: "EMAIL" | "INTERVIEW" | "SCORE") => {
    setBulkActionType(actionType);
    setBulkActionModalOpen(true);
  };

  const handleBulkActionConfirm = async (data: any) => {
    try {
      setActionLoading(true);

      if (bulkActionType === "EMAIL") {
        await sendBulkEmail(selectedCandidates, data);
        alert('Emails sent successfully!');
      } else if (bulkActionType === "INTERVIEW" && activePhaseId) {
        await sendBulkInterview(selectedCandidates, data);
        alert('Interview links sent successfully!');
      } else if (bulkActionType === "SCORE") {
        await handleBulkScore();
      }

      setSelectedCandidates([]);
      setBulkActionModalOpen(false);
    } catch (err) {
      console.error('Bulk action failed:', err);
      alert('Bulk action failed. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusIcon = (status?: CandidatePhaseStatus) => {
    const icons = {
      "PENDING": <Clock className="w-4 h-4 text-gray-400" />,
      "IN_PROGRESS": <Activity className="w-4 h-4 text-blue-500" />,
      "QUALIFIED": <CheckCircle2 className="w-4 h-4 text-green-500" />,
      "REJECTED": <XCircle className="w-4 h-4 text-red-500" />,
    };
    return icons[status || "PENDING"];
  };

  if (phasesLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-3 text-gray-600">Loading phases...</span>
      </div>
    );
  }

  if (phasesError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
        <h3 className="text-lg font-semibold text-gray-900">Failed to load phases</h3>
        <p className="text-sm text-gray-600 mt-2">{phasesError.message}</p>
      </div>
    );
  }

  if (phases.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="w-12 h-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-semibold text-gray-900">No phases configured</h3>
        <p className="text-sm text-gray-600 mt-2">Please configure interview phases first</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto space-y-6">
      {/* Workflow Stepper */}
      <WorkflowStepper steps={workflowSteps} currentStep={currentStepIndex} />

      {/* Phase Tabs */}
      <Card>
        <CardContent className="p-0">
          <div className="flex items-center overflow-x-auto">
            {phases.map((phase: Phase, index: number) => (
              <React.Fragment key={phase.id}>
                <button
                  onClick={() => setActivePhaseId(phase.id)}
                  className={`flex-1 min-w-[200px] px-6 py-4 text-left transition-colors ${
                    activePhaseId === phase.id ? 'bg-blue-50 border-b-2 border-blue-600' : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-gray-900">Phase {phase.order}</span>
                        {phase.isClosed && <Lock className="w-4 h-4 text-gray-400" />}
                      </div>
                      <div className="text-sm font-medium text-gray-700 mt-1">{phase.name}</div>
                      <div className="text-xs text-gray-500 mt-1">{phase.candidateCount || 0} candidates</div>
                    </div>
                    <Badge
                      variant={
                        phase.status === "ACTIVE"
                          ? "default"
                          : phase.status === "COMPLETED"
                          ? "success"
                          : "secondary"
                      }
                    >
                      {phase.status}
                    </Badge>
                  </div>
                </button>
                {index < phases.length - 1 && <ChevronRight className="w-5 h-5 text-gray-300 flex-shrink-0" />}
              </React.Fragment>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Phase Actions Bar */}
      {activePhase && (
        <Card className={activePhase.isClosed ? "bg-gray-50" : ""}>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                  {activePhase.name}
                  {activePhase.isClosed && (
                    <Badge variant="secondary">
                      <Lock className="w-3 h-3 mr-1" />
                      Closed
                    </Badge>
                  )}
                </h2>
                <p className="text-sm text-gray-600 mt-1">{activePhase.description}</p>
              </div>

              {!activePhase.isClosed && (
                <div className="flex items-center gap-3">
                  <Button variant="outline" size="sm" onClick={refetch} disabled={candidatesLoading}>
                    <RefreshCw className={`w-4 h-4 mr-2 ${candidatesLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>

                  {selectedCandidates.length > 0 && (
                    <>
                      <Button variant="success" onClick={handleQualifyCandidates} disabled={actionLoading}>
                        <CheckCheck className="w-4 h-4 mr-2" />
                        Qualify ({selectedCandidates.length})
                      </Button>
                      <Button variant="destructive" onClick={handleRejectCandidates} disabled={actionLoading}>
                        <Ban className="w-4 h-4 mr-2" />
                        Reject ({selectedCandidates.length})
                      </Button>
                    </>
                  )}

                  {candidates.some((c: PhaseCandidate) => c.phaseStatus === "QUALIFIED") && (
                    <Button onClick={handleAdvanceToNextPhase} disabled={actionLoading}>
                      <ArrowRight className="w-4 h-4 mr-2" />
                      Advance Qualified
                    </Button>
                  )}

                  <Button variant="outline" onClick={handleClosePhase} disabled={actionLoading}>
                    <Lock className="w-4 h-4 mr-2" />
                    Close Phase
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Phase-specific action panels */}
      {activePhase && !activePhase.isClosed && selectedCandidates.length > 0 && (
        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
          <CardHeader className="border-blue-100">
            <h3 className="text-lg font-semibold text-gray-900">
              {activePhase.type === "SCREENING" ? "CV Screening Actions" : "Interview Actions"}
            </h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {activePhase.type === "SCREENING" ? (
                <>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => handleBulkAction("SCORE")}
                    disabled={actionLoading}
                  >
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Score Selected CVs
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => handleBulkAction("EMAIL")}
                    disabled={actionLoading}
                  >
                    <Mail className="w-4 h-4 mr-2" />
                    Send Bulk Email
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => handleBulkAction("INTERVIEW")}
                    disabled={actionLoading}
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Send Interview Links
                  </Button>
                  <Button
                    className="w-full justify-start"
                    variant="outline"
                    onClick={() => handleBulkAction("EMAIL")}
                    disabled={actionLoading}
                  >
                    <Mail className="w-4 h-4 mr-2" />
                    Send Bulk Email
                  </Button>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filters - Similar to original implementation */}
      <Card>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search candidates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={activePhase?.isClosed}
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className={showFilters ? "bg-blue-50 border-blue-200 text-blue-700" : ""}
              disabled={activePhase?.isClosed}
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <Button variant="outline" disabled={activePhase?.isClosed}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>

          {showFilters && (
            <div className="border-t border-gray-100 pt-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Score Range</label>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={scoreFilter[0]}
                      onChange={(e) => setScoreFilter([parseInt(e.target.value) || 0, scoreFilter[1]])}
                      className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-500">to</span>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={scoreFilter[1]}
                      onChange={(e) => setScoreFilter([scoreFilter[0], parseInt(e.target.value) || 100])}
                      className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Candidates Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            {candidatesLoading ? (
              <div className="flex items-center justify-center h-64">
                <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
                <span className="ml-3 text-gray-600">Loading candidates...</span>
              </div>
            ) : (
              <table className="w-full">
                <thead className="bg-gray-50 border-b border-gray-200">
                  <tr>
                    <th className="py-4 px-6 text-center">
                      <input
                        type="checkbox"
                        checked={
                          selectedCandidates.length === filteredCandidates.length && filteredCandidates.length > 0
                        }
                        onChange={(e) =>
                          setSelectedCandidates(e.target.checked ? filteredCandidates.map((c: PhaseCandidate) => c.id) : [])
                        }
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        disabled={activePhase?.isClosed}
                      />
                    </th>
                    <th className="text-left py-4 px-6 font-medium text-gray-900">Candidate</th>
                    <th className="text-left py-4 px-6 font-medium text-gray-900">Location</th>
                    <th className="text-left py-4 px-6 font-medium text-gray-900">Score</th>
                    <th className="text-left py-4 px-6 font-medium text-gray-900">Status</th>
                    <th className="text-left py-4 px-6 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {filteredCandidates.length > 0 ? (
                    filteredCandidates.map((candidate: PhaseCandidate) => (
                      <tr key={candidate.id} className="hover:bg-gray-50 transition-colors">
                        <td className="py-4 px-6">
                          <input
                            type="checkbox"
                            checked={selectedCandidates.includes(candidate.id)}
                            onChange={(e) => {
                              setSelectedCandidates((prev) =>
                                e.target.checked
                                  ? [...prev, candidate.id]
                                  : prev.filter((id: number) => id !== candidate.id)
                              );
                            }}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            disabled={activePhase?.isClosed}
                          />
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-3">
                            <img
                              src={candidate.avatar}
                              alt={candidate.name}
                              className="w-10 h-10 rounded-full object-cover"
                            />
                            <div>
                              <div className="font-medium text-gray-900">{candidate.name}</div>
                              <div className="text-sm text-gray-500">{candidate.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-1 text-gray-600">
                            <MapPin className="w-4 h-4" />
                            <span className="text-sm">{candidate.location || candidate.city || 'N/A'}</span>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          {candidate.phaseScore ? (
                            <Badge
                              variant={
                                candidate.phaseScore >= 80
                                  ? "success"
                                  : candidate.phaseScore >= 60
                                  ? "default"
                                  : "warning"
                              }
                            >
                              <Star className="w-3 h-3 mr-1" />
                              {candidate.phaseScore}/100
                            </Badge>
                          ) : (
                            <span className="text-sm text-gray-400">Not scored</span>
                          )}
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(candidate.phaseStatus)}
                            <Badge
                              variant={
                                candidate.phaseStatus === "QUALIFIED"
                                  ? "success"
                                  : candidate.phaseStatus === "REJECTED"
                                  ? "destructive"
                                  : candidate.phaseStatus === "IN_PROGRESS"
                                  ? "default"
                                  : "secondary"
                              }
                            >
                              {candidate.phaseStatus || 'PENDING'}
                            </Badge>
                          </div>
                        </td>
                        <td className="py-4 px-6">
                          <div className="flex items-center gap-2">
                            {!activePhase?.isClosed && (
                              <>
                                {activePhase?.type === "SCREENING" && !candidate.phaseScore && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleScoreCandidate(candidate.id)}
                                    disabled={actionLoading}
                                  >
                                    <TrendingUp className="w-3 h-3 mr-1" />
                                    Score
                                  </Button>
                                )}
                              </>
                            )}
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                setSelectedCandidate(candidate);
                                setDetailModalOpen(true);
                              }}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={7} className="py-12 px-6 text-center">
                        <div className="flex flex-col items-center gap-3">
                          <Users className="w-12 h-12 text-gray-300" />
                          <div>
                            <h3 className="font-medium text-gray-900">No candidates in this phase</h3>
                            <p className="text-sm text-gray-500">
                              {activePhase?.order === 1
                                ? "Upload CVs to begin the screening process"
                                : "Advance qualified candidates from previous phase"}
                            </p>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <Card>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">{candidates.length}</div>
              <div className="text-sm text-gray-600 mt-1">Total Candidates</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {candidates.filter((c: PhaseCandidate) => c.phaseScore).length}
              </div>
              <div className="text-sm text-gray-600 mt-1">Scored</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {candidates.filter((c: PhaseCandidate) => c.phaseStatus === "QUALIFIED").length}
              </div>
              <div className="text-sm text-gray-600 mt-1">Qualified</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">
                {candidates.filter((c: PhaseCandidate) => c.phaseStatus === "REJECTED").length}
              </div>
              <div className="text-sm text-gray-600 mt-1">Rejected</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      {selectedCandidate && activePhase && (
        <PhaseCandidateDetailModal
          open={detailModalOpen}
          onOpenChange={setDetailModalOpen}
          candidate={{
            ...selectedCandidate,
            actions: selectedCandidate.actions || [],
          }}
          phase={{
            id: activePhase.id.toString(),
            name: activePhase.name,
            type: activePhase.type === 'SCREENING' ? 'SCREENING' : 'INTERVIEW',
            order: activePhase.order,
          }}
          onQualify={async () => {
            await updateCandidateStatus(selectedCandidate.id, 'IN_REVIEW');
            setDetailModalOpen(false);
            refetch();
          }}
          onReject={async () => {
            await updateCandidateStatus(selectedCandidate.id, 'REJECTED');
            setDetailModalOpen(false);
            refetch();
          }}
          onScoreCandidate={() => handleScoreCandidate(selectedCandidate.id)}
          onSendEmail={() => handleBulkAction("EMAIL")}
        />
      )}

      <BulkActionModal
        open={bulkActionModalOpen}
        onOpenChange={setBulkActionModalOpen}
        actionType={bulkActionType}
        candidates={candidates
          .filter((c: PhaseCandidate) => selectedCandidates.includes(c.id))
          .map((c: PhaseCandidate) => ({
            id: c.id.toString(),
            name: c.name,
            email: c.email,
            avatar: c.avatar,
          }))}
        onConfirm={handleBulkActionConfirm}
      />
    </div>
  );
}