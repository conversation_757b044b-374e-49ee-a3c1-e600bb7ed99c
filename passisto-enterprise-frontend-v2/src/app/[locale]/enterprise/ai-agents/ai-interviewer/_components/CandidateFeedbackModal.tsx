import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Download,
  Star,
  ThumbsUp,
  ThumbsDown,
  <PERSON><PERSON><PERSON><PERSON>gle,
  User,
  X,
} from 'lucide-react';

interface CategoryScore {
  name: string;
  score: number;
  comment: string;
}

interface Feedback {
  id: string;
  userId: string;
  candidateName: string;
  candidateEmail: string;
  interviewId: string;
  interviewRole: string;
  interviewLevel: string;
  totalScore: number;
  categoryScores: CategoryScore[];
  strengths: string[];
  areasForImprovement: string[];
  finalAssessment: string;
  createdAt: string;
}

interface CandidateFeedbackModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  candidateId: string;
  candidateName: string;
}

// Mock feedback data
const mockFeedback: Feedback = {
  id: "feedback-1",
  userId: "user-1",
  candidateName: "<PERSON>",
  candidateEmail: "<EMAIL>",
  interviewId: "interview-1",
  interviewRole: "Senior Frontend Developer",
  interviewLevel: "senior",
  totalScore: 85,
  categoryScores: [
    {
      name: "Technical Skills",
      score: 90,
      comment: "Excellent understanding of React, TypeScript, and modern web development practices. Demonstrated strong problem-solving abilities."
    },
    {
      name: "Communication",
      score: 85,
      comment: "Clear and articulate communication. Able to explain complex technical concepts effectively."
    },
    {
      name: "Problem Solving",
      score: 88,
      comment: "Strong analytical thinking and systematic approach to problem-solving. Good debugging skills."
    },
    {
      name: "Cultural Fit",
      score: 80,
      comment: "Shows good alignment with company values and demonstrates collaborative mindset."
    },
    {
      name: "Experience Relevance",
      score: 82,
      comment: "Solid background in relevant technologies with good understanding of scalable architecture."
    }
  ],
  strengths: [
    "Strong technical foundation in React and TypeScript",
    "Excellent problem-solving methodology",
    "Clear communication and explanation skills",
    "Good understanding of modern development practices",
    "Experience with cloud platforms and CI/CD"
  ],
  areasForImprovement: [
    "Could benefit from more experience with microservices architecture",
    "Leadership experience could be expanded",
    "Knowledge of advanced testing strategies could be improved"
  ],
  finalAssessment: "Alice is a strong candidate with excellent technical skills and good communication abilities. She demonstrates a solid understanding of modern web development practices and shows great potential for growth. Her problem-solving approach is methodical and her technical foundation is very strong. With some additional experience in system architecture and leadership, she would be an excellent addition to the team.",
  createdAt: new Date().toISOString()
};

export function CandidateFeedbackModal({ 
  open, 
  onOpenChange, 
  candidateId, 
  candidateName 
}: CandidateFeedbackModalProps) {
  const [feedback, setFeedback] = useState<Feedback | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (open) {
      // Simulate API call with mock data
      setLoading(true);
      setTimeout(() => {
        // Customize mock data with actual candidate name
        const customizedFeedback = {
          ...mockFeedback,
          candidateName: candidateName,
        };
        setFeedback(customizedFeedback);
        setLoading(false);
      }, 1000);
    }
  }, [open, candidateId, candidateName]);

  if (!open) return null;

  // Helper function to get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Helper function to get text color based on score
  const getScoreTextColor = (score: number) => {
    if (score >= 80) return "text-green-700";
    if (score >= 60) return "text-yellow-700";
    return "text-red-700";
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={() => onOpenChange(false)}
      />
      
      {/* Modal */}
      <div className="relative bg-white rounded-xl shadow-2xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Candidate Feedback</h2>
            {feedback && (
              <p className="text-sm text-gray-600">
                Generated on {formatDate(feedback.createdAt)}
              </p>
            )}
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => onOpenChange(false)}
              className="p-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-4 text-gray-600">Loading candidate feedback...</p>
            </div>
          ) : !feedback ? (
            <div className="flex flex-col items-center justify-center py-12">
              <AlertTriangle className="h-16 w-16 text-red-500 mb-4" />
              <h3 className="text-xl font-bold mb-2">Feedback Not Found</h3>
              <p className="text-center text-gray-600 mb-6">
                We couldn't find the feedback for this candidate. It may have been
                deleted or doesn't exist.
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Candidate Info Card */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold text-gray-900">Candidate Information</h3>
                  <p className="text-sm text-gray-600">Details about the candidate and interview</p>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-6 md:grid-cols-2">
                    <div>
                      <div className="flex items-center gap-3 mb-4">
                        <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                          <User className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{feedback.candidateName}</h4>
                          <p className="text-sm text-gray-600">{feedback.candidateEmail}</p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-600">Position</p>
                          <p className="font-medium text-gray-900">{feedback.interviewRole}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Level</p>
                          <p className="font-medium text-gray-900 capitalize">{feedback.interviewLevel}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Interview Date</p>
                          <p className="font-medium text-gray-900">{formatDate(feedback.createdAt)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Overall Score</p>
                          <p className="font-medium text-gray-900">{feedback.totalScore}/100</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Overall Score Card */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold text-gray-900">Overall Assessment</h3>
                  <p className="text-sm text-gray-600">Summary of interview performance</p>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col md:flex-row gap-6 items-center md:items-start">
                    <div className="flex flex-col items-center">
                      <div className="relative w-32 h-32 flex items-center justify-center">
                        <svg className="w-full h-full" viewBox="0 0 100 100">
                          <circle
                            className="text-gray-200 stroke-current"
                            strokeWidth="10"
                            cx="50"
                            cy="50"
                            r="40"
                            fill="transparent"
                          ></circle>
                          <circle
                            className={`${getScoreTextColor(feedback.totalScore)} stroke-current`}
                            strokeWidth="10"
                            strokeLinecap="round"
                            cx="50"
                            cy="50"
                            r="40"
                            fill="transparent"
                            strokeDasharray={`${2 * Math.PI * 40}`}
                            strokeDashoffset={`${2 * Math.PI * 40 * (1 - feedback.totalScore / 100)}`}
                            transform="rotate(-90 50 50)"
                          ></circle>
                        </svg>
                        <div className="absolute flex flex-col items-center justify-center">
                          <span className="text-3xl font-bold text-gray-900">{feedback.totalScore}</span>
                          <span className="text-xs text-gray-600">out of 100</span>
                        </div>
                      </div>
                      <div className="mt-2 flex items-center gap-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-4 w-4 ${
                              star <= Math.floor(feedback.totalScore / 20)
                                ? "fill-yellow-400 text-yellow-400"
                                : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold mb-2 text-gray-900">Final Assessment</h4>
                      <p className="text-gray-700 leading-relaxed">{feedback.finalAssessment}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Strengths and Areas for Improvement */}
              <div className="grid gap-6 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <h4 className="text-lg font-semibold flex items-center gap-2 text-gray-900">
                      <ThumbsUp className="h-5 w-5 text-green-500" />
                      Strengths
                    </h4>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {feedback.strengths.map((strength, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <div className="mt-1.5 min-w-2 h-2 rounded-full bg-green-500"></div>
                          <span className="text-gray-700">{strength}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <h4 className="text-lg font-semibold flex items-center gap-2 text-gray-900">
                      <ThumbsDown className="h-5 w-5 text-yellow-500" />
                      Areas for Improvement
                    </h4>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {feedback.areasForImprovement.map((area, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <div className="mt-1.5 min-w-2 h-2 rounded-full bg-yellow-500"></div>
                          <span className="text-gray-700">{area}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>

              {/* Detailed Category Scores */}
              <Card>
                <CardHeader>
                  <h4 className="text-lg font-semibold text-gray-900">Detailed Category Scores</h4>
                  <p className="text-sm text-gray-600">Breakdown of performance by category</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {feedback.categoryScores.map((category, index) => (
                      <div key={index} className="border-b border-gray-100 pb-6 last:border-b-0 last:pb-0">
                        <div className="flex justify-between items-center mb-3">
                          <div className="flex items-center gap-3">
                            <h5 className="font-medium text-gray-900">{category.name}</h5>
                            <Badge variant={category.score >= 80 ? "success" : category.score >= 60 ? "default" : "secondary"}>
                              {category.score}/100
                            </Badge>
                          </div>
                          <span className={`font-semibold ${getScoreTextColor(category.score)}`}>
                            {category.score >= 80 ? "Excellent" : category.score >= 60 ? "Good" : "Needs Improvement"}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-3">
                          <div
                            className={`h-2.5 rounded-full transition-all duration-500 ${getScoreColor(category.score)}`}
                            style={{ width: `${category.score}%` }}
                          ></div>
                        </div>
                        <p className="text-sm text-gray-700 leading-relaxed">{category.comment}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}