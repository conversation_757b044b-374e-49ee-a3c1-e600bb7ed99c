"use client"

import React from 'react'
import {
  X,
  Mail,
  MapPin,
  Briefcase,
  CheckCircle2,
  XCircle,
  Clock,
  Activity,
  Send,
  TrendingUp,
  Calendar,
  MessageSquare
} from 'lucide-react'

import { PhaseCandidate } from '../../../../../../hooks/usePhaseCandidates';

type CandidatePhaseStatus = "PENDING" | "QUALIFIED" | "REJECTED" | "IN_PROGRESS"
type PhaseType = "SCREENING" | "INTERVIEW"

type CandidateAction = {
  id: string
  type: "EMAIL_SENT" | "SCORE_ASSIGNED" | "INTERVIEW_SENT" | "FEEDBACK_GENERATED"
  timestamp: string
  details?: string
}

type Candidate = PhaseCandidate & {
  experience?: number
}

type Phase = {
  id: string
  name: string
  type: PhaseType
  order: number
}

const Badge = ({ children, variant = "default", className = "" }: { 
  children: React.ReactNode
  variant?: "default" | "secondary" | "destructive" | "outline" | "success" | "warning"
  className?: string 
}) => {
  const variants = {
    default: "bg-blue-100 text-blue-800 border border-blue-200",
    secondary: "bg-gray-100 text-gray-800 border border-gray-200",
    destructive: "bg-red-100 text-red-800 border border-red-200",
    outline: "bg-transparent border border-gray-300 text-gray-700",
    success: "bg-green-100 text-green-800 border border-green-200",
    warning: "bg-yellow-100 text-yellow-800 border border-yellow-200"
  }
  
  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${variants[variant]} ${className}`}>
      {children}
    </span>
  )
}

const Button = ({ 
  children, 
  variant = "default", 
  size = "default",
  disabled = false,
  onClick,
  className = "",
  ...props 
}: {
  children: React.ReactNode
  variant?: "default" | "outline" | "ghost" | "destructive" | "success"
  size?: "default" | "sm" | "lg"
  disabled?: boolean
  onClick?: () => void
  className?: string
  [key: string]: any
}) => {
  const variants = {
    default: "bg-blue-600 text-white hover:bg-blue-700 border border-blue-600 shadow-sm",
    outline: "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300 shadow-sm",
    ghost: "bg-transparent text-gray-700 hover:bg-gray-100 border-0",
    destructive: "bg-red-600 text-white hover:bg-red-700 border border-red-600 shadow-sm",
    success: "bg-green-600 text-white hover:bg-green-700 border border-green-600 shadow-sm"
  }
  
  const sizes = {
    default: "px-4 py-2 text-sm",
    sm: "px-3 py-1.5 text-xs",
    lg: "px-6 py-3 text-base"
  }
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${variants[variant]} ${sizes[size]} ${className}`}
      {...props}
    >
      {children}
    </button>
  )
}

type PhaseCandidateDetailModalProps = {
  open: boolean
  onOpenChange: (open: boolean) => void
  candidate: Candidate
  phase: Phase
  onQualify?: () => void
  onReject?: () => void
  onScoreCandidate?: () => void
  onSendInterview?: () => void
  onSendEmail?: () => void
}

export function PhaseCandidateDetailModal({
  open,
  onOpenChange,
  candidate,
  phase,
  onQualify,
  onReject,
  onScoreCandidate,
  onSendInterview,
  onSendEmail
}: PhaseCandidateDetailModalProps) {
  if (!open) return null

  const getStatusIcon = (status: CandidatePhaseStatus) => {
    const icons = {
      "PENDING": <Clock className="w-5 h-5 text-gray-400" />,
      "IN_PROGRESS": <Activity className="w-5 h-5 text-blue-500" />,
      "QUALIFIED": <CheckCircle2 className="w-5 h-5 text-green-500" />,
      "REJECTED": <XCircle className="w-5 h-5 text-red-500" />
    }
    return icons[status]
  }

  const getActionIcon = (type: CandidateAction["type"]) => {
    const icons = {
      "EMAIL_SENT": <Mail className="w-4 h-4 text-blue-500" />,
      "SCORE_ASSIGNED": <TrendingUp className="w-4 h-4 text-green-500" />,
      "INTERVIEW_SENT": <Send className="w-4 h-4 text-purple-500" />,
      "FEEDBACK_GENERATED": <MessageSquare className="w-4 h-4 text-orange-500" />
    }
    return icons[type]
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={() => onOpenChange(false)} />
        
        <div className="relative bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
          {/* Header */}
          <div className="px-6 py-5 border-b border-gray-200">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-4">
                <img 
                  src={candidate.avatar} 
                  alt={candidate.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{candidate.name}</h2>
                  <div className="flex items-center gap-3 mt-2">
                    <span className="text-sm text-gray-600 flex items-center gap-1">
                      <Mail className="w-4 h-4" />
                      {candidate.email}
                    </span>
                    <span className="text-sm text-gray-600 flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {candidate.location}
                    </span>
                  </div>
                </div>
              </div>
              <button
                onClick={() => onOpenChange(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto px-6 py-6 space-y-6">
            {/* Phase Context */}
            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-blue-900">Current Phase</div>
                  <div className="text-lg font-semibold text-blue-700 mt-1">
                    Phase {phase.order}: {phase.name}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(candidate.phaseStatus || "PENDING")}
                  <Badge
                    variant={
                      candidate.phaseStatus === "QUALIFIED" ? "success" :
                      candidate.phaseStatus === "REJECTED" ? "destructive" :
                      candidate.phaseStatus === "IN_PROGRESS" ? "default" : "secondary"
                    }
                  >
                    {candidate.phaseStatus || "PENDING"}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Basic Info */}
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-3">Professional Details</h3>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-gray-600">
                    <Briefcase className="w-4 h-4" />
                    <span className="text-sm">{candidate.experience} years experience</span>
                  </div>
                  <div className="flex flex-wrap gap-1 mt-2">
                    {candidate.skills?.map((skill: string) => (
                      <Badge key={skill} variant="outline">{skill}</Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Score Section */}
              {candidate.phaseScore !== undefined && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    {phase.type === "SCREENING" ? "CV Score" : "Interview Score"}
                  </h3>
                  <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4 border border-green-200">
                    <div className="flex items-center gap-3">
                      <div className="text-4xl font-bold text-green-700">{candidate.phaseScore}</div>
                      <div className="text-sm text-green-600">/100</div>
                    </div>
                    <div className="mt-2">
                      <Badge 
                        variant={candidate.phaseScore >= 80 ? "success" : 
                               candidate.phaseScore >= 60 ? "default" : "warning"}
                      >
                        {candidate.phaseScore >= 80 ? "Excellent" : 
                         candidate.phaseScore >= 60 ? "Good" : "Fair"}
                      </Badge>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* AI Feedback */}
            {candidate.feedback && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                  <MessageSquare className="w-4 h-4" />
                  AI Feedback
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200 space-y-4">
                  {typeof candidate.feedback === 'object' && candidate.feedback !== null ? (
                    <>
                      {/* Recommendation Summary */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-xs font-medium text-gray-500 uppercase mb-1">Recommendation</div>
                          <Badge
                            variant={
                              (candidate.feedback as any).recommendation === "Recommended" ? "success" :
                              (candidate.feedback as any).recommendation === "Not Recommended" ? "destructive" :
                              "warning"
                            }
                            className="text-sm px-3 py-1"
                          >
                            {(candidate.feedback as any).recommendation || 'N/A'}
                          </Badge>
                        </div>
                        {(candidate.feedback as any).confidence_level && (
                          <div className="text-right">
                            <div className="text-xs font-medium text-gray-500 uppercase mb-1">Confidence</div>
                            <Badge variant="outline" className="text-sm">
                              {(candidate.feedback as any).confidence_level}
                            </Badge>
                          </div>
                        )}
                      </div>

                      {/* Detailed Scores */}
                      {(candidate.feedback as any).scores && (
                        <div>
                          <div className="text-xs font-medium text-gray-500 uppercase mb-3">Detailed Scores</div>
                          <div className="space-y-3">
                            {Object.entries((candidate.feedback as any).scores).map(([key, value]: [string, any]) => (
                              <div key={key} className="bg-white rounded-lg p-3 border border-gray-200">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-sm font-medium text-gray-700 capitalize">
                                    {key.replace(/_/g, ' ')}
                                  </span>
                                  <Badge
                                    variant={
                                      value.score >= 80 ? "success" :
                                      value.score >= 60 ? "default" :
                                      value.score >= 40 ? "warning" :
                                      "destructive"
                                    }
                                  >
                                    {value.score}/100
                                  </Badge>
                                </div>
                                {value.reasoning && (
                                  <p className="text-xs text-gray-600 leading-relaxed">
                                    {value.reasoning}
                                  </p>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Flags if any */}
                      {(candidate.feedback as any).flags && (candidate.feedback as any).flags.length > 0 && (
                        <div>
                          <div className="text-xs font-medium text-gray-500 uppercase mb-2">Flags</div>
                          <div className="flex flex-wrap gap-2">
                            {(candidate.feedback as any).flags.map((flag: string, index: number) => (
                              <Badge key={index} variant="warning">
                                {flag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {candidate.feedback}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Action History */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
                <Activity className="w-4 h-4" />
                Action History
              </h3>
              {candidate.actions && candidate.actions.length > 0 ? (
                <div className="space-y-3">
                  {candidate.actions.map((action: CandidateAction) => (
                    <div key={action.id} className="flex items-start gap-3 bg-white rounded-lg p-3 border border-gray-200">
                      <div className="mt-0.5">
                        {getActionIcon(action.type)}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-sm text-gray-900">
                          {action.type.replace(/_/g, ' ')}
                        </div>
                        {action.details && (
                          <div className="text-xs text-gray-600 mt-1">{action.details}</div>
                        )}
                        <div className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {formatTimestamp(action.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-6 border border-gray-200 text-center">
                  <Clock className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No actions taken yet</p>
                </div>
              )}
            </div>
          </div>

          {/* Footer Actions */}
          <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={onSendEmail}>
                  <Mail className="w-4 h-4 mr-2" />
                  Send Email
                </Button>
                
                {phase.type === "SCREENING" && !candidate.phaseScore && (
                  <Button variant="outline" onClick={onScoreCandidate}>
                    <TrendingUp className="w-4 h-4 mr-2" />
                    Score CV
                  </Button>
                )}
                
                {phase.type === "INTERVIEW" && (
                  <Button variant="outline" onClick={onSendInterview}>
                    <Send className="w-4 h-4 mr-2" />
                    Send Interview
                  </Button>
                )}
              </div>

              <div className="flex items-center gap-2">
                {candidate.phaseStatus !== "REJECTED" && (
                  <Button variant="destructive" onClick={onReject}>
                    <XCircle className="w-4 h-4 mr-2" />
                    Reject
                  </Button>
                )}
                {candidate.phaseStatus !== "QUALIFIED" && (
                  <Button variant="success" onClick={onQualify}>
                    <CheckCircle2 className="w-4 h-4 mr-2" />
                    Qualify
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
