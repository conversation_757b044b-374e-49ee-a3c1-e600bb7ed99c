import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import { InterviewTemplateResponseDTO, CreateStructuralTemplateDTO, CreateEmailTemplateDTO, UpdateInterviewTemplateDTO, QuestionGenerationParamsDTO } from '../../types/interview-template';

// Define the initial state
const initialState = {
  templates: [] as InterviewTemplateResponseDTO[],
  activeTemplates: [] as InterviewTemplateResponseDTO[],
  templateDetails: null as InterviewTemplateResponseDTO | null,
  status: 'idle',
  error: null as string | null,
};

// Async thunks for API calls
export const fetchTemplates = createAsyncThunk<
  InterviewTemplateResponseDTO[],
  { projectId: number; token: string }
>(
  'interviewTemplates/fetchTemplates',
  async ({ projectId, token }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      console.log("fetch:", response);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchActiveTemplates = createAsyncThunk<
  InterviewTemplateResponseDTO[],
  { projectId: number; token: string }
>(
  'interviewTemplates/fetchActiveTemplates',
  async ({ projectId, token }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}/active`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const fetchTemplateById = createAsyncThunk<
  InterviewTemplateResponseDTO,
  { projectId: number; id: number; token: string }
>(
  'interviewTemplates/fetchTemplateById',
  async ({ projectId, id, token }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const createStructuralTemplate = createAsyncThunk<
  InterviewTemplateResponseDTO,
  { projectId: number; data: CreateStructuralTemplateDTO; token: string }
>(
  'interviewTemplates/createStructuralTemplate',
  async ({ projectId, data, token }, { rejectWithValue }) => {
    console.log("data", data);
    try {
      const response = await axios.post(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}/structural`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const createEmailTemplate = createAsyncThunk<
  InterviewTemplateResponseDTO,
  { projectId: number; data: CreateEmailTemplateDTO; token: string }
>(
  'interviewTemplates/createEmailTemplate',
  async ({ projectId, data, token }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}/email`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const updateTemplate = createAsyncThunk<
  InterviewTemplateResponseDTO,
  { projectId: number; id: number; data: UpdateInterviewTemplateDTO; templatetype: string; token: string }
>(
  'interviewTemplates/updateTemplate',
  async ({ projectId, id, data, templatetype, token }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}/${templatetype}/${id}`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deleteTemplate = createAsyncThunk<
  number,
  { projectId: number; id: number; token: string }
>(
  'interviewTemplates/deleteTemplate',
  async ({ projectId, id, token }, { rejectWithValue }) => {
    try {
      await axios.delete(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return id;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const activateTemplate = createAsyncThunk<
  InterviewTemplateResponseDTO,
  { projectId: number; id: number; token: string }
>(
  'interviewTemplates/activateTemplate',
  async ({ projectId, id, token }, { rejectWithValue }) => {
    try {
      const response = await axios.patch(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}/${id}/activate`, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const deactivateTemplate = createAsyncThunk<
  InterviewTemplateResponseDTO,
  { projectId: number; id: number; token: string }
>(
  'interviewTemplates/deactivateTemplate',
  async ({ projectId, id, token }, { rejectWithValue }) => {
    try {
      const response = await axios.patch(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}/${id}/deactivate`, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

export const generateQuestions = createAsyncThunk<
  any,
  { projectId: number; params: QuestionGenerationParamsDTO; token: string }
>(
  'interviewTemplates/generateQuestions',
  async ({ projectId, params, token }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`http://localhost:4000/api-hiring/v1/interview-templates/projects/${projectId}/generate-questions`, params, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      console.log(response.data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data || error.message);
    }
  }
);

// Slice
const interviewTemplateSlice = createSlice({
  name: 'interviewTemplates',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchTemplates.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchTemplates.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.templates = action.payload;
      })
      .addCase(fetchTemplates.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      .addCase(fetchActiveTemplates.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchActiveTemplates.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.activeTemplates = action.payload;
      })
      .addCase(fetchActiveTemplates.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      .addCase(fetchTemplateById.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(fetchTemplateById.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.templateDetails = action.payload;
      })
      .addCase(fetchTemplateById.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      .addCase(createStructuralTemplate.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(createStructuralTemplate.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.templates.push(action.payload);
      })
      .addCase(createStructuralTemplate.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      .addCase(createEmailTemplate.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(createEmailTemplate.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.templates.push(action.payload);
      })
      .addCase(createEmailTemplate.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      .addCase(updateTemplate.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(updateTemplate.fulfilled, (state, action) => {
        state.status = 'succeeded';
        const index = state.templates.findIndex((template: InterviewTemplateResponseDTO) => template.id === action.payload.id);
        if (index !== -1) {
          state.templates[index] = action.payload;
        }
      })
      .addCase(updateTemplate.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      .addCase(deleteTemplate.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(deleteTemplate.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.templates = state.templates.filter((template: InterviewTemplateResponseDTO) => template.id !== action.payload);
      })
      .addCase(deleteTemplate.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      .addCase(activateTemplate.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(activateTemplate.fulfilled, (state, action) => {
        state.status = 'succeeded';
        const index = state.templates.findIndex((template) => template.id === action.payload.id);
        if (index !== -1) {
          state.templates[index] = action.payload;
        }
      })
      .addCase(activateTemplate.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      .addCase(deactivateTemplate.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(deactivateTemplate.fulfilled, (state, action) => {
        state.status = 'succeeded';
        const index = state.templates.findIndex((template) => template.id === action.payload.id);
        if (index !== -1) {
          state.templates[index] = action.payload;
        }
      })
      .addCase(deactivateTemplate.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      })
      .addCase(generateQuestions.pending, (state) => {
        state.status = 'loading';
      })
      .addCase(generateQuestions.fulfilled, (state) => {
        state.status = 'succeeded';
      })
      .addCase(generateQuestions.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      });
  },
});

export default interviewTemplateSlice.reducer;