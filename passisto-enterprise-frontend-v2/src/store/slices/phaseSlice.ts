

import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import axiosHiringInstance from "@/config/axios-hiring";
import {
  PHASE_LIST,
  PHASE_CREATE,
  PHASE_PATCH,
  PHASE_DELETE,
  PHASE_SUGGEST,
} from "@/utils/hiring.routes";
import { InterviewPhase } from "@/lib/utils";

// ================== Thunks ==================

// Fetch all phases
export const fetchPhases = createAsyncThunk<
  InterviewPhase[],
  { token: string; projectId: string },
  { rejectValue: string }
>("phases/fetchPhases", async ({ token, projectId }, { rejectWithValue }) => {
  try {
    const res = await axiosHiringInstance.get(PHASE_LIST(Number(projectId)), {
      headers: { Authorization: `Bearer ${token}` },
    });

    return res.data.map((phase: any) => ({
      id: String(phase.id),
      name: phase.name,
      description: phase.description,
      type: phase.phaseType,
      order: phase.phaseOrder,
      isRequired: phase.isRequired,
      estimatedDuration: phase.estimatedDuration,
      startDate: phase.startDate ? phase.startDate.slice(0, 16) : "",
      endDate: phase.endDate ? phase.endDate.slice(0, 16) : "",
      allowSkip: phase.allowSkip,
      requiresApproval: phase.requiresApproval,
      autoAdvance: phase.autoAdvance,
      passingScore: phase.passingScore,
      maxAttempts: phase.maxAttempts,
      status: phase.status,
      createdAt: phase.createdAt,
      updatedAt: phase.updatedAt,
    }));
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || "Failed to fetch phases");
  }
});

// Create phase
export const createPhase = createAsyncThunk<
  InterviewPhase,
  { token: string; projectId: string; phase: any },
  { rejectValue: string }
>("phases/createPhase", async ({ token, projectId, phase }, { rejectWithValue }) => {
  try {
    const res = await axiosHiringInstance.post(PHASE_CREATE(Number(projectId)), phase, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return res.data;
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || "Failed to create phase");
  }
});

// Update phase
export const updatePhase = createAsyncThunk<
  InterviewPhase,
  { token: string; phaseId: string; phase: any },
  { rejectValue: string }
>("phases/updatePhase", async ({ token, phaseId, phase }, { rejectWithValue }) => {
  try {
    const res = await axiosHiringInstance.patch(PHASE_PATCH(Number(phaseId)), phase, {
      headers: { Authorization: `Bearer ${token}` },
    });
    return res.data;
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || "Failed to update phase");
  }
});

// Delete phase
export const deletePhase = createAsyncThunk<
  string,
  { token: string; phaseId: string },
  { rejectValue: string }
>("phases/deletePhase", async ({ token, phaseId }, { rejectWithValue }) => {
  try {
    await axiosHiringInstance.delete(PHASE_DELETE(Number(phaseId)), {
      headers: { Authorization: `Bearer ${token}` },
    });
    return phaseId;
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || "Failed to delete phase");
  }
});

// Suggest phase dates
// ✅ Correction légère sur suggestPhaseDates
export const suggestPhaseDates = createAsyncThunk<
  { phaseId: string; startDate: string; endDate: string },
  { token: string; projectId: string; phaseId: string },
  { rejectValue: string }
>("phases/suggestPhaseDates", async ({ token, projectId, phaseId }, { rejectWithValue }) => {
  try {
    const res = await axiosHiringInstance.get(PHASE_SUGGEST(Number(projectId)), {
      headers: { Authorization: `Bearer ${token}` },
    });

    // ✅ s'assurer que la réponse contient bien les champs nécessaires
    const { startDate, endDate } = res.data;
    return { phaseId, startDate, endDate };
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || "Failed to suggest dates");
  }
});


// ================== Slice ==================
const phasesSlice = createSlice({
  name: "phases",
  initialState: {
    phases: [] as InterviewPhase[],
    loading: false,
    error: null as string | null,
  },
  reducers: {
    setPhases(state, action: PayloadAction<InterviewPhase[]>) {
      state.phases = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPhases.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchPhases.fulfilled, (state, action) => {
        state.loading = false;
        state.phases = action.payload;
      })
      .addCase(fetchPhases.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || null;
      })
      .addCase(createPhase.fulfilled, (state, action) => {
        state.phases.push(action.payload);
      })
      .addCase(updatePhase.fulfilled, (state, action) => {
        const index = state.phases.findIndex((p) => p.id === action.payload.id);
        if (index !== -1) state.phases[index] = action.payload;
      })
      .addCase(deletePhase.fulfilled, (state, action) => {
        state.phases = state.phases.filter((p) => p.id !== action.payload);
      })
      .addCase(suggestPhaseDates.fulfilled, (state, action) => {
        const phase = state.phases.find((p) => p.id === action.payload.phaseId);
        if (phase) {
          phase.startDate = action.payload.startDate;
          phase.endDate = action.payload.endDate;
        }
      });
  },
});

export const { setPhases } = phasesSlice.actions;
export default phasesSlice.reducer;
