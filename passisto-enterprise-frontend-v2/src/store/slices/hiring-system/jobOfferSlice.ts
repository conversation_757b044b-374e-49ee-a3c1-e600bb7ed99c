import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from '@/store/index';
import axiosInstance from "@/config/axios-hiring";
import { JOB_OFFER_ROUTES } from "@/utils/hiring.routes";

// Updated interfaces to match backend DTOs exactly
export interface JobOffer {
  id: number;
  title: string;
  description?: string | null;
  location?: string | null;
  workArrangement?: "ONSITE" | "REMOTE" | "HYBRID" | null;
  salaryType?: "RANGE" | "FIXED_AMOUNT" | null;
  minSalary?: number | null;
  maxSalary?: number | null;
  currency?: string | null;
  salaryPeriod?: "HOURLY" | "DAILY" | "MONTHLY" | null;
  salary?: string | null;
  bonusStructure?: string | null;
  additionalBenefits?: string | null;
  educationLevel?: string | null;
  yearsOfExperience?: number | null;
  requiredCertifications?: string | null;
  languages?: string | null;
  expectedStartDate?: Date | null; // Backend returns Date, not string
  contactEmail?: string | null;
  applicationInstructions?: string | null;
  employmentType: "FULL_TIME" | "PART_TIME" | "CONTRACT" | "TEMPORARY" | "INTERNSHIP" | "FREELANCE"; // Required field
  department?: string | null;
  experienceLevel: "ENTRY" | "JUNIOR" | "MID" | "SENIOR" | "EXECUTIVE"; // Required field
  requiredSkills?: string | null;
  applicationDeadline?: Date | null; // Backend returns Date, not string
  benefits?: string | null;
  projectId: number;
  isActive: boolean;
  createdAt: Date; // Backend returns Date, not string
  updatedAt: Date; // Backend returns Date, not string
  formId?: string;
}

// Create DTO interface matching backend exactly
export interface CreateJobOfferDTO {
  title: string;
  description?: string;
  location?: string;
  workArrangement?: "ONSITE" | "REMOTE" | "HYBRID";
  salaryType?: "RANGE" | "FIXED_AMOUNT";
  minSalary?: number;
  maxSalary?: number;
  currency?: string;
  salaryPeriod?: "HOURLY" | "DAILY" | "MONTHLY";
  salary?: string;
  bonusStructure?: string;
  additionalBenefits?: string;
  educationLevel?: string;
  yearsOfExperience?: number;
  requiredCertifications?: string;
  languages?: string;
  expectedStartDate?: string; // ISO date string for input
  contactEmail?: string;
  applicationInstructions?: string;
  department?: string;
  experienceLevel?: "ENTRY" | "JUNIOR" | "MID" | "SENIOR" | "EXECUTIVE";
  requiredSkills?: string;
  applicationDeadline?: string; // ISO date string for input
  benefits?: string;
  projectId: number;
  isActive?: boolean;
  formId?: string;
}

// Update DTO interface matching backend exactly
export interface UpdateJobOfferDTO {
  title?: string;
  description?: string;
  location?: string;
  workArrangement?: "ONSITE" | "REMOTE" | "HYBRID";
  salaryType?: "RANGE" | "FIXED_AMOUNT";
  minSalary?: number;
  maxSalary?: number;
  currency?: string;
  salaryPeriod?: "HOURLY" | "DAILY" | "MONTHLY";
  salary?: string;
  bonusStructure?: string;
  additionalBenefits?: string;
  educationLevel?: string;
  yearsOfExperience?: number;
  requiredCertifications?: string;
  languages?: string;
  expectedStartDate?: string; // ISO date string for input
  contactEmail?: string;
  applicationInstructions?: string;
  department?: string;
  experienceLevel?: "ENTRY" | "JUNIOR" | "MID" | "SENIOR" | "EXECUTIVE";
  requiredSkills?: string;
  applicationDeadline?: string; // ISO date string for input
  benefits?: string;
  isActive?: boolean;
  formId?: string;
}


export interface JobOfferState {
  currentJobOffer: JobOffer | null;
  status: "idle" | "loading" | "succeeded" | "failed";
  error: string | null;
}

const initialState: JobOfferState = {
  currentJobOffer: null,
  status: "idle",
  error: null,
};

// Updated to include token in headers for Clerk authentication

export const fetchJobOfferByProject = createAsyncThunk<
  JobOffer,
  { token: string; projectId: number },
  { rejectValue: string }
>(
  "jobOffers/fetchJobOfferByProject",
  async ({ token, projectId }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get<JobOffer>(
        JOB_OFFER_ROUTES.BY_PROJECT(projectId),
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return rejectWithValue("Job offer not found for this project");
      }
      return rejectWithValue(
        error.response?.data?.message ||
          "Failed to fetch job offer for the project"
      );
    }
  }
);

export const createJobOffer = createAsyncThunk<
  JobOffer,
  { token: string; jobOfferData: CreateJobOfferDTO },
  { rejectValue: string }
>(
  "jobOffers/createJobOffer",
  async ({ token, jobOfferData }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post<JobOffer>(
        JOB_OFFER_ROUTES.CREATE,
        jobOfferData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 400) {
        return rejectWithValue(
          error.response.data?.message || "Invalid project data"
        );
      }
      if (error.response?.status === 409) {
        return rejectWithValue(
          error.response.data?.message || "Project already has a job offer"
        );
      }
      return rejectWithValue(
        error.response?.data?.message || "Failed to create job offer"
      );
    }
  }
);

export const updateJobOffer = createAsyncThunk<
  JobOffer,
  { token: string; id: number; jobOfferData: UpdateJobOfferDTO },
  { rejectValue: string }
>(
  "jobOffers/updateJobOffer",
  async ({ token, id, jobOfferData }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.put<JobOffer>(
        JOB_OFFER_ROUTES.UPDATE(id),
        jobOfferData,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to update job offer"
      );
    }
  }
);

export const deleteJobOffer = createAsyncThunk<
  number,
  { token: string; id: number },
  { rejectValue: string }
>(
  "jobOffers/deleteJobOffer",
  async ({ token, id }, { rejectWithValue }) => {
    try {
      await axiosInstance.delete(JOB_OFFER_ROUTES.DELETE(id), {
        headers: { Authorization: `Bearer ${token}` },
      });
      return id;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to delete job offer"
      );
    }
  }
);

export const activateJobOffer = createAsyncThunk<
  JobOffer,
  { token: string; id: number },
  { rejectValue: string }
>(
  "jobOffers/activateJobOffer",
  async ({ token, id }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.patch<JobOffer>(
        JOB_OFFER_ROUTES.ACTIVATE(id),
        {},
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to activate job offer"
      );
    }
  }
);

export const deactivateJobOffer = createAsyncThunk<
  JobOffer,
  { token: string; id: number },
  { rejectValue: string }
>(
  "jobOffers/deactivateJobOffer",
  async ({ token, id }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.patch<JobOffer>(
        JOB_OFFER_ROUTES.DEACTIVATE(id),
        {},
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      return response.data;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || "Failed to deactivate job offer"
      );
    }
  }
);

// Slice
const jobOfferSlice = createSlice({
  name: "jobOffers",
  initialState,
  reducers: {
    clearCurrentJobOffer: (state) => {
      state.currentJobOffer = null;
    },
    setCurrentJobOfferField: (
      state,
      action: PayloadAction<{ field: keyof JobOffer; value: any }>
    ) => {
      if (state.currentJobOffer) {
        (state.currentJobOffer[action.payload.field] as any) = action.payload.value;
      } else {
        state.currentJobOffer = {
          ...({} as JobOffer), // empty object with typing
          [action.payload.field]: action.payload.value,
        };
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Job Offer By Project
      .addCase(fetchJobOfferByProject.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchJobOfferByProject.fulfilled, (state, action: PayloadAction<JobOffer>) => {
        state.status = "succeeded";
        state.currentJobOffer = action.payload;
      })
      .addCase(fetchJobOfferByProject.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload || "Failed to fetch job offer";
        state.currentJobOffer = null;
      })
      
      // Create Job Offer
      .addCase(createJobOffer.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(createJobOffer.fulfilled, (state, action: PayloadAction<JobOffer>) => {
        state.status = "succeeded";
        state.currentJobOffer = action.payload;
      })
      .addCase(createJobOffer.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload || "Failed to create job offer";
      })
      
      // Update Job Offer
      .addCase(updateJobOffer.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(updateJobOffer.fulfilled, (state, action: PayloadAction<JobOffer>) => {
        state.status = "succeeded";
        const updated = action.payload;
        // Keep currentJobOffer in sync
        if (state.currentJobOffer?.id === updated.id) {
          state.currentJobOffer = updated;
        }
      })
      .addCase(updateJobOffer.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload || "Failed to update job offer";
      })
      
      // Delete Job Offer
      .addCase(deleteJobOffer.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(deleteJobOffer.fulfilled, (state, action: PayloadAction<number>) => {
        state.status = "succeeded";        
        // Clear currentJobOffer if it was the deleted one
        if (state.currentJobOffer?.id === action.payload) {
          state.currentJobOffer = null;
        }
      })
      .addCase(deleteJobOffer.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload || "Failed to delete job offer";
      })
      
      // Activate Job Offer
      .addCase(activateJobOffer.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(activateJobOffer.fulfilled, (state, action: PayloadAction<JobOffer>) => {
        state.status = "succeeded";
        const updated = action.payload;
        
        // Keep currentJobOffer in sync
        if (state.currentJobOffer?.id === updated.id) {
          state.currentJobOffer = updated;
        }
      })
      .addCase(activateJobOffer.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload || "Failed to activate job offer";
      })
      
      // Deactivate Job Offer
      .addCase(deactivateJobOffer.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(deactivateJobOffer.fulfilled, (state, action: PayloadAction<JobOffer>) => {
        state.status = "succeeded";
        const updated = action.payload;
        // Keep currentJobOffer in sync
        if (state.currentJobOffer?.id === updated.id) {
          state.currentJobOffer = updated;
        }
      })
      .addCase(deactivateJobOffer.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload || "Failed to deactivate job offer";
      });
  },
});

export const selectCurrentProject = (state: { jobOffer: JobOfferState }) => state.jobOffer.currentJobOffer;

export const { clearCurrentJobOffer, clearError, setCurrentJobOfferField } = jobOfferSlice.actions;
export default jobOfferSlice.reducer;