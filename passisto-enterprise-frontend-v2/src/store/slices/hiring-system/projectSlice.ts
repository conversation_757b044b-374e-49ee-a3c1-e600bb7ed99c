import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axiosInstance from '@/config/axios-hiring';
import { PROJECT_ROUTES } from '@/utils/hiring.routes';

export interface Project {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  endDate: string;
  numCandidates: number;
  progress: number;
  status: "IN_PROGRESS" | "COMPLETE" | "DRAFT" | "ARCHIVED";
  startDate?: string;
  updatedAt?: string;
}


export interface UpdateProjectDTO {
    name?: string;
    description?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
  }

interface ProjectState {
  projects: Project[];
  currentProject: Project | null;
  isLoading: boolean;
  error: string | null;
  filters: {
    status: "all" | "active" | "complete" | "draft" | "archived" | "in progress";
    search: string;
  };
}

const initialState: ProjectState = {
  projects: [],
  currentProject: null,
  isLoading: false,
  error: null,
  filters: {
    status: "active",
    search: "",
  },
};

// Async thunks
export const fetchProjects = createAsyncThunk(
  'projects/fetchProjects',
  async (token: string, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(PROJECT_ROUTES.LIST, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch projects');
    }
  }
);

export const createProject = createAsyncThunk(
  'projects/createProject',
  async ({ token, projectData }: { token: string; projectData: Partial<Project> }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(PROJECT_ROUTES.CREATE, projectData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create project');
    }
  }
);

export const updateProject = createAsyncThunk(
  'projects/updateProject',
  async ({ token, projectId, projectData }: { token: string; projectId: string; projectData: UpdateProjectDTO }, { rejectWithValue }) => {
    try {
      const response = await axiosInstance.put(PROJECT_ROUTES.UPDATE(projectId), projectData, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update project');
    }
  }
);

export const deleteProject = createAsyncThunk(
  'projects/deleteProject',
  async ({ token, projectId }: { token: string; projectId: string }, { rejectWithValue }) => {
    try {
      await axiosInstance.delete(PROJECT_ROUTES.DELETE(projectId), {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return projectId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete project');
    }
  }
);

const projectSlice = createSlice({
  name: 'projects',
  initialState,
  reducers: {
    setFilter: (state, action: PayloadAction<{ status?: ProjectState['filters']['status']; search?: string }>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setCurrentProject: (state, action: PayloadAction<Project | null>) => {
      state.currentProject = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Projects
      .addCase(fetchProjects.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchProjects.fulfilled, (state, action) => {
        state.isLoading = false;
        state.projects = action.payload;
      })
      .addCase(fetchProjects.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Create Project
      .addCase(createProject.fulfilled, (state, action) => {
        state.projects.push(action.payload);
        state.currentProject = action.payload;
      })
      // Update Project
      .addCase(updateProject.fulfilled, (state, action) => {
        const index = state.projects.findIndex(p => p.id === action.payload.id);
        if (index !== -1) {
          state.projects[index] = action.payload;
        }
        if (state.currentProject?.id === action.payload.id) {
          state.currentProject = action.payload;
        }
      })
      // Delete Project
      .addCase(deleteProject.fulfilled, (state, action) => {
        state.projects = state.projects.filter(p => p.id !== action.payload);
        if (state.currentProject?.id === action.payload) {
          state.currentProject = null;
        }
      });
  },
});

export const { setFilter, setCurrentProject, clearError } = projectSlice.actions;
export default projectSlice.reducer;

// Selectors
export const selectAllProjects = (state: { projects: ProjectState }) => state.projects.projects;
export const selectCurrentProject = (state: { projects: ProjectState }) => state.projects.currentProject;
export const selectProjectsLoading = (state: { projects: ProjectState }) => state.projects.isLoading;
export const selectProjectsError = (state: { projects: ProjectState }) => state.projects.error;
export const selectProjectFilters = (state: { projects: ProjectState }) => state.projects.filters;

export const selectFilteredProjects = (state: { projects: ProjectState }) => {
  const { projects, filters } = state.projects;
  let filtered = [...projects];

  // Apply status filter
  switch (filters.status) {
    case "active":
      filtered = filtered.filter(p => 
        p.status === "IN_PROGRESS" || p.status === "COMPLETE" || p.status === "DRAFT"
      );
      break;
    case "complete":
      filtered = filtered.filter(p => p.status === "COMPLETE");
      break;
    case "draft":
      filtered = filtered.filter(p => p.status === "DRAFT");
      break;
    case "in progress":
      filtered = filtered.filter(p => p.status === "IN_PROGRESS");
      break;
    case "archived":
      filtered = filtered.filter(p => p.status === "ARCHIVED");
      break;
    // "all" shows everything
  }

  // Apply search filter
  if (filters.search) {
    filtered = filtered.filter(p => 
      p.name.toLowerCase().includes(filters.search.toLowerCase())
    );
  }

  return filtered;
};