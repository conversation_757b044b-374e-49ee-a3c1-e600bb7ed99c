import { configureStore } from '@reduxjs/toolkit';
import userReducer from './slices/userSlice';
import rolePermissionReducer from './slices/rolePermissionSlice';
import teamReducer from './slices/teamSlice';
import jobOfferReducer from './slices/hiring-system/jobOfferSlice';
import projectReducer from './slices/hiring-system/projectSlice';
import phaseReducer from './slices/phaseSlice'; 

export const store = configureStore({
  reducer: {
    users: userReducer,
    rolePermission: rolePermissionReducer,
    teams: teamReducer,
    jobOffer: jobOfferReducer,
    projects: projectReducer,
    phases: phaseReducer,

   
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
