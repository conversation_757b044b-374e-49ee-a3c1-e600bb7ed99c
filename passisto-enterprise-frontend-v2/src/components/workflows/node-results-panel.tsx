"use client"

import { useState, use<PERSON>em<PERSON>, useRef, use<PERSON><PERSON>back, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { CheckCircle, XCircle, Clock, AlertCircle, ArrowRightCircle, ChevronDown, Eye, Code, GripVertical } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

export interface NodeResult {
  nodeId: string
  nodeType: string
  nodeName: string
  status: 'SUCCESS' | 'FAILED' | 'RUNNING' | 'PENDING' | 'WAITING_FOR_USER' | 'SKIPPED'
  executionTime?: string
  output?: any
  error?: string
  order?: number // Position in the execution order
}

interface NodeResultsPanelProps {
  results: NodeResult[]
  currentNodeId: string | null
  isRunning: boolean
}

export function NodeResultsPanel({ results, currentNodeId, isRunning }: NodeResultsPanelProps) {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [showRawOutput, setShowRawOutput] = useState(false)
  
  // Dragging state for the floating panel
  const [position, setPosition] = useState({ x: 16, y: 16 }) // Default position (bottom-right with 16px margin)
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const dragRef = useRef<HTMLDivElement>(null)

  // Handle mouse down on drag handle
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!dragRef.current) return
    
    const rect = dragRef.current.getBoundingClientRect()
    // Store the offset from mouse to the top-left corner of the panel
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
    setIsDragging(true)
    e.preventDefault()
  }, [])

  // Handle mouse move during drag
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging) return
    
    // Calculate where the top-left corner of the panel should be
    const panelLeft = e.clientX - dragOffset.x
    const panelTop = e.clientY - dragOffset.y
    
    // Convert to right/bottom positioning
    const panelWidth = dragRef.current?.offsetWidth || 350
    const panelHeight = dragRef.current?.offsetHeight || 100
    
    const newRight = window.innerWidth - panelLeft - panelWidth
    const newBottom = window.innerHeight - panelTop - panelHeight
    
    // Keep the panel within viewport bounds
    const minDistance = 16
    const maxRight = window.innerWidth - panelWidth - minDistance
    const maxBottom = window.innerHeight - panelHeight - minDistance
    
    setPosition({
      x: Math.max(minDistance, Math.min(maxRight, newRight)),
      y: Math.max(minDistance, Math.min(maxBottom, newBottom))
    })
  }, [isDragging, dragOffset])

  // Handle mouse up to stop dragging
  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Add event listeners for mouse move and up
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.userSelect = 'none' // Prevent text selection during drag
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.body.style.userSelect = ''
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  // Sort results by order if available
  const sortedResults = useMemo(() => {
    return [...results].sort((a, b) => {
      // If order is available, use it
      if (a.order !== undefined && b.order !== undefined) {
        return a.order - b.order;
      }
      // If only one has order, prioritize the one with order
      if (a.order !== undefined) return -1;
      if (b.order !== undefined) return 1;
      // Otherwise, keep original order
      return 0;
    });
  }, [results]);

  // If no node is selected, default to the current node or the last completed node
  const effectiveSelectedNodeId = selectedNodeId ?? currentNodeId ?? (sortedResults.length > 0 ? sortedResults[0].nodeId : null)

  // Get the selected node result
  const selectedResult = sortedResults.find(r => r.nodeId === effectiveSelectedNodeId)

  // Format the execution time
  const formatTime = (timeString?: string) => {
    if (!timeString) return 'N/A'
    try {
      return formatDistanceToNow(new Date(timeString), { addSuffix: true })
    } catch (e) {
      return timeString
    }
  }

  // Format output in a user-friendly way
  const formatUserFriendlyOutput = (output: any) => {
    if (!output) return null

    // If it's a simple string, return it directly
    if (typeof output === 'string') {
      return output
    }

    // If it's an object, extract user-friendly content
    if (typeof output === 'object') {
      // Helper function to extract readable text from nested objects
      const extractReadableText = (obj: any): string | null => {
        if (typeof obj === 'string') return obj
        if (typeof obj !== 'object' || obj === null) return String(obj)
        
        // Look for common text fields in nested objects
        const textFields = ['comment', 'text', 'content', 'message', 'transcript', 'transcription', 'description', 'title', 'summary']
        for (const field of textFields) {
          if (obj[field] && typeof obj[field] === 'string') {
            return obj[field]
          }
        }
        
        // For arrays, try to extract text from items
        if (Array.isArray(obj)) {
          const textItems = obj
            .map(item => extractReadableText(item))
            .filter(text => text && text.length > 0)
          if (textItems.length > 0) {
            return textItems.join('\n')
          }
        }
        
        return null
      }

      // Exclude technical/metadata fields that aren't user-friendly
      const excludedKeys = [
        'processedInputs', 'availableInputs', 'selectedInputs', 'enhancedPrompt', 
        'model', 'usage', 'contextualData', 'completedBy', 'files', 'error', 
        'errors', 'metadata', 'headers', 'executionTime', 'nodeId', 'status',
        'selectedOutputsCount'
      ]

      // Get all meaningful keys (excluding technical fields)
      const meaningfulKeys = Object.keys(output).filter(key => 
        !excludedKeys.includes(key) && 
        output[key] !== undefined && 
        output[key] !== null
      )

      // Collect all meaningful content
      const meaningfulContent: string[] = []

      // Priority order for displaying content - more important fields first
      const priorityKeys = [
        'comment', 'description', 'message', 'text', 'content', 'transcript', 'transcription', 
        'result', 'response', 'output', 'title', 'summary'
      ]

      // Add priority content first
      for (const key of priorityKeys) {
        if (meaningfulKeys.includes(key)) {
          const value = output[key]
          
          // Try to extract readable text first
          const readableText = extractReadableText(value)
          if (readableText && readableText.trim().length > 0) {
            meaningfulContent.push(readableText)
            // Remove from meaningfulKeys to avoid duplication
            meaningfulKeys.splice(meaningfulKeys.indexOf(key), 1)
            continue
          }
          
          // Fallback to simple string conversion for primitives
          if (typeof value === 'string' && value.trim().length > 0) {
            meaningfulContent.push(value)
            meaningfulKeys.splice(meaningfulKeys.indexOf(key), 1)
          } else if (typeof value === 'number' || typeof value === 'boolean') {
            meaningfulContent.push(`${key}: ${value}`)
            meaningfulKeys.splice(meaningfulKeys.indexOf(key), 1)
          }
        }
      }

      // Add remaining meaningful content as key-value pairs
      for (const key of meaningfulKeys) {
        const value = output[key]
        
        if (typeof value === 'string' && value.trim().length > 0) {
          meaningfulContent.push(`${key}: ${value}`)
        } else if (typeof value === 'number' || typeof value === 'boolean') {
          meaningfulContent.push(`${key}: ${value}`)
        } else if (value instanceof Date) {
          meaningfulContent.push(`${key}: ${value.toLocaleString()}`)
        } else {
          // Try to extract readable text from objects
          const readableText = extractReadableText(value)
          if (readableText && readableText.trim().length > 0) {
            meaningfulContent.push(`${key}: ${readableText}`)
          }
        }
      }

      // Return all meaningful content joined together
      if (meaningfulContent.length > 0) {
        return meaningfulContent.join('\n')
      }

      // If no meaningful content found (only errors or complex objects), return null
      return null
    }

    return String(output)
  }

  // Get user-friendly summary of the output
  const getOutputSummary = (output: any) => {
    if (!output) return 'No output'

    if (typeof output === 'string') {
      return output.length > 100 ? `${output.substring(0, 100)}...` : output
    }

    if (typeof output === 'object') {
      // Check for result/content first (excluding error fields)
      if (output.result && typeof output.result === 'string') {
        const result = output.result
        return result.length > 100 ? `${result.substring(0, 100)}...` : result
      }

      if (output.content && typeof output.content === 'string') {
        const content = output.content
        return content.length > 100 ? `${content.substring(0, 100)}...` : content
      }

      // For objects, show a summary (excluding error fields)
      const keys = Object.keys(output).filter(key => !['error', 'errors'].includes(key))
      if (keys.length === 0) {
        return 'No meaningful output'
      }
      return `Object with ${keys.length} properties: ${keys.slice(0, 3).join(', ')}${keys.length > 3 ? '...' : ''}`
    }

    return String(output)
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <Badge className="bg-green-100 text-green-800 border-green-300">Success</Badge>
      case 'FAILED':
        return <Badge className="bg-red-100 text-red-800 border-red-300">Failed</Badge>
      case 'RUNNING':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-300">Running</Badge>
      case 'WAITING_FOR_USER':
        return <Badge className="bg-amber-100 text-amber-800 border-amber-300">Waiting for User</Badge>
      case 'SKIPPED':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">Skipped</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">Pending</Badge>
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'RUNNING':
        return <Clock className="h-4 w-4 text-blue-500 animate-pulse" />
      case 'WAITING_FOR_USER':
        return <AlertCircle className="h-4 w-4 text-amber-500" />
      case 'SKIPPED':
        return <ArrowRightCircle className="h-4 w-4 text-gray-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  if (!isRunning && results.length === 0) {
    return null
  }

  // Calculate summary stats for collapsed view
  const successCount = results.filter(r => r.status === 'SUCCESS').length
  const failedCount = results.filter(r => r.status === 'FAILED').length
  const runningCount = results.filter(r => r.status === 'RUNNING').length
  const pendingCount = results.filter(r => r.status === 'PENDING').length

  // Render collapsed floating bar
  if (isCollapsed) {
    return (
      <div 
        ref={dragRef}
        className="fixed z-50 select-none"
        style={{ 
          right: `${position.x}px`, 
          bottom: `${position.y}px`,
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
      >
        <Card className="shadow-lg border-2 bg-white/95 backdrop-blur-sm">
          <div className="flex">
            {/* Drag handle */}
            <div 
              className="flex items-center justify-center w-6 bg-gray-100 hover:bg-gray-200 transition-colors border-r cursor-grab active:cursor-grabbing"
              onMouseDown={handleMouseDown}
            >
              <GripVertical className="h-4 w-4 text-gray-400" />
            </div>
            
            {/* Content */}
            <div 
              className="px-4 py-3 cursor-pointer hover:bg-gray-50 transition-colors flex-1"
              onClick={() => setIsCollapsed(false)}
            >
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Workflow Results</span>
                  {isRunning && (
                    <Badge className="bg-blue-100 text-blue-800 border-blue-300 text-xs px-2 py-1">
                      <Clock className="h-3 w-3 mr-1 animate-pulse" />
                      Running
                    </Badge>
                  )}
                </div>
                
                {results.length > 0 && (
                  <div className="flex items-center gap-1">
                    {successCount > 0 && (
                      <Badge className="bg-green-100 text-green-800 border-green-300 text-xs px-1.5 py-0.5">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {successCount}
                      </Badge>
                    )}
                    {failedCount > 0 && (
                      <Badge className="bg-red-100 text-red-800 border-red-300 text-xs px-1.5 py-0.5">
                        <XCircle className="h-3 w-3 mr-1" />
                        {failedCount}
                      </Badge>
                    )}
                    {runningCount > 0 && (
                      <Badge className="bg-blue-100 text-blue-800 border-blue-300 text-xs px-1.5 py-0.5">
                        <Clock className="h-3 w-3 mr-1 animate-pulse" />
                        {runningCount}
                      </Badge>
                    )}
                    {pendingCount > 0 && (
                      <Badge className="bg-gray-100 text-gray-800 border-gray-300 text-xs px-1.5 py-0.5">
                        <Clock className="h-3 w-3 mr-1" />
                        {pendingCount}
                      </Badge>
                    )}
                  </div>
                )}
                
                <ChevronDown className="h-4 w-4 rotate-180 text-gray-500" />
              </div>
              
              {/* Current status text */}
              {results.length > 0 && (
                <div className="text-xs text-gray-600 mt-1">
                  {isRunning && currentNodeId ? (
                    <span>Executing: {sortedResults.find(r => r.nodeId === currentNodeId)?.nodeName || 'Unknown node'}</span>
                  ) : (
                    <span>
                      {results.length} {results.length === 1 ? 'node' : 'nodes'} executed
                      {successCount > 0 && ` • ${successCount} completed`}
                      {failedCount > 0 && ` • ${failedCount} failed`}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>
    )
  }

  return (
    <Card className="mt-4">
      <Collapsible open={!isCollapsed} onOpenChange={(open) => setIsCollapsed(!open)}>
        <CollapsibleTrigger asChild>
          <CardHeader className="pb-2 cursor-pointer hover:bg-gray-50 transition-colors">
            <CardTitle className="text-lg flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span>Node Execution Results</span>
                {isRunning && <Badge className="bg-blue-100 text-blue-800 border-blue-300">Workflow Running</Badge>}
                {results.length > 0 && (
                  <Badge className="bg-gray-100 text-gray-800 border-gray-300">
                    {results.length} {results.length === 1 ? 'node' : 'nodes'}
                  </Badge>
                )}
              </div>
              <ChevronDown className={`h-5 w-5 transition-transform duration-200 text-gray-500 ${isCollapsed ? 'rotate-180' : ''}`} />
            </CardTitle>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-1 border rounded-md p-2">
            <h3 className="text-sm font-medium mb-2">Executed Nodes</h3>
            <ScrollArea className="h-[300px]">
              <div className="space-y-1">
                {sortedResults.length === 0 ? (
                  <div className="text-sm text-gray-500 p-2">No nodes executed yet</div>
                ) : (
                  sortedResults.map((result) => (
                    <div
                      key={result.nodeId}
                      className={`flex items-center p-2 rounded-md cursor-pointer hover:bg-gray-100 ${
                        result.nodeId === effectiveSelectedNodeId ? 'bg-gray-100' : ''
                      }`}
                      onClick={() => setSelectedNodeId(result.nodeId)}
                    >
                      {getStatusIcon(result.status)}
                      <div className="ml-2 flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">{result.nodeName}</div>
                        <div className="text-xs text-gray-500 truncate">{result.nodeType}</div>
                        {result.output && result.status === 'SUCCESS' && (
                          <div className="text-xs text-gray-400 truncate mt-1">
                            {getOutputSummary(result.output)}
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        {getStatusBadge(result.status)}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>

          <div className="md:col-span-2 border rounded-md p-2">
            <h3 className="text-sm font-medium mb-2">Node Details</h3>
            {selectedResult ? (
              <div>
                <div className="grid grid-cols-2 gap-2 mb-4">
                  <div>
                    <div className="text-xs text-gray-500">Node ID</div>
                    <div className="text-sm font-mono">{selectedResult.nodeId}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Status</div>
                    <div className="text-sm">{getStatusBadge(selectedResult.status)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Type</div>
                    <div className="text-sm">{selectedResult.nodeType}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Execution Time</div>
                    <div className="text-sm">{formatTime(selectedResult.executionTime)}</div>
                  </div>
                </div>

                <Tabs defaultValue="output">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="output">Output</TabsTrigger>
                    <TabsTrigger value="error" disabled={!selectedResult.error && !selectedResult.output?.error}>Error</TabsTrigger>
                  </TabsList>
                  <TabsContent value="output">
                    <div className="space-y-2">
                      {selectedResult.output && selectedResult.status === 'SUCCESS' && (
                        <div className="flex items-center justify-between">
                          <div className="text-xs text-gray-500">
                            {showRawOutput ? 'Raw Output' : 'User-Friendly Output'}
                          </div>
                          <button
                            onClick={() => setShowRawOutput(!showRawOutput)}
                            className="flex items-center gap-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
                          >
                            {showRawOutput ? <Eye className="h-3 w-3" /> : <Code className="h-3 w-3" />}
                            {showRawOutput ? 'Show Friendly' : 'Show Raw'}
                          </button>
                        </div>
                      )}
                      <ScrollArea className="h-[200px] w-full border rounded-md">
                        {selectedResult.output ? (
                          <div className="p-2">
                            {showRawOutput ? (
                              selectedResult.status !== 'SUCCESS' ? (
                                <div className="p-2 text-sm text-gray-500">
                                  {selectedResult.status === 'FAILED'
                                    ? 'Node execution failed - check the Error tab for details'
                                    : selectedResult.status === 'RUNNING'
                                    ? 'Node is still running...'
                                    : selectedResult.status === 'PENDING'
                                    ? 'Node execution is pending...'
                                    : selectedResult.status === 'WAITING_FOR_USER'
                                    ? 'Node is waiting for user input...'
                                    : selectedResult.status === 'SKIPPED'
                                    ? 'Node was skipped'
                                    : 'No output available for this node status'}
                                </div>
                              ) : (
                                <pre className="text-xs whitespace-pre-wrap">
                                  {typeof selectedResult.output === 'object'
                                    ? JSON.stringify(selectedResult.output, null, 2)
                                    : selectedResult.output}
                                </pre>
                              )
                            ) : (
                              (() => {
                                // If the node is not successful, show appropriate status message
                                if (selectedResult.status !== 'SUCCESS') {
                                  return (
                                    <div className="p-2 text-sm text-gray-500">
                                      {selectedResult.status === 'FAILED'
                                        ? 'Node execution failed - check the Error tab for details'
                                        : selectedResult.status === 'RUNNING'
                                        ? 'Node is still running...'
                                        : selectedResult.status === 'PENDING'
                                        ? 'Node execution is pending...'
                                        : selectedResult.status === 'WAITING_FOR_USER'
                                        ? 'Node is waiting for user input...'
                                        : selectedResult.status === 'SKIPPED'
                                        ? 'Node was skipped'
                                        : 'No output available for this node status'}
                                    </div>
                                  );
                                }

                                const friendlyOutput = formatUserFriendlyOutput(selectedResult.output);
                                return friendlyOutput ? (
                                  <div className="text-sm whitespace-pre-wrap">
                                    {friendlyOutput}
                                  </div>
                                ) : (
                                  <div className="p-2 text-sm text-gray-500">
                                    {(selectedResult.error || selectedResult.output?.error)
                                      ? 'Output contains errors - check the Error tab for details'
                                      : 'No meaningful output available'}
                                  </div>
                                );
                              })()
                            )}
                          </div>
                        ) : (
                          <div className="p-2 text-sm text-gray-500">No output available</div>
                        )}
                      </ScrollArea>
                    </div>
                  </TabsContent>
                  <TabsContent value="error">
                    <ScrollArea className="h-[200px] w-full border rounded-md">
                      {selectedResult.error ? (
                        <pre className="p-2 text-xs text-red-600 whitespace-pre-wrap">
                          {selectedResult.error}
                        </pre>
                      ) : selectedResult.output?.error ? (
                        <pre className="p-2 text-xs text-red-600 whitespace-pre-wrap">
                          {selectedResult.output.error}
                        </pre>
                      ) : (
                        <div className="p-2 text-sm text-gray-500">No errors</div>
                      )}
                    </ScrollArea>
                  </TabsContent>
                </Tabs>
              </div>
            ) : (
              <div className="text-sm text-gray-500 p-2">Select a node to view details</div>
            )}
          </div>
        </div>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}
