"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@clerk/nextjs";
import { useBackendUser } from "@/hooks/useBackendUser";
import socketService from "@/services/socket";

interface SocketEvent {
  id: string;
  timestamp: Date;
  event: string;
  data: any;
  workflowId?: string;
  workflowRunId?: string;
  nodeId?: string;
}

export function SocketEventDebugger() {
  const [events, setEvents] = useState<SocketEvent[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const { getToken } = useAuth();
  const { backendUser } = useBackendUser();

  // Connect to socket with authentication
  const connectSocket = async () => {
    try {
      const token = await getToken();
      console.log('[SocketDebugger] Connecting with token:', token ? 'Present' : 'Missing');
      console.log('[SocketDebugger] User info:', backendUser);
      socketService.connect(token);
      setIsConnected(true);
    } catch (error) {
      console.error('[SocketDebugger] Failed to connect to socket:', error);
    }
  };

  // Start listening to events
  const startListening = () => {
    if (!isConnected) {
      connectSocket();
    }

    const eventTypes = [
      'workflowRunProgress',
      'nodeRunProgress',
      'nodeExecution',
      'workflowCompleted',
      'decisionNodeResult'
    ];

    // Clear existing listeners first
    eventTypes.forEach(eventType => {
      socketService.removeListener(eventType, () => {});
    });

    eventTypes.forEach(eventType => {
      const listener = (data: any) => {
        const event: SocketEvent = {
          id: `${Date.now()}-${Math.random()}`,
          timestamp: new Date(),
          event: eventType,
          data,
          workflowId: data.workflowId,
          workflowRunId: data.workflowRunId,
          nodeId: data.nodeId
        };

        console.log(`[SocketDebugger] Received ${eventType}:`, data);
        setEvents(prev => [event, ...prev].slice(0, 50)); // Keep last 50 events
      };

      socketService.addListener(eventType, listener);
    });

    setIsListening(true);
  };

  // Stop listening to events
  const stopListening = () => {
    const eventTypes = [
      'workflowRunProgress',
      'nodeRunProgress',
      'nodeExecution',
      'workflowCompleted',
      'decisionNodeResult'
    ];

    eventTypes.forEach(eventType => {
      socketService.removeListener(eventType, () => {});
    });

    setIsListening(false);
  };

  // Clear events
  const clearEvents = () => {
    setEvents([]);
  };

  // Auto-connect on mount
  useEffect(() => {
    connectSocket();
    return () => {
      stopListening();
    };
  }, []);

  const getEventBadgeColor = (eventType: string) => {
    switch (eventType) {
      case 'workflowRunProgress':
        return 'bg-blue-100 text-blue-800';
      case 'nodeRunProgress':
        return 'bg-green-100 text-green-800';
      case 'nodeExecution':
        return 'bg-purple-100 text-purple-800';
      case 'workflowCompleted':
        return 'bg-gray-100 text-gray-800';
      case 'decisionNodeResult':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Socket Event Debugger</span>
          <div className="flex items-center gap-2">
            <Badge className={isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
            {backendUser && (
              <Badge className="bg-blue-100 text-blue-800">
                User: {backendUser.fullName}
              </Badge>
            )}
          </div>
        </CardTitle>
        <div className="flex gap-2">
          <Button
            onClick={isListening ? stopListening : startListening}
            variant={isListening ? "destructive" : "default"}
            size="sm"
          >
            {isListening ? 'Stop Listening' : 'Start Listening'}
          </Button>
          <Button onClick={clearEvents} variant="outline" size="sm">
            Clear Events
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-sm text-gray-600">
            Events received: {events.length} | Listening: {isListening ? 'Yes' : 'No'}
          </div>
          
          <ScrollArea className="h-[400px] w-full border rounded-md p-2">
            {events.length === 0 ? (
              <div className="text-center text-gray-500 py-8">
                No events received yet. Start listening and trigger some workflows to see events.
              </div>
            ) : (
              <div className="space-y-2">
                {events.map((event) => (
                  <div key={event.id} className="border rounded-md p-3 bg-gray-50">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge className={getEventBadgeColor(event.event)}>
                          {event.event}
                        </Badge>
                        <span className="text-xs text-gray-500">
                          {event.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <div className="flex gap-1">
                        {event.workflowId && (
                          <Badge variant="outline" className="text-xs">
                            W: {event.workflowId.slice(-8)}
                          </Badge>
                        )}
                        {event.workflowRunId && (
                          <Badge variant="outline" className="text-xs">
                            R: {event.workflowRunId.slice(-8)}
                          </Badge>
                        )}
                        {event.nodeId && (
                          <Badge variant="outline" className="text-xs">
                            N: {event.nodeId.slice(-8)}
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <details className="text-xs">
                      <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                        View Event Data
                      </summary>
                      <pre className="mt-2 p-2 bg-white rounded border text-xs overflow-auto">
                        {JSON.stringify(event.data, null, 2)}
                      </pre>
                    </details>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </CardContent>
    </Card>
  );
}
