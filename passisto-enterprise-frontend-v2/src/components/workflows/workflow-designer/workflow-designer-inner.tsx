"use client";

import { useR<PERSON>, useEffect, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useState } from "react";
import {
  ReactFlow,
  MiniMap,
  Controls,
  Background,
  BackgroundVariant,
  useReactFlow,
  useStore,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";

import { useWorkflowState } from "./use-workflow-state";
import { useWorkflowExecution } from "./use-workflow-execution";
import { useWorkflowApi } from "./use-workflow-api";
import { useWorkflowSocket } from "./use-workflow-socket";
import { useNodeOperations } from "./use-node-operations";
import { WorkflowToolbar } from "./workflow-toolbar";
import { WorkflowStatusPanels } from "./workflow-status-panels";
import { WorkflowHeader } from "./workflow-header";
import { suppressResizeObserverErrors, createStyledNodes, createStyledEdges } from "./utils";
import { WorkflowDesigner<PERSON>rops, NodeResult } from "./types";

// Import node components
import { TaskNode } from "../nodes/task-node";
import { DecisionNode } from "../nodes/decision-node";
import { StartNode } from "../nodes/start-node";
import { EndNode } from "../nodes/end-node";
import { AskAINode } from "../nodes/ask-ai-node";
import { SpeechToTextNode } from "../nodes/speech-to-text-node";
import { DataExtractionNode } from "../nodes/data-extraction-node";
import { EmailNode } from "../nodes/email-node";
import { PropertiesPanel } from "../properties-panel";
import { SaveWorkflowDialog } from "../save-workflow-dialog";
import { SocketEventDebugger } from "./socket-event-debugger";


// Define node types outside the component to prevent recreation on each render
const nodeTypes = {
  task: TaskNode,
  decision: DecisionNode,
  start: StartNode,
  end: EndNode,
  "ask-ai": AskAINode,
  "speech-to-text": SpeechToTextNode,
  "data-extraction": DataExtractionNode,
  email: EmailNode,
};

export function WorkflowDesignerInner({ currentWorkflow, onWorkflowSaved }: Readonly<WorkflowDesignerProps>) {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const reactFlowInstance = useReactFlow();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize workflow state
  const {
    nodes,
    edges,
    viewport,
    selectedNodes,
    selectedEdge,
    workflowTitle,
    workflowDescription,
    isEditingTitle,
    isSaveDialogOpen,
    showMiniMap,
    isInitialized,
    setNodes,
    // setEdges, // Unused
    setSelectedNodes,
    // setSelectedEdge, // Unused
    setWorkflowTitle,
    setWorkflowDescription,
    setIsEditingTitle,
    setIsSaveDialogOpen,
    setShowMiniMap,
    setIsInitialized,
    onNodesChange,
    onEdgesChange,
    onConnect,
    onNodeClick,
    onEdgeClick,
    onPaneClick,
    onNodeDragStart,
    onNodeDrag,
    onNodeDragEnd,
    updateNodeProperties,
    deleteSelectedEdge,
    deleteSelectedNodes,
    loadWorkflowFromProp,
  } = useWorkflowState(currentWorkflow);

  // Initialize workflow execution
  const {
    executionState,
    setExecutionState,
    executeWorkflow,
    stopExecution,
    determineExecutionOrder,
    // updateNodeResults, // Unused
    setCurrentNode,
    // addToExecutionPath, // Unused
    resetExecutionState,
    checkWorkflowRunningStatus,
  } = useWorkflowExecution(currentWorkflow, setNodes);

  // Initialize workflow API
  const {
    saveWorkflow,
    loadWorkflow,
    exportWorkflow,
  } = useWorkflowApi(
    currentWorkflow,
    workflowTitle,
    workflowDescription,
    onWorkflowSaved
  );

  // Initialize node operations
  const {
    onDrop,
    onDragOver,
  } = useNodeOperations(setNodes);

  // Initialize workflow socket
  const setIsRunning = useCallback((isRunning: boolean) => {
    setExecutionState(prev => ({ ...prev, isRunning }));
  }, [setExecutionState]);

  const setExecutionPathWrapper = useCallback(
    (pathOrUpdater: string[] | ((prev: string[]) => string[])) => {
      if (typeof pathOrUpdater === 'function') {
        setExecutionState(prev => ({
          ...prev,
          executionPath: pathOrUpdater(prev.executionPath)
        }));
      } else {
        setExecutionState(prev => ({ ...prev, executionPath: pathOrUpdater }));
      }
    },
    [setExecutionState]
  );

  const setNodeResultsWrapper = useCallback(
    (resultsOrUpdater: NodeResult[] | ((prev: NodeResult[]) => NodeResult[])) => {
      if (typeof resultsOrUpdater === 'function') {
        setExecutionState(prev => ({
          ...prev,
          nodeResults: resultsOrUpdater(prev.nodeResults)
        }));
      } else {
        setExecutionState(prev => ({ ...prev, nodeResults: resultsOrUpdater }));
      }
    },
    [setExecutionState]
  );

  useWorkflowSocket(
    currentWorkflow,
    nodes,
    edges,
    setNodes,
    executionState.isRunning,
    setIsRunning,
    setCurrentNode,
    setExecutionPathWrapper,
    setNodeResultsWrapper,
    determineExecutionOrder
  );

  // Check for running workflows when the component mounts
  useEffect(() => {
    if (currentWorkflow?.id && isInitialized) {
      // Add a small delay to ensure the nodes are loaded
      const timer = setTimeout(() => {
        checkWorkflowRunningStatus();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [currentWorkflow?.id, isInitialized, checkWorkflowRunningStatus]);

  // Handle keyboard events
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Only process if we're not running a workflow
      if (executionState.isRunning) return;

      if (event.key === "Delete") {
        if (selectedEdge) {
          deleteSelectedEdge();
        } else if (selectedNodes.length > 0) {
          deleteSelectedNodes();
        }
      }
    },
    [deleteSelectedEdge, selectedEdge, selectedNodes, executionState.isRunning, deleteSelectedNodes]
  );

  // Add keyboard event listener
  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown]);

  // Suppress ResizeObserver errors on mount and initialize
  useEffect(() => {
    suppressResizeObserverErrors();

    // Delay initialization to allow layout to stabilize
    const timer = setTimeout(() => {
      setIsInitialized(true);
    }, 500);

    return () => clearTimeout(timer);
  }, [setIsInitialized]);

  // Store the initial viewport
  // const [initialViewport, setInitialViewport] = useState({ x: 0, y: 0, zoom: 1 });

  // Load workflow when currentWorkflow changes
  useEffect(() => {
    loadWorkflowFromProp();
    // setInitialViewport(viewport);
  }, [currentWorkflow, loadWorkflowFromProp]);

  // Handle zoom changes
  // useEffect(() => {
  //   // Clear any existing timeout
  //   if (timeoutRef.current) {
  //     clearTimeout(timeoutRef.current);
  //   }

  //   // Show mini map immediately when zoom changes
  //   setShowMiniMap(true);

  //   // Use a debounced approach to hide the mini map
  //   timeoutRef.current = setTimeout(() => {
  //     // Keep mini map visible if zoom is not at default level
  //     if (Math.abs(zoom - 1) < 0.01) {
  //       setShowMiniMap(false);
  //     }
  //   }, 2000);

  //   return () => {
  //     if (timeoutRef.current) {
  //       clearTimeout(timeoutRef.current);
  //     }
  //   };
  // }, [setShowMiniMap]);

  // Handle title change
  const handleTitleChange = useCallback((newTitle: string) => {
    setWorkflowTitle(newTitle);
    setIsEditingTitle(false);

    // Auto-save when title changes
    setTimeout(() => {
      saveWorkflow();
    }, 100);
  }, [setWorkflowTitle, setIsEditingTitle, saveWorkflow]);

  // Handle save from dialog
  const handleSaveFromDialog = useCallback((name: string, description: string) => {
    setWorkflowTitle(name);
    setWorkflowDescription(description);

    // Update state first, then save in the next tick
    setTimeout(() => {
      saveWorkflow();
    }, 100);
  }, [setWorkflowTitle, setWorkflowDescription, saveWorkflow]);

  // Create styled nodes and edges
  const styledNodes = useMemo(() => createStyledNodes(nodes, selectedNodes), [nodes, selectedNodes]);
  const styledEdges = useMemo(() => createStyledEdges(edges, selectedEdge), [edges, selectedEdge]);

  // Memoize the mini map component
  const miniMapComponent = useMemo(() => {
    return showMiniMap ? <MiniMap /> : null;
  }, [showMiniMap]);

  // Edge options
  const edgeOptions = useMemo(() => {
    return {
      style: { strokeWidth: 2 },
    };
  }, []);

  // Handle drop wrapper
  const handleDrop = useCallback(
    (event: React.DragEvent) => {
      onDrop(event);
    },
    [onDrop]
  );

  return (
    <div className="h-full w-full flex overflow-hidden">
      <div className="flex-1 h-full flex flex-col overflow-hidden">
        {/* Header with editable title */}
        <WorkflowHeader
          workflowTitle={workflowTitle}
          isEditingTitle={isEditingTitle}
          setWorkflowTitle={setWorkflowTitle}
          setIsEditingTitle={setIsEditingTitle}
          handleTitleChange={handleTitleChange}
          openSaveDialog={() => setIsSaveDialogOpen(true)}
        />

        <div className="flex-1 relative overflow-hidden" ref={reactFlowWrapper}>
          {isInitialized ? (
            <ReactFlow
              nodes={styledNodes}
              edges={styledEdges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              onEdgeClick={onEdgeClick}
              onPaneClick={onPaneClick}
              onDrop={handleDrop}
              onDragOver={onDragOver}
              onNodeDragStart={onNodeDragStart}
              onNodeDrag={onNodeDrag}
              onNodeDragStop={onNodeDragEnd}
              nodeTypes={nodeTypes}
              defaultEdgeOptions={edgeOptions}
              // fitView
              // snapToGrid
              // snapGrid={[15, 15]}
              minZoom={0.1}
              maxZoom={4}
              defaultViewport={viewport}
              edgesFocusable
              selectNodesOnDrag={false}
              elementsSelectable
              className="h-full w-full"
            >
              <Controls />
              {miniMapComponent}
              <Background variant={"dots" as BackgroundVariant} gap={12} size={1} />

              {/* Toolbars */}
              <WorkflowToolbar
                isRunning={executionState.isRunning}
                selectedEdge={selectedEdge}
                selectedNodes={selectedNodes}
                executeWorkflow={executeWorkflow}
                stopExecution={stopExecution}
                saveWorkflow={saveWorkflow}
                exportWorkflow={exportWorkflow}
                loadWorkflow={loadWorkflow}
                deleteSelectedEdge={deleteSelectedEdge}
                deleteSelectedNodes={deleteSelectedNodes}
                zoomIn={() => reactFlowInstance.zoomIn()}
                zoomOut={() => reactFlowInstance.zoomOut()}
                openSaveDialog={() => setIsSaveDialogOpen(true)}
              />

              {/* Status Panels */}
              <WorkflowStatusPanels
                isRunning={executionState.isRunning}
                currentNodeId={executionState.currentNodeId}
                nodeResults={executionState.nodeResults}
                selectedEdge={selectedEdge}
              />
            </ReactFlow>
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-50">
              <div className="animate-pulse text-gray-500">Loading workflow designer...</div>
            </div>
          )}
        </div>
      </div>

      {/* Properties Panel */}
      {selectedNodes.length === 1 && !executionState.isRunning && (
        <PropertiesPanel
          node={selectedNodes[0]}
          onChange={(data) => updateNodeProperties(selectedNodes[0].id, data)}
          onClose={() => setSelectedNodes([])}
        />
      )}

      {/* Save Workflow Dialog */}
      <SaveWorkflowDialog
        open={isSaveDialogOpen}
        onOpenChange={setIsSaveDialogOpen}
        onSave={handleSaveFromDialog}
        currentWorkflow={currentWorkflow}
      />

      {/* Socket Event Debugger - Temporary for debugging */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-4 right-4 z-50 max-w-md">
          <SocketEventDebugger />
        </div>
      )} */}
    </div>
  );
}
