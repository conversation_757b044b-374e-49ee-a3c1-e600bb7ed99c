"use client"

import Re<PERSON>, { use<PERSON><PERSON>, useEffe<PERSON> } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { X, Plus, Trash2, FileAudio, FileVideo, FormInput, Eye, EyeOff, Database, Copy } from "lucide-react"
import { useUsers } from "@/hooks/useUsers"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { NodeFileUpload } from "@/components/workflows/node-file-upload"
import { Slider } from "@/components/ui/slider"
import { Accordion, Accordion<PERSON>ontent, <PERSON><PERSON>rdi<PERSON><PERSON><PERSON>, Accordion<PERSON>rigger } from "@/components/ui/accordion"
import { FormFieldsEditor } from "@/components/workflows/form-fields-editor"
import { useReactFlow } from "@xyflow/react"


interface PropertiesPanelProps {
  node: any
  onChange: (data: any) => void
  onClose: () => void
}

interface Field {
  name: string;
  description: string;
}

interface DataExtractionField extends Field {
  type?: string;
  required?: boolean;
}

interface VariableInfo {
  name: string;
  type: string;
  possibleValues?: string[];
  description?: string;
}

export function PropertiesPanel({ node, onChange, onClose }: PropertiesPanelProps) {
  const [properties, setProperties] = useState<any>({})
  const [newField, setNewField] = useState<Field>({ name: "", description: "" })
  const { users, loading: usersLoading } = useUsers()
  const { getNodes, getEdges } = useReactFlow()
  const [availableContextVars, setAvailableContextVars] = useState<Array<{nodeId: string, nodeName: string, key: string, value: any}>>([])

  // Helper function to format values correctly for expressions
  const formatValueForExpression = (value: string): string => {
    // Check if it's already quoted
    const isAlreadyQuoted = (value.startsWith("'") && value.endsWith("'")) ||
                          (value.startsWith('"') && value.endsWith('"'))

    if (!isAlreadyQuoted) {
      // Check if it's a boolean
      if (value === 'true' || value === 'false') {
        return value // Keep boolean values unquoted
      }
      // Check if it's a number
      else if (!isNaN(Number(value)) && value.trim() !== '') {
        return value // Keep numbers unquoted
      }
      // Otherwise, it's a string - add quotes
      else {
        return `'${value}'`
      }
    }

    return value
  }

  // Initialize properties from node data
  useEffect(() => {
    if (node?.data) {
      setProperties(node.data)
    }

    // Find available context variables from previous nodes
    if (node) {
      findAvailableContextVars(node.id)
    }
  }, [node])

  // Find available context variables from previous nodes
  const findAvailableContextVars = (nodeId: string) => {
    const nodes = getNodes()
    const edges = getEdges()

    // For AskAI, Email, Task, and Decision nodes, we want to get all upstream nodes, not just direct predecessors
    const currentNode = nodes.find(n => n.id === nodeId)
    const isSpecialNode = currentNode?.type === 'ask-ai' || currentNode?.type === 'email' || currentNode?.type === 'task' || currentNode?.type === 'decision'

    let sourceNodes = []

    if (isSpecialNode) {
      // Get all upstream nodes for AskAI nodes
      const visited = new Set<string>()
      const upstreamNodeIds = new Set<string>()

      // Recursive function to find all upstream nodes
      const findUpstreamNodes = (currentNodeId: string) => {
        if (visited.has(currentNodeId)) return
        visited.add(currentNodeId)

        // Find all incoming edges to this node
        const incomingEdges = edges.filter(edge => edge.target === currentNodeId)

        // Process each source node
        for (const edge of incomingEdges) {
          const sourceNodeId = edge.source
          upstreamNodeIds.add(sourceNodeId)

          // Recursively find upstream nodes of this source node
          findUpstreamNodes(sourceNodeId)
        }
      }

      // Start the recursive search
      findUpstreamNodes(nodeId)

      // Get the actual node objects
      sourceNodes = nodes.filter(n => upstreamNodeIds.has(n.id))

      console.log(`Found ${sourceNodes.length} upstream nodes for ${currentNode?.type} node ${nodeId}`)
      // For task nodes, add a more descriptive log message
      if (currentNode?.type === 'task') {
        console.log('Task node now has access to all upstream nodes for contextual data selection')
      }
      // For decision nodes, add a descriptive log message
      if (currentNode?.type === 'decision') {
        console.log('Decision node now has access to all upstream nodes for condition variables')
      }
    } else {
      // For non-AskAI nodes, just get direct predecessors as before
      // Find all incoming edges to this node
      const incomingEdges = edges.filter(edge => edge.target === nodeId)

      // Get source nodes from incoming edges
      const sourceNodeIds = incomingEdges.map(edge => edge.source)

      // Get unique source nodes
      const uniqueSourceNodeIds = [...new Set(sourceNodeIds)]

      // Find all nodes that are sources
      sourceNodes = nodes.filter(n => uniqueSourceNodeIds.includes(n.id))
    }

    // Extract available variables from source nodes based on node type
    const contextVars: Array<{nodeId: string, nodeName: string, key: string, value: any}> = []

    sourceNodes.forEach(sourceNode => {
      // Ensure nodeName is always a string
      const nodeName = String(sourceNode.data?.label || sourceNode.id)

      // Add variables based on node type
      switch (sourceNode.type) {
        case 'ask-ai':
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'result',
            value: 'AI response'
          })
          // Also add model and prompt for context
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'model',
            value: 'AI model used'
          })
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'enhancedPrompt',
            value: 'Full prompt sent to AI'
          })
          break
        case 'speech-to-text':
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'transcription',
            value: 'Transcribed text'
          })
          // Add service info
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'service',
            value: 'Transcription service used'
          })
          break
        case 'data-extraction':
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'extractedData',
            value: 'Extracted data'
          })
          // Add fields info
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'fields',
            value: 'Extraction fields'
          })
          break
        case 'decision':
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'result',
            value: 'Decision result (true/false)'
          })
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'condition',
            value: 'Decision condition'
          })
          break
        case 'task':
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'result',
            value: 'Task result'
          })
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'formData',
            value: 'Form data submitted by user'
          })
          break
        default:
          contextVars.push({
            nodeId: sourceNode.id,
            nodeName,
            key: 'result',
            value: 'Node result'
          })
      }
    })

    setAvailableContextVars(contextVars)
  }

  // Handle property changes
  const handleChange = (key: string, value: any) => {
    const newProperties = { ...properties, [key]: value }
    setProperties(newProperties)
    onChange(newProperties)
  }

  // Toggle a context variable selection
  const toggleContextVar = (contextVar: {nodeId: string, nodeName: string, key: string, value: any}) => {
    const currentSelectedVars = properties.selectedContextVars || []

    // Check if this variable is already selected
    const isSelected = currentSelectedVars.some(
      (v: any) => v.nodeId === contextVar.nodeId && v.key === contextVar.key
    )

    let newSelectedVars

    if (isSelected) {
      // Remove from selection
      newSelectedVars = currentSelectedVars.filter(
        (v: any) => !(v.nodeId === contextVar.nodeId && v.key === contextVar.key)
      )
    } else {
      // Add to selection with a default label
      newSelectedVars = [
        ...currentSelectedVars,
        {
          nodeId: contextVar.nodeId,
          key: contextVar.key,
          label: `${contextVar.nodeName} - ${contextVar.key}`
        }
      ]
    }

    handleChange('selectedContextVars', newSelectedVars)
  }

  // Check if a context variable is selected
  const isContextVarSelected = (nodeId: string, key: string) => {
    const selectedVars = properties.selectedContextVars || []
    return selectedVars.some((v: any) => v.nodeId === nodeId && v.key === key)
  }

  // Toggle a specific output selection for AskAI nodes
  const toggleOutputSelection = (nodeId: string, key: string, label: string) => {
    const currentSelectedOutputs = properties.selectedOutputs || []

    // Check if this output is already selected
    const isSelected = currentSelectedOutputs.some(
      (selection: any) => selection.nodeId === nodeId && selection.key === key
    )

    let newSelectedOutputs

    if (isSelected) {
      // Remove from selection
      newSelectedOutputs = currentSelectedOutputs.filter(
        (selection: any) => !(selection.nodeId === nodeId && selection.key === key)
      )
    } else {
      // Add to selection
      newSelectedOutputs = [
        ...currentSelectedOutputs,
        {
          nodeId,
          key,
          label
        }
      ]
    }

    handleChange('selectedOutputs', newSelectedOutputs)
  }

  // Check if a specific output is selected for AskAI nodes
  const isOutputSelected = (nodeId: string, key: string) => {
    const selectedOutputs = properties.selectedOutputs || []
    return selectedOutputs.some(
      (selection: any) => selection.nodeId === nodeId && selection.key === key
    )
  }

  // Render universal output selection component
  const renderOutputSelection = (nodeType: string, helpText?: string) => {
    if (availableContextVars.length === 0) {
      return (
        <div className="p-3 border rounded-md bg-gray-50">
          <p className="text-xs text-muted-foreground">
            No upstream nodes available. Connect this node to other nodes to select their outputs.
          </p>
        </div>
      )
    }

    return (
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="output-selection">
          <AccordionTrigger className="text-sm">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Input Configuration
              {properties.selectedOutputs?.length > 0 && (
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">
                  {properties.selectedOutputs.length} selected
                </span>
              )}
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2">
              <p className="text-xs text-muted-foreground">
                {helpText || "Select specific outputs from previous nodes to include as inputs"}
              </p>

              {/* Group by node */}
              {Array.from(new Set(availableContextVars.map(v => v.nodeId))).map(nodeId => {
                const nodeVars = availableContextVars.filter(v => v.nodeId === nodeId);
                const nodeName = nodeVars[0]?.nodeName || nodeId;

                return (
                  <div key={nodeId} className="border rounded-md p-2">
                    <h4 className="text-xs font-medium mb-2">{nodeName}</h4>
                    <div className="space-y-1">
                      {nodeVars.map(contextVar => (
                        <div
                          key={`${contextVar.nodeId}-${contextVar.key}`}
                          className="flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer"
                          onClick={() => toggleOutputSelection(contextVar.nodeId, contextVar.key, `${contextVar.nodeName} - ${contextVar.key}`)}
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <input
                                type="checkbox"
                                checked={isOutputSelected(contextVar.nodeId, contextVar.key)}
                                onChange={() => {}}
                                className="rounded"
                              />
                              <span className="text-xs font-medium">{contextVar.key}</span>
                            </div>
                            <p className="text-xs text-muted-foreground ml-6">{contextVar.value}</p>
                          </div>
                          <div className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded">
                            {`{${contextVar.nodeId}.${contextVar.key}}`}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}

              {properties.selectedOutputs?.length > 0 && (
                <div className="mt-4 p-2 bg-blue-50 rounded-md">
                  <p className="text-xs font-medium">Selected inputs:</p>
                  <ul className="text-xs mt-1 space-y-1">
                    {properties.selectedOutputs.map((selection: any) => (
                      <li key={`selected-output-${selection.nodeId}-${selection.key}`} className="flex items-center">
                        <span className="flex-1">{selection.label}</span>
                        <div className="text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded mr-2">
                          {`{${selection.nodeId}.${selection.key}}`}
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleOutputSelection(selection.nodeId, selection.key, selection.label)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    )
  }

  // Add a field to data extraction node
  const addField = () => {
    if (newField.name.trim()) {
      const fields = [...(properties.fields || []), { ...newField }]
      handleChange("fields", fields)
      setNewField({ name: "", description: "" })
    }
  }

  // Remove a field from data extraction node
  const removeField = (index: number) => {
    const fields = [...(properties.fields || [])]
    fields.splice(index, 1)
    handleChange("fields", fields)
  }

  // Extract possible values from task node form fields
  const extractPossibleValuesFromNode = (sourceNode: any): Record<string, VariableInfo> => {
    const variables: Record<string, VariableInfo> = {}

    // Handle different node types
    switch (sourceNode.type) {
      case 'task':
        // Extract form fields
        if (sourceNode.data?.formFields && Array.isArray(sourceNode.data.formFields)) {
          sourceNode.data.formFields.forEach((field: any) => {
            let type = 'text'
            let possibleValues: string[] | undefined = undefined

            // Handle different field types
            if (field.type === 'select' && field.options) {
              type = 'select'
              possibleValues = field.options.map((opt: any) => opt.value || opt.label)
            } else if (field.type === 'checkbox') {
              type = 'boolean'
              possibleValues = ['true', 'false']
            }

            // Add the field to variables
            variables[field.id] = {
              name: field.id,
              type,
              possibleValues,
              description: field.label || field.id
            }
          })
        }

        // Add task result
        variables['result'] = {
          name: 'result',
          type: 'object',
          description: 'Task result data'
        }
        break

      case 'ask-ai':
        variables['result'] = {
          name: 'result',
          type: 'string',
          description: 'AI response text'
        }
        break

      case 'decision':
        variables['result'] = {
          name: 'result',
          type: 'boolean',
          possibleValues: ['true', 'false'],
          description: 'Decision result (true/false)'
        }
        break

      case 'speech-to-text':
        variables['transcription'] = {
          name: 'transcription',
          type: 'string',
          description: 'Transcribed text'
        }
        break

      default:
        variables['result'] = {
          name: 'result',
          type: 'any',
          description: 'Node output'
        }
    }

    return variables
  }

  // Render different properties based on node type
  const renderProperties = () => {
    const commonProperties = (
      <>
        <div className="space-y-2">
          <Label htmlFor="label">Label</Label>
          <Input id="label" value={properties.label || ""} onChange={(e) => handleChange("label", e.target.value)} />
        </div>
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={properties.description || ""}
            onChange={(e) => handleChange("description", e.target.value)}
          />
        </div>
      </>
    )

    switch (node.type) {
      case "task":
        return (
          <>
            {commonProperties}
            <div className="space-y-2">
              <Label htmlFor="assignee">Assignee</Label>
              <Select
                value={properties.assignee || ""}
                onValueChange={(value) => handleChange("assignee", value)}
                disabled={usersLoading}
              >
                <SelectTrigger id="assignee" className="w-full">
                  <SelectValue placeholder="Select assignee">
                    {properties.assignee && (
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarFallback>
                            {users.find(u => u.id === properties.assignee)?.firstName?.charAt(0) || "?"}
                          </AvatarFallback>
                        </Avatar>
                        <span>{users.find(u => u.id === properties.assignee)?.firstName || "Select assignee"}</span>
                      </div>
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {users.map((  user) => (
                    <SelectItem key={user.id} value={user.id}>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarFallback>{user.firstName.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <span>{user.firstName}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="dueDate">Due Date</Label>
              <Input
                id="dueDate"
                type="date"
                value={properties.dueDate || ""}
                onChange={(e) => handleChange("dueDate", e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="critical"
                checked={properties.critical || false}
                onCheckedChange={(checked) => handleChange("critical", checked)}
              />
              <Label htmlFor="critical">Critical Task</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="requiresUserInput"
                checked={properties.requiresUserInput || false}
                onCheckedChange={(checked) => handleChange("requiresUserInput", checked)}
              />
              <Label htmlFor="requiresUserInput">Requires User Input</Label>
            </div>

            {/* Form Fields Editor */}
            <Accordion type="single" collapsible className="w-full mt-4">
              <AccordionItem value="form-fields">
                <AccordionTrigger className="flex items-center gap-2">
                  <FormInput className="h-4 w-4" />
                  <span>Form Fields</span>
                </AccordionTrigger>
                <AccordionContent>
                  <FormFieldsEditor
                    fields={properties.formFields || []}
                    onChange={(fields) => handleChange("formFields", fields)}
                  />
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            {/* Context Variables Configuration for Task Nodes */}
            {renderOutputSelection("task", "Select variables from any upstream nodes to show to the assignee. All selected variables will be displayed in the task UI for context.")}
          </>
        )
      case "decision":
        // Get all upstream nodes and their variables
        const upstreamNodes = getNodes().filter(n => {
          // Get all nodes that are upstream of this decision node
          const nodeId = node.id
          const visited = new Set<string>()
          const isUpstream = (currentNodeId: string): boolean => {
            if (visited.has(currentNodeId)) return false
            visited.add(currentNodeId)

            // If this is the target node, return true
            if (currentNodeId === nodeId) return true

            // Check if any outgoing edge leads to an upstream node
            const outgoingEdges = getEdges().filter(e => e.source === currentNodeId)
            return outgoingEdges.some(edge => isUpstream(edge.target))
          }

          // Check if this node is upstream of the decision node
          return n.id !== node.id && isUpstream(n.id)
        })

        // Group variables by node
        const nodeVariables: Record<string, Record<string, VariableInfo>> = {}
        upstreamNodes.forEach(sourceNode => {
          nodeVariables[sourceNode.id] = extractPossibleValuesFromNode(sourceNode)
        })

        return (
          <>
            {commonProperties}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Conditions</Label>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7"
                    onClick={() => {
                      // Add a new empty condition with default values
                      const conditions = [...(properties.conditions ?? [])]

                      // If there are existing conditions, add an operator after the last one
                      if (conditions.length > 0) {
                        // Add an operator entry after the last condition
                        conditions.push({
                          id: `op-${Date.now()}`,
                          type: 'operator',
                          value: 'AND'
                        })
                      }

                      // Add the new condition
                      conditions.push({
                        id: `cond-${Date.now()}`,
                        type: 'condition',
                        expression: "",
                        operator: "===",
                        value: "'value'"
                      })

                      handleChange("conditions", conditions)
                    }}
                  >
                    <Plus className="h-4 w-4 mr-1" /> Add Condition
                  </Button>
                </div>
              </div>

              {/* List of conditions */}
              <div>
                {(properties.conditions ?? []).length === 0 ? (
                  <div className="p-3 border rounded-md bg-gray-50 text-center text-sm text-muted-foreground">
                    No conditions added. Click "Add" to create a condition.
                  </div>
                ) : (
                  (properties.conditions ?? []).map((item: any, index: number) => (
                    <React.Fragment key={item.id}>
                      {item.type === 'operator' ? (
                        // Render operator selector
                        <div className="flex items-center justify-center my-2">
                          <Select
                            value={item.value}
                            onValueChange={(value) => {
                              const conditions = [...(properties.conditions ?? [])]
                              conditions[index] = { ...item, value }
                              handleChange("conditions", conditions)
                            }}
                          >
                            <SelectTrigger className="w-20 h-7 px-3 py-1 rounded-full text-xs font-medium
                              bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200 hover:text-blue-900">
                              <SelectValue placeholder="Operator" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="AND">AND</SelectItem>
                              <SelectItem value="OR">OR</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      ) : (
                        // Render condition
                        <div className="space-y-3 mb-4 p-3 border rounded-md bg-gray-50">
                      {/* Visual condition builder */}
                      <div className="grid grid-cols-12 gap-2 items-center">
                        {/* Variable selector */}
                        <div className="col-span-5">
                          <Select
                            value={item.variable ?? ""}
                            onValueChange={(value) => {
                              const conditions = [...(properties.conditions ?? [])]
                              const selectedVar = value.split('|')
                              const nodeId = selectedVar[0]
                              const varName = selectedVar[1]

                              // Format the value correctly for the expression
                              const currentValue = item.value ?? "'value'"
                              const formattedValue = formatValueForExpression(currentValue)

                              // Update both the display value and the actual expression
                              conditions[index] = {
                                ...item,
                                variable: value,
                                nodeId: nodeId,
                                varName: varName,
                                // Update the expression for backend compatibility
                                expression: `inputs['${nodeId}'].${varName} ${item.operator ?? '==='} ${formattedValue}`
                              }
                              handleChange("conditions", conditions)
                            }}
                          >
                            <SelectTrigger className="h-9">
                              <SelectValue placeholder="Select variable" />
                            </SelectTrigger>
                            <SelectContent>
                              {Object.entries(nodeVariables).map(([nodeId, variables]) => {
                                const sourceNode = upstreamNodes.find(n => n.id === nodeId)
                                const nodeName = String(sourceNode?.data?.label ?? nodeId)

                                return (
                                  <SelectGroup key={nodeId}>
                                    <SelectLabel>{nodeName}</SelectLabel>
                                    {Object.values(variables).map((variable) => (
                                      <SelectItem key={`${nodeId}|${variable.name}`} value={`${nodeId}|${variable.name}`}>
                                        {variable.name} ({variable.type})
                                      </SelectItem>
                                    ))}
                                  </SelectGroup>
                                )
                              })}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Operator selector */}
                        <div className="col-span-3">
                          <Select
                            value={item.operator ?? "==="}
                            onValueChange={(value) => {
                              const conditions = [...(properties.conditions ?? [])]

                              // Format the value correctly for the expression
                              const currentValue = item.value ?? "'value'"
                              const formattedValue = formatValueForExpression(currentValue)

                              conditions[index] = {
                                ...item,
                                operator: value,
                                // Update the expression for backend compatibility
                                expression: `inputs['${item.nodeId ?? "node-id"}'].${item.varName ?? "result"} ${value} ${formattedValue}`
                              }
                              handleChange("conditions", conditions)
                            }}
                          >
                            <SelectTrigger className="h-9">
                              <SelectValue placeholder="Operator" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="===">equals</SelectItem>
                              <SelectItem value="!==">not equals</SelectItem>
                              <SelectItem value=".includes">
                                contains
                              </SelectItem>
                              <SelectItem value=">">
                                greater than
                              </SelectItem>
                              <SelectItem value="<">
                                less than
                              </SelectItem>
                              <SelectItem value=">=">
                                greater or equal
                              </SelectItem>
                              <SelectItem value="<=">
                                less or equal
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Value input */}
                        <div className="col-span-3">
                          <Input
                            value={item.value ?? "'value'"}
                            placeholder="Value"
                            onChange={(e) => {
                              const conditions = [...(properties.conditions ?? [])]
                              const value = e.target.value

                              // Format the value correctly for the expression
                              const formattedValue = formatValueForExpression(value)

                              conditions[index] = {
                                ...item,
                                value: value,
                                // Update the expression for backend compatibility
                                expression: `inputs['${item.nodeId ?? "node-id"}'].${item.varName ?? "result"} ${item.operator ?? "==="} ${formattedValue}`
                              }
                              handleChange("conditions", conditions)
                            }}
                            className="h-9"
                          />
                        </div>

                        {/* Delete button */}
                        <div className="col-span-1 flex justify-end">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => {
                              const conditions = [...(properties.conditions ?? [])]

                              // If this is a condition and there's an operator before it, remove the operator too
                              if (item.type === 'condition' && index > 0 && conditions[index-1].type === 'operator') {
                                conditions.splice(index-1, 2) // Remove operator and condition
                              }
                              // If this is a condition and there's an operator after it, remove the operator too
                              else if (item.type === 'condition' && index < conditions.length - 1 && conditions[index+1].type === 'operator') {
                                conditions.splice(index, 2) // Remove condition and operator
                              }
                              // Otherwise just remove this item
                              else {
                                conditions.splice(index, 1)
                              }

                              handleChange("conditions", conditions)
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Show the actual expression for advanced users */}
                      <div className="text-xs text-muted-foreground bg-gray-100 p-2 rounded">
                        <div className="font-medium mb-1">Expression:</div>
                        <code>{item.expression ?? ""}</code>
                      </div>
                    </div>
                      )}
                    </React.Fragment>
                  ))
                )}
              </div>

              {/* <div className="p-3 bg-blue-50 border border-blue-100 rounded-md mt-2">
                <p className="text-xs text-blue-700">
                  <span className="font-medium">How this works:</span> Create one or more conditions to check.
                  Use the AND/OR operators between conditions to control how they are combined.
                </p>
                <div className="flex items-center gap-1 mt-2">
                  <div className="px-2 py-0.5 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">AND</div>
                  <p className="text-xs text-blue-700">All conditions must be true</p>
                </div>
                <div className="flex items-center gap-1 mt-1">
                  <div className="px-2 py-0.5 rounded-full bg-amber-100 text-amber-800 text-xs font-medium">OR</div>
                  <p className="text-xs text-blue-700">Any condition can be true</p>
                </div>
                <div className="border-t border-blue-100 mt-2 pt-2">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    <p className="text-xs text-blue-700">Yes path: When conditions are true</p>
                  </div>
                  <div className="flex items-center gap-1 mt-1">
                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                    <p className="text-xs text-blue-700">No path: When conditions are false</p>
                  </div>
                </div>
              </div> */}

              {/* For backward compatibility, update the single condition field */}
              {properties.condition && !properties.conditions && (
                <div className="p-2 bg-amber-50 border border-amber-200 rounded-md text-xs">
                  <p className="font-medium text-amber-700">Legacy condition detected</p>
                  <p className="text-amber-600 mt-1">Your existing condition will be converted to the new format when you add or modify conditions.</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 h-7 text-xs bg-white"
                    onClick={() => {
                      // Convert legacy condition to new format
                      handleChange("conditions", [{
                        id: `cond-${Date.now()}`,
                        type: 'condition',
                        expression: properties.condition
                      }])
                      handleChange("condition", undefined) // Remove legacy condition
                    }}
                  >
                    Convert Now
                  </Button>
                </div>
              )}
            </div>



            {/* Universal Input Configuration for Decision Nodes */}
            {renderOutputSelection("decision", "Select inputs from previous nodes to use in decision conditions. Variables can be referenced in condition expressions.")}

            {/* <div className="space-y-4 mt-4 p-3 border rounded-md bg-gray-50">
              <h4 className="text-sm font-medium">Decision Paths</h4>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <Label className="text-sm">Yes</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">Taken when condition is true</p>
                </div>
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <Label className="text-sm">No</Label>
                  </div>
                  <p className="text-xs text-muted-foreground">Taken when condition is false</p>
                </div>
              </div>
            </div> */}
          </>
        )
      case "ask-ai":
        return (
          <>
            {commonProperties}
            <div className="space-y-2">
              <Label htmlFor="prompt">Prompt</Label>
              <Textarea
                id="prompt"
                value={properties.prompt || ""}
                placeholder="Enter your prompt for the AI"
                onChange={(e) => handleChange("prompt", e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-xs text-muted-foreground mt-1">
                You can use variables from selected outputs with curly braces, e.g. {'{nodeId.result}'}
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="context">Context (Optional)</Label>
              <Textarea
                id="context"
                value={properties.context || ""}
                placeholder="Additional context for the AI"
                onChange={(e) => handleChange("context", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="model">AI Model</Label>
              <Select value={properties.model ?? "gemini-2.0-flash"} onValueChange={(value) => handleChange("model", value)}>
                <SelectTrigger id="model">
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Google</SelectLabel>
                    <SelectItem value="gemini-2.5-pro">Gemini 2.5 Flash</SelectItem>
                    <SelectItem value="gemini-2.5-flash">Gemini 2.5 Pro</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="advanced-options">
                <AccordionTrigger>Advanced Options</AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-2">
                    <Label htmlFor="maxTokens">Max Tokens</Label>
                    <Input
                      id="maxTokens"
                      type="number"
                      value={properties.maxTokens ?? "4096"}
                      placeholder="4096"
                      onChange={(e) => handleChange("maxTokens", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2 mt-2">
                    <div className="flex justify-between">
                      <Label htmlFor="temperature">Temperature</Label>
                      <span className="text-xs text-gray-500">{properties.temperature ?? "0.7"}</span>
                    </div>
                    <Slider
                      id="temperature"
                      min={0}
                      max={1}
                      step={0.1}
                      value={[parseFloat(properties.temperature ?? "0.7")]}
                      onValueChange={(value) => handleChange("temperature", value[0].toString())}
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Precise</span>
                      <span>Creative</span>
                    </div>
                  </div>
                </AccordionContent>
              </AccordionItem>

              {/* Output Selection */}
              {availableContextVars.length > 0 && (
                <AccordionItem value="output-selection">
                  <AccordionTrigger className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    <span>Output Selection</span>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      <p className="text-xs text-muted-foreground">
                        Select specific outputs from previous nodes to include in the AI prompt
                      </p>

                      {/* Group by node */}
                      {Array.from(new Set(availableContextVars.map(v => v.nodeId))).map(nodeId => {
                        const nodeVars = availableContextVars.filter(v => v.nodeId === nodeId);
                        const nodeName = nodeVars[0]?.nodeName || nodeId;

                        return (
                          <div key={`node-group-${nodeId}`} className="mt-4 border rounded-md overflow-hidden">
                            <div className="bg-gray-100 p-2 font-medium text-sm">{nodeName}</div>
                            <div className="divide-y">
                              {nodeVars.map((contextVar, index) => (
                                <div
                                  key={`${contextVar.nodeId}-${contextVar.key}-${index}`}
                                  className={`flex items-center justify-between p-2 ${isOutputSelected(contextVar.nodeId, contextVar.key) ? 'bg-violet-50' : 'bg-white'}`}
                                >
                                  <div className="flex-1">
                                    <div className="text-sm">{contextVar.key}</div>
                                    <div className="text-xs text-muted-foreground">{contextVar.value}</div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleOutputSelection(
                                      contextVar.nodeId,
                                      contextVar.key,
                                      `${nodeName} - ${contextVar.key}`
                                    )}
                                    className="ml-2"
                                  >
                                    {isOutputSelected(contextVar.nodeId, contextVar.key) ? (
                                      <Eye className="h-4 w-4 text-violet-600" />
                                    ) : (
                                      <EyeOff className="h-4 w-4" />
                                    )}
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}

                      {properties.selectedOutputs?.length > 0 && (
                        <div className="mt-4 p-2 bg-violet-50 rounded-md">
                          <p className="text-xs font-medium">Selected outputs:</p>
                          <ul className="text-xs mt-1 space-y-1">
                            {properties.selectedOutputs.map((selection: any) => (
                              <li key={`selected-output-${selection.nodeId}-${selection.key}`} className="flex items-center">
                                <span className="flex-1">{selection.label}</span>
                                <div className="text-xs text-violet-600 bg-violet-100 px-2 py-0.5 rounded mr-2">
                                  {`{${selection.nodeId}.${selection.key}}`}
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleOutputSelection(selection.nodeId, selection.key, selection.label)}
                                  className="h-6 w-6 p-0"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              )}
            </Accordion>
            {properties.response && (
              <div className="space-y-2 mt-4 p-2 bg-gray-50 rounded-md">
                <Label>Response Preview</Label>
                <div className="text-sm border p-2 rounded bg-white max-h-32 overflow-auto">{properties.response}</div>
              </div>
            )}
          </>
        )
      case "speech-to-text":
        return (
          <>
            {commonProperties}
            <div className="space-y-2">
              <Label htmlFor="sourceType">Source Type</Label>
              <Select
                value={properties.sourceType || "audio"}
                onValueChange={(value) => handleChange("sourceType", value)}
              >
                <SelectTrigger id="sourceType">
                  <SelectValue placeholder="Select source type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="audio">Audio File</SelectItem>
                  <SelectItem value="video">Video File</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="source">Source URL (Optional)</Label>
              <Input
                id="source"
                value={properties.source || ""}
                placeholder="Enter URL or upload a file below"
                onChange={(e) => handleChange("source", e.target.value)}
              />
              <p className="text-xs text-muted-foreground">
                You can either provide a URL or upload a file below
              </p>
            </div>

            <div className="space-y-2 mt-4 p-3 border rounded-md bg-gray-50">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  {properties.sourceType === "video" ? (
                    <FileVideo className="h-4 w-4" />
                  ) : (
                    <FileAudio className="h-4 w-4" />
                  )}
                  <Label>Upload {properties.sourceType || "audio"} file</Label>
                </div>
                <span className="text-xs text-blue-600">Auto-upload</span>
              </div>

              {node.id && (
                <>
                  <NodeFileUpload
                    nodeId={node.id}
                    accept={properties.sourceType === "video" ? "video/*" : "audio/*"}
                    onFilesChanged={() => handleChange("hasUpload", true)}
                  />
                  <p className="text-xs text-muted-foreground mt-2">
                    Files are automatically uploaded when selected.
                  </p>
                </>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="service">Transcription Service</Label>
              <Select
                value={properties.service || "simulated"}
                onValueChange={(value) => handleChange("service", value)}
              >
                <SelectTrigger id="service">
                  <SelectValue placeholder="Select transcription service" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="simulated">Simulated (Demo)</SelectItem>
                  <SelectItem value="revai">Rev.ai</SelectItem>
                  <SelectItem value="azure-whisper">Azure Whisper</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-muted-foreground">
                {properties.service === "revai" ?
                  "Rev.ai provides professional-grade speech-to-text transcription" :
                  properties.service === "azure-whisper" ?
                  "Azure Whisper provides AI-powered speech-to-text with speaker diarization" :
                  "Simulated service for demonstration purposes only"}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="demoMode">Demo Mode</Label>
              <div className="flex items-center space-x-2">
                <Switch
                  id="demoMode"
                  checked={properties.demoMode || false}
                  onCheckedChange={(checked) => handleChange("demoMode", checked)}
                />
                <span className="text-sm text-muted-foreground">Run without requiring file upload</span>
              </div>
              <p className="text-xs text-muted-foreground">
                When enabled, the node will run even if no file is uploaded
              </p>
            </div>
            {properties.transcription && (
              <div className="space-y-2 mt-4 p-2 bg-gray-50 rounded-md">
                <Label>Transcription Preview</Label>
                <div className="text-sm border p-2 rounded bg-white max-h-32 overflow-auto">
                  {properties.transcription}
                </div>
              </div>
            )}

            {/* Universal Input Configuration for Speech-to-Text Nodes */}
            {renderOutputSelection("speech-to-text", "Select inputs from previous nodes to include in speech-to-text processing context.")}
          </>
        )
      case "data-extraction":
        return (
          <>
            {commonProperties}
            <div className="space-y-4 mt-4">
              <Label>Fields to Extract</Label>

              {/* List of existing fields */}
              {properties.fields && properties.fields.length > 0 ? (
                <div className="space-y-2">
                  {renderDataExtractionFields()}
                </div>
              ) : (
                <div className="text-sm text-gray-500 italic">No fields defined yet</div>
              )}

              {/* Add new field form */}
              <div className="space-y-2 p-3 border rounded-md bg-gray-50">
                <Label htmlFor="fieldName">New Field</Label>
                <Input
                  id="fieldName"
                  value={newField.name}
                  placeholder="Field name"
                  onChange={(e) => setNewField({ ...newField, name: e.target.value })}
                  className="mb-2"
                />
                <Textarea
                  id="fieldDescription"
                  value={newField.description}
                  placeholder="Field description"
                  onChange={(e) => setNewField({ ...newField, description: e.target.value })}
                  className="mb-2"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addField}
                  className="w-full"
                  disabled={!newField.name || !newField.description}
                >
                  <Plus className="h-4 w-4 mr-2" /> Add Field
                </Button>
              </div>
            </div>

            {properties.extractedData && Object.keys(properties.extractedData).length > 0 && (
              <div className="space-y-2 mt-4 p-2 bg-gray-50 rounded-md">
                <Label>Extracted Data Preview</Label>
                <div className="text-sm border p-2 rounded bg-white max-h-32 overflow-auto">
                  <pre className="text-xs">{JSON.stringify(properties.extractedData, null, 2)}</pre>
                </div>
              </div>
            )}

            {/* Universal Input Configuration for Data Extraction Nodes */}
            {renderOutputSelection("data-extraction", "Select inputs from previous nodes to include in data extraction processing.")}
          </>
        )
      case "email":
        return (
          <>
            {commonProperties}
            <div className="space-y-2">
              <Label htmlFor="to">To</Label>
              <Input
                id="to"
                value={properties.to || ""}
                placeholder="<EMAIL>"
                onChange={(e) => handleChange("to", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={properties.subject || ""}
                placeholder="Email subject"
                onChange={(e) => handleChange("subject", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="body">Body</Label>
              <Textarea
                id="body"
                value={properties.body || ""}
                placeholder="Email body"
                onChange={(e) => handleChange("body", e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-xs text-muted-foreground mt-1">
                You can use variables from selected outputs with curly braces, e.g. {'{nodeId.result}'}
              </p>
            </div>

            {/* Output Selection */}
            {availableContextVars.length > 0 && (
              <Accordion type="single" collapsible className="w-full mt-4">
                <AccordionItem value="output-selection">
                  <AccordionTrigger className="flex items-center gap-2">
                    <Database className="h-4 w-4" />
                    <span>Output Selection</span>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="space-y-2">
                      <p className="text-xs text-muted-foreground">
                        Select specific outputs from previous nodes to include in the email content
                      </p>

                      {/* Group by node */}
                      {Array.from(new Set(availableContextVars.map(v => v.nodeId))).map(nodeId => {
                        const nodeVars = availableContextVars.filter(v => v.nodeId === nodeId);
                        const nodeName = nodeVars[0]?.nodeName || nodeId;

                        return (
                          <div key={`node-group-${nodeId}`} className="mt-4 border rounded-md overflow-hidden">
                            <div className="bg-gray-100 p-2 font-medium text-sm">{nodeName}</div>
                            <div className="divide-y">
                              {nodeVars.map((contextVar, index) => (
                                <div
                                  key={`${contextVar.nodeId}-${contextVar.key}-${index}`}
                                  className={`flex items-center justify-between p-2 ${isOutputSelected(contextVar.nodeId, contextVar.key) ? 'bg-purple-50' : 'bg-white'}`}
                                >
                                  <div className="flex-1">
                                    <div className="text-sm">{contextVar.key}</div>
                                    <div className="text-xs text-muted-foreground">{contextVar.value}</div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleOutputSelection(
                                      contextVar.nodeId,
                                      contextVar.key,
                                      `${nodeName} - ${contextVar.key}`
                                    )}
                                    className="ml-2"
                                  >
                                    {isOutputSelected(contextVar.nodeId, contextVar.key) ? (
                                      <Eye className="h-4 w-4 text-purple-600" />
                                    ) : (
                                      <EyeOff className="h-4 w-4" />
                                    )}
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}

                      {properties.selectedOutputs?.length > 0 && (
                        <div className="mt-4 p-2 bg-purple-50 rounded-md">
                          <p className="text-xs font-medium">Selected outputs:</p>
                          <ul className="text-xs mt-1 space-y-1">
                            {properties.selectedOutputs.map((selection: any) => (
                              <li key={`selected-output-${selection.nodeId}-${selection.key}`} className="flex items-center">
                                <span className="flex-1">{selection.label}</span>
                                <div className="text-xs text-purple-600 bg-purple-100 px-2 py-0.5 rounded mr-2">
                                  {`{${selection.nodeId}.${selection.key}}`}
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleOutputSelection(selection.nodeId, selection.key, selection.label)}
                                  className="h-6 w-6 p-0"
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            )}
          </>
        )
      default:
        return (
          <>
            {commonProperties}
            {/* Universal Input Configuration for Default Nodes */}
            {renderOutputSelection("default", "Select inputs from previous nodes to include in node processing.")}
          </>
        )
    }
  }

  // Render data extraction fields
  const renderDataExtractionFields = () => {
    return (properties.fields || []).map((field: DataExtractionField, index: number) => (
      <div key={index} className="flex items-center gap-2">
        <div className="flex-1">
          <Input
            value={field.name}
            onChange={(e) => {
              const fields = [...(properties.fields || [])]
              fields[index] = { ...field, name: e.target.value }
              handleChange("fields", fields)
            }}
            placeholder="Field name"
          />
        </div>
        <div className="flex-1">
          <Input
            value={field.description}
            onChange={(e) => {
              const fields = [...(properties.fields || [])]
              fields[index] = { ...field, description: e.target.value }
              handleChange("fields", fields)
            }}
            placeholder="Field description"
          />
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => removeField(index)}
          className="h-10 w-10"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ))
  }

  return (
    <Card className="w-80 h-full overflow-auto border-l">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Properties: {node.type}</CardTitle>
        <Button variant="ghost" size="icon" onClick={onClose} aria-label="Close properties panel">
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">{renderProperties()}</div>
      </CardContent>
    </Card>
  )
}

