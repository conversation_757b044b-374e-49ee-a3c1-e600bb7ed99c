{"24-7-available": "24/7 disponible", "CandidateInterviewPage": {"aiInterviewer": "AI Interviewer", "candidateInterviewTitle": "Entrevista candidata", "candidateLabel": "Candi<PERSON><PERSON>", "interviewNotFoundDescription": "No pudimos encontrar la entrevista que estás buscando. \nPuede haber sido eliminado o no existe.", "interviewNotFoundTitle": "Entrevista no encontrada", "interviewTypeCardTitle": "Tipo de entrevista", "levelBadge": "<PERSON><PERSON>", "loadingInterviewDetails": "Cargando detalles de la entrevista ...", "permissionDeniedToastDescription": "No tiene permiso para acceder a la página de entrevistas.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "returnToInterviews": "Volver a las entrevistas", "roleCardTitle": "Role", "scoreCardTitle": "<PERSON><PERSON><PERSON>", "scoreExcellent": "Excelente", "scoreGood": "Bien", "scoreNeedsImprovement": "Necesita mejora", "scoreNotScoredYet": "No anotado todavía", "statusCompleted": "Terminado", "statusInProgress": "En curso", "transcriptDescription": "Registro completo de la conversación", "transcriptTitle": "Transcripción de la entrevista", "viewFeedbackButton": "Ver comentarios"}, "ChatPage": {"creatingNewChatSession": "Creación de una nueva sesión de chat ..."}, "ChatSettings": {"allTab": "Todo", "ariaLabelSettings": "<PERSON><PERSON><PERSON><PERSON>", "dialogDescription": "Configure su experiencia de chat", "dialogTitle": "Configuración de chat", "errorLoadingKnowledgeBases": "Error de carga de bases de conocimiento: {error}", "ftpTab": "Ftp", "jiraTab": "<PERSON><PERSON>", "knowledge": "Conocimiento:", "knowledgeBasesDescription": "Seleccione qué bases de conocimiento buscar al hacer preguntas", "knowledgeBasesLabel": "Bases de conocimiento", "loadingKnowledgeBases": "Cargando bases de conocimiento ...", "webTab": "Web"}, "Chatbot": {"errors": {"apiEndpointNotFound": "El punto final de la API de chatbot no se encuentra. \nVerifique la configuración de URL API.", "authenticationError": "Error de autenticación. \nEs posible que no tenga permiso para acceder al chatbot.", "connectionRefused": "No se puede conectar al servidor de chatbot. \nCompruebe si el servidor se ejecuta en el puerto 5921.", "corsError": "Error CORS: el servidor ChatBot no está configurado para aceptar solicitudes de este origen.", "createSessionFailed": "No se pudo crear una nueva sesión de chat. \nPor favor intente de nuevo.", "deleteSessionFailed": "No se pudo eliminar la sesión de chat. \nPor favor intente de nuevo.", "errorMessagePrefix": "<PERSON> siento, encontré un error:", "loadHistory": "No se pudo cargar el historial de chat. \nPor favor intente de nuevo.", "loadSessions": "No se pudo cargar sesiones de chat. \nPor favor intente de nuevo.", "newSessionWelcome": "Nueva sesión detectada, mostrando un mensaje de bienvenida", "noSessionsFound": "No se encontraron sesiones de chat para este usuario.", "pleaseTryAgain": "Por favor intente de nuevo. \nAsegúrese de estar conectado a Internet y configurar el chatbot correctamente.", "sendMessageFailed": "No se pudo enviar un mensaje.", "serverError": "Error del servidor: {mensaje}", "userNotFound": "Información del usuario no disponible. \nVuelva a intentarlo en un momento."}, "logs": {"newSessionCreated": "Se creó una nueva sesión de chat con ID: {SessionID}", "newSessionDetected": "Nueva sesión detectada, mostrando un mensaje de bienvenida", "waitingForUser": "Esperando que el backenduser esté disponible antes de obtener sesiones de usuario", "waitingForUserHistory": "Esperando que el backenduser esté disponible antes de buscar el historial de chat"}, "welcomeMessage": "¡Hola! \n¿Cómo puedo ayudarte hoy?"}, "DashboardPage": {"active": "Activo", "active-teams": "equipos activos", "active-users": "usuarios activos", "ai-interview": "Entrevista de IA", "apr": "Abr", "aug": "Ago", "avg-users-team": "Avg. \nUsuarios por equipo", "completed": "terminado", "conv-rate": "% de tasa de conversión", "dec": "Dic", "email-builder": "Email Builder", "email-opened": "{count} abierto", "email-sent": "{count} enviado", "emails": "Correos electrónicos", "feb": "Feb", "form-builder": "Formulario", "form-submissions": "{count} presentaciones", "forms": "Formularios", "inactive": "Inactivo", "interview-completed": "{count} completado", "interview-pending": "{count} pendiente", "interviews": "Entrevistas", "jan": "Ene", "jul": "Jul", "jun": "Jun", "mar": "Mar", "may": "<PERSON><PERSON><PERSON>", "nov": "Nov", "oct": "Oct", "of-teams-have-members": "de los equipos tienen miembros", "opened": "abierto", "pending": "pendiente", "quick-actions": "Acciones rápidas", "sent": "enviado", "sep": "Sep", "submissions": "presentaciones", "team-activity-rate": "Tasa de actividad del equipo", "team-status": "Estatus de equipo", "total-teams": "Total de equipos", "total-users": "Usuarios totales", "user-growth": "Crecimiento de los usuarios", "users": "Usuarios", "users-per-team": "Usuarios por equipo", "conversion-rate": "{tasa}% tasa de conversión", "emails-opened": "{Count} abierto", "emails-sent": "{Count} enviado", "error": "Error", "failed-to-load-stats": "No se pudo cargar estadísticas", "loading": "Cargando", "no-team-activity": "No hay datos de actividad de equipo disponibles.", "no-team-distribution": "No hay datos de distribución de equipo disponibles.", "no-user-growth": "No hay datos de crecimiento del usuario disponibles.", "retry": "<PERSON>er", "team-activity": "Actividad de equipo", "team-distribution": "Distribución del equipo"}, "EditUserPage": {"buttons": {"cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar cambios", "saving": "Ahorro..."}, "description": "Actualice los detalles del usuario, los roles, los permisos y los equipos.", "errors": {"loadData": "Error de carga de datos", "saveFailed": "No se pudo actualizar el usuario", "teamsLoadFailed": "No se pudo cargar equipos"}, "formErrors": {"emailExists": "Ya existe un usuario con este correo electrónico.", "emailInvalid": "Por favor, introduce una dirección de correo electrónico válida.", "emailRequired": "Se requiere correo electrónico.", "fillFields": "Complete los siguientes campos:", "firstName": "Se requiere el primer nombre.", "lastName": "Se requiere apellido.", "roles": "Se debe seleccionar al menos un rol.", "userExists": "El usuario ya existe"}, "formLabels": {"email": "Correo electrónico", "firstName": "Nombre de pila", "lastName": "Apellido"}, "loading": {"permissions": "Cargando permisos ...", "roles": "Roles de carga ...", "teams": "Cargando equipos ..."}, "permissionDenied": {"description": "No tiene permiso para actualizar a los usuarios.", "title": "<PERSON><PERSON><PERSON> den<PERSON>ado"}, "permissions": {"effectiveDescription": "Estos son los permisos que este usuario tendrá después de aplicar todos los roles, equipos y anulaciones.", "effectiveTitle": "Permisos efectivos", "extraDescription": "<PERSON><PERSON>gar permisos adicionales más allá de lo que proporcionan los roles y equipos del usuario.", "extraTitle": "Permisos adicionales", "inheritedDescription": "Estos permisos se otorgan automáticamente a través de los roles y equipos del usuario.", "inheritedTitle": "Permisos hereditarios", "noEffective": "Sin permisos efectivos", "noInherited": "No hay permisos heredados. \n<PERSON><PERSON><PERSON> roles o equipos primero.", "noTeamsDescription": "Seleccione los equipos en la pestaña de equipos primero.", "noTeamsTitle": "No hay equipos seleccionados", "revoke": "Revocar", "selectProjects": "Seleccionar proyectos", "selectTeams": "Seleccionar equipos", "sourceRole": "Role:", "sourceTeam": "Equipo:"}, "placeholders": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Gama"}, "roles": {"title": "Roles de usuario"}, "successMessage": "Usuario actualizado correctamente", "tabs": {"permissions": "Anulaciones de permiso", "roles": "Roles", "teams": "Equipos"}, "teams": {"member": "miembro", "members": "<PERSON><PERSON><PERSON><PERSON>", "noTeams": "No hay equipos disponibles", "required": "Requerido", "title": "Equipos"}, "title": "<PERSON>ar usuario"}, "EditorPage": {"backToGenerator": "Volver al generador", "loadingEditor": "Editor de carga ...", "permissionDeniedToastDescription": "No tiene permiso para acceder a la página del creador de correo electrónico.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado"}, "InterviewDetailPage": {"callStartedToastDescription": "Ahora estás conectado con el entrevistador de IA.", "callStartedToastTitle": "La llamada comenzó", "candidateInformationCardTitle": "Información candidata", "completedAtLabel": "Completado en", "completedBadge": "Terminado", "failedToStartInterviewToast": "No se pudo comenzar la entrevista. \nPor favor intente de nuevo.", "feedbackAvailable": "Comentarios disponibles", "feedbackOnlyVisibleToCompany": "La retroalimentación solo es visible para la empresa", "feedbackStatusLabel": "Estado de retroalimentación", "interviewCompletedCardTitle": "Entrevista completada", "interviewCompletedDescription": "Esta entrevista se ha completado. \nSe han proporcionado comentarios a la empresa. \nPuede ver la transcripción de su entrevista a continuación.", "interviewDetailsTitle": "Detalles de la entrevista", "interviewNotFoundDescription": "No pudimos encontrar la entrevista que estás buscando. \nPuede haber sido eliminado o no existe.", "interviewNotFoundTitle": "Entrevista no encontrada", "interviewOrCandidateNotFoundDescription": "No pudimos encontrar la entrevista o el candidato que está buscando. \nPuede haber sido eliminado o no existe.", "interviewOrCandidateNotFoundTitle": "Entrevista o candidato no encontrado", "interviewTypeCardTitle": "Tipo de entrevista", "loadingInterview": "Entrevista de carga ...", "loadingInterviewDetails": "Cargando detalles de la entrevista ...", "noFeedback": "Sin comentarios", "notStartedBadge": "No comenzó", "readyToStartInterviewDescription": "Estás a punto de comenzar una entrevista {entrevistaType} para la posición {entrevista} {entrevista -level}. \nHaga clic en el botón Llamar cuando esté listo.", "readyToStartInterviewTitle": "¿Listo para comenzar tu entrevista?", "returnToInterviews": "Volver a las entrevistas", "roleCardTitle": "Role", "scoreLabel": "<PERSON><PERSON><PERSON>", "startButton": "Comenzar", "startInterviewButton": "Entrevista de inicio", "statusCompleted": "Terminado", "statusInProgress": "En curso", "statusLabel": "Estado", "statusPending": "Pendiente", "techStackCardTitle": "Pila de tecnología", "viewFullTranscriptButton": "Ver transcripción completa", "webcamErrorDescription": "No pudo acceder a su cámara web. \nVerifique los permisos.", "webcamErrorTitle": "Error de la webcam"}, "InterviewDialog": {"description": "Envíe la entrevista \"{rol} {nivel}\" a los candidatos."}, "NavUser": {"billingSettings": "Configuración de facturación", "failedToAccessBillingPortalToast": "No se pudo acceder al portal de facturación", "upgradeToPro": "Actualizar a Pro", "usageInformation": "Información de uso", "userAltText": "Usuario"}, "NewTeamPage": {"authenticationRequiredToast": "Se requiere autenticación", "backToTeams": "Volver a los equipos", "breadcrumbCreateNewTeam": "Crear un nuevo equipo", "breadcrumbTeams": "Equipos", "cancelButton": "<PERSON><PERSON><PERSON>", "cardDescription": "Cree un nuevo equipo y asigne permisos.", "cardTitle": "Crear un nuevo equipo", "createTeamButton": "<PERSON><PERSON><PERSON> equipo", "creatingTeamButton": "Creando ...", "creationFailedToastDescription": "Se produjo un error durante la creación.", "creationFailedToastTitle": "La creación falló", "descriptionLabel": "Descripción", "descriptionPlaceholder": "Una breve descripción del propósito y las responsabilidades del equipo", "descriptionRequired": "Se requiere descripción.", "failedToLoadPermissions": "No se pudo cargar permisos", "formErrorsGenericTitle": "Error", "formErrorsTitleFillFields": "Complete los siguientes campos:", "formErrorsTitleTeamExists": "El equipo ya existe", "loadingPermissions": "Cargando permisos ...", "permissionDeniedToastDescription": "No tienes permiso para crear equipos.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "permissionsDescription": "Los permisos asignados a este equipo se otorgarán a todos los miembros del equipo, a menos que se revocen explícitamente para usuarios específicos.", "permissionsSelected": "{count, plural, one {# permiso seleccionado} other {# permisos seleccionados}}", "teamAlreadyExists": "Ya existe un equipo con el nombre \"{TeamName}\".", "teamCreatedToastDescription": "{TeamName} se ha creado con éxito.", "teamCreatedToastTitle": "<PERSON><PERSON><PERSON> c<PERSON>o", "teamNameLabel": "Nombre del equipo", "teamNamePlaceholder": "Ingeniería", "teamNameRequired": "Se requiere el nombre del equipo.", "teamPermissionsTitle": "Permisos de equipo"}, "RolesPermissionsPage": {"allCategoriesOption": "Todas las categorías", "categoryTableHead": "Categoría", "errorLoadingData": "Error de carga de datos", "errorLoadingDataDescription": "No se pudo cargar roles y permisos.", "infoBoxDescription1": "En este sistema, los permisos se asignan directamente a los usuarios, no heredados a través de roles. \nLos roles se utilizan solo para fines organizativos. \nPara asignar permisos a un usuario, edite al usuario directamente desde la página de usuarios.", "infoBoxDescription2": "En esta página, puede ver todos los roles y permisos disponibles. \nUse las pestañas para cambiar entre vistas, el cuadro de búsqueda para encontrar elementos específicos y el filtro de categoría para organizar los permisos por tipo.", "infoBoxTitle": "Asignación de permiso directo", "loadingPermissions": "Cargando permisos ...", "noPermissionsFound": "No se encontraron permisos", "noRolesFound": "No se encuentran roles", "pageDescription": "Administre roles y permisos para su organización.", "pageTitle": "Roles", "permissionNameTableHead": "Nombre de permiso", "permissionSystemInfoButton": "Sistema de gestión de permisos", "permissionsManagementDescription": "Ver y filtrar permisos en su sistema", "permissionsManagementTitle": "Gestión de permisos", "permissionsTab": "<PERSON><PERSON><PERSON>", "roleAdministrator": "Administrador", "roleGuest": "<PERSON><PERSON><PERSON><PERSON>", "roleManager": "<PERSON><PERSON><PERSON>", "roleMember": "Miembro", "roleNameTableHead": "Nombre de rol", "rolesManagementDescription": "Ver y filtrar roles en su sistema", "rolesManagementTitle": "Gestión de roles", "rolesTab": "Roles", "searchPermissionsPlaceholder": "Permisos de búsqueda ...", "searchRolesPlaceholder": "Roles de búsqueda ...", "systemNameTableHead": "Nombre del sistema", "uncategorized": "Sin categoría"}, "SearchPage": {"noSearchQueryToastDescription": "Ingrese una consulta de búsqueda antes de buscar.", "noSearchQueryToastTitle": "Búsqueda vacía", "related-searches": "Búsquedas relacionadas:", "search-anything": "Buscar cualquier cosa ...", "searchErrorDescription": "No se pudo recuperar los resultados de búsqueda. \nPor favor intente de nuevo.", "searchErrorTitle": "<PERSON><PERSON><PERSON> <PERSON>", "searchNowButton": "Buscar ahora", "searching-in": "Buscando en:", "searchingButton": "Búsqueda", "title": "Search Passisto", "titleHighlight": "<PERSON><PERSON>"}, "SecurityInfoPage": {"a-user-with-the-manager-role-in-the-engineering-team-can-manage-engineering-resources": "Un usuario con el papel del gerente en el equipo de ingeniería puede administrar los recursos de ingeniería.", "adds-all-permissions-from-the-users-teams": "Agrega todos los permisos de los equipos del usuario.", "adds-any-extra-permissions-granted-directly-to-the-user": "Agrega cualquier permiso adicional otorgado directamente al usuario.", "administrator-role": "Rol de administrador", "as-your-organization-grows-roles-make-it-easier-to-onboard-new-users-with-the-appropriate-access-levels-without-having-to-configure-permissions-individually": "A medida que su organización crece, los roles hacen que sea más fácil incorporar a los nuevos usuarios con los niveles de acceso apropiados sin tener que configurar los permisos individualmente.", "assign-user-to-team": "Asignar usuario al equipo", "auditability": "Auditorabilidad", "back-to-roles-and-permissions": "Volver a roles", "benefits-of-role-based-access": "Beneficios del acceso basado en roles", "best-practices": "Mejores prácticas", "collects-all-permissions-from-the-users-roles": "Recopila todos los permisos de los roles del usuario.", "combined-permissions": "Permisos combinados", "considers-the-scope-of-each-permission-global-team-project-self": "Considera el alcance de cada permiso (global, equipo, proyecto, yo).", "create-data-provider": "<PERSON><PERSON><PERSON><PERSON> de datos", "create-data-provider-with-team-scope": "<PERSON><PERSON><PERSON> de <PERSON> (con alcance del equipo)", "create-user": "<PERSON><PERSON><PERSON> usuario", "different-teams-can-have-different-permission-sets-based-on-their-needs": "Los diferentes equipos pueden tener diferentes conjuntos de permisos en función de sus necesidades.", "each-role-contains-a-collection-of-permissions-with-specific-scopes-for-example": "Cada rol contiene una colección de permisos con ámbitos específicos. \nPor ejemplo:", "effective-permissions": "Permisos efectivos", "engineering-team": "Equipo de ingeniería", "example-scope-resolution": "Ejemplo: resolución de alcance", "export-data-with-project-scope": "Datos de exportación (con alcance del proyecto)", "extra-permissions": "Permisos adicionales", "global": "Global", "global-0": "Global", "global-scope": "Alcance global", "how-roles-provide-a-foundation-for-permissions-in-the-system": "Cómo los roles proporcionan una base para los permisos en el sistema.", "how-teams-provide-contextual-access-to-resources": "Cómo los equipos proporcionan acceso contextual a los recursos.", "if-a-user-has-the-view-analytics-permission-from": "Si un usuario tiene el permiso de 'Ver Analytics' de:", "important-note": "Nota importante", "individual-users-can-be-granted-extra-permissions-or-have-inherited-permissions-revoked-to-handle-exceptions": "A los usuarios individuales se les puede otorgar permisos adicionales o han heredado los permisos revocados para manejar las excepciones.", "instead-of-assigning-individual-permissions-to-each-user-you-can-assign-roles-that-contain-predefined-sets-of-permissions-making-user-management-more-efficient": "En lugar de asignar permisos individuales a cada usuario, puede asignar roles que contengan conjuntos predefinidos de permisos, lo que hace que la gestión del usuario sea más eficiente.", "jane-smith": "fulana", "john-doe": "<PERSON>", "key-0": "→", "manage-billing": "Administrar facturación", "manage-profile-with-self-scope": "Administre el perfil (con el margen de auto)", "manager-role": "<PERSON><PERSON> <PERSON> gerente", "multi-layered-permission-system": "Sistema de permiso de múltiples capas", "must-avoid": "¡Debe evitar!", "our-security-system-uses-a-multi-layered-approach-to-permissions-allowing-for-fine-grained-access-control-while-maintaining-flexibility-and-ease-of-management": "Nuestro sistema de seguridad utiliza un enfoque de múltiples capas para los permisos, lo que permite el control de acceso de grano fino mientras mantiene flexibilidad y facilidad de gestión.", "overview-0": "Descripción general", "permission-applies-across-the-entire-system-to-all-resources-of-the-relevant-type": "El permiso se aplica en todo el sistema a todos los recursos del tipo relevante.", "permission-applies-only-to-resources-created-by-or-directly-assigned-to-the-user": "El permiso se aplica solo a los recursos creados o directamente asignados al usuario.", "permission-applies-only-to-resources-owned-by-or-associated-with-the-users-team-s": "El permiso se aplica solo a los recursos propiedad o asociados con el equipo del usuario.", "permission-applies-only-to-specific-projects-regardless-of-team-ownership": "El permiso se aplica solo a proyectos específicos, independientemente de la propiedad del equipo.", "permission-inheritance-flow": "Flujo de herencia de permiso", "permission-model-overview": "Descripción general del modelo de permiso", "permission-resolution": "Resolución de permiso", "permission-scopes": "Alcance de permiso", "permission-scopes-define-the-boundaries-within-which-a-permission-applies-they-allow-for-fine-grained-control-over-what-resources-a-user-can-access-with-a-given-permission": "Los ámbitos de permiso definen los límites dentro de los cuales se aplica un permiso. \nPermiten un control de grano fino sobre qué recursos puede acceder un usuario con un permiso determinado.", "permissions-apply-only-to-resources-owned-by-the-team-this-is-the-most-common-scope-for-team-permissions": "Los permisos se aplican solo a los recursos propiedad del equipo. \nEste es el alcance más común para los permisos de equipo.", "permissions-granted-to-all-members-of-the-team-typically-scoped-to-team-resources": "Permisos otorgados a todos los miembros del equipo, generalmente alcanzados a los recursos del equipo.", "permissions-may-be-limited-to-specific-projects-that-the-team-is-working-on-even-if-those-projects-are-shared-with-other-teams": "Los permisos pueden limitarse a proyectos específicos en los que el equipo está trabajando, incluso si esos proyectos se comparten con otros equipos.", "project-scope": "Alcance del proyecto", "removes-any-permissions-explicitly-revoked-for-the-user": "Elimina cualquier permiso explícitamente revocado para el usuario.", "resource-scope": "Alcance de recursos", "revoked-permissions": "Permisos revocados", "robert-johnson": "<PERSON>", "role-based-access-control": "Control de acceso basado en roles", "role-permissions": "Per<PERSON><PERSON> de <PERSON>", "role-structure": "Estructura de papel", "roles": "Roles", "roles-are-defined-at-the-system-level-and-cannot-be-created-or-modified-by-regular-users-contact-your-system-administrator-if-you-need-a-new-role-or-modifications-to-existing-roles": "Los roles se definen a nivel del sistema y no pueden ser creados o modificados por usuarios regulares. \nPóngase en contacto con su administrador del sistema si necesita un nuevo rol o modificaciones en los roles existentes.", "roles-are-predefined-sets-of-permissions-that-represent-common-job-functions-or-responsibility-levels-within-your-organization-each-user-can-be-assigned-one-or-more-roles": "Los roles son conjuntos predefinidos de permisos que representan funciones de trabajo comunes o niveles de responsabilidad dentro de su organización. \nA cada usuario se le puede asignar uno o más roles.", "roles-ensure-that-users-with-similar-responsibilities-have-consistent-access-rights-reducing-the-risk-of-permission-inconsistencies": "Los roles as<PERSON>uran que los usuarios con responsabilidades similares tengan derechos de acceso consistentes, reduciendo el riesgo de inconsistencias de permiso.", "roles-provide-a-clear-structure-for-access-rights-making-it-easier-to-audit-who-has-access-to-what-and-why-they-have-that-access": "Los roles proporcionan una estructura clara para los derechos de acceso, lo que facilita la auditoría de quién tiene acceso a qué y por qué tienen ese acceso.", "scalability": "Escalabilidad", "scope-examples": "Ejemplos de alcance", "scope-hierarchy-broadest-to-narrowest": "Jerarq<PERSON><PERSON> de alcance (más estrecha a más estrecha)", "scope-resolution": "Resolución de alcance", "scope-types": "Tipos de alcance", "scopes": "Escopas", "security-system": "Sistema de seguridad", "simplified-management": "Gestión simplificada", "some-team-permissions-may-have-global-scope-allowing-team-members-to-perform-actions-across-the-entire-system": "Algunos permisos de equipo pueden tener un alcance global, lo que permite a los miembros del equipo realizar acciones en todo el sistema.", "standardization": "Normalización", "team": "Equipo", "team-0": "Equipo", "team-1": "Equipo", "team-based": "Basado en equipo", "team-based-permissions": "Permisos basados ​​en el equipo", "team-members": "Miembros del equipo", "team-members-0": "Miembros del equipo", "team-permission-scopes": "Alcances de permiso de equipo", "team-permissions": "Permisos de equipo", "team-permissions-0": "Permisos de equipo", "team-permissions-1": "Permisos de equipo", "team-permissions-are-typically-scoped-to-the-teams-resources-but-they-can-also-have-different-scope-types": "Los permisos del equipo suelen alcanzar los recursos del equipo, pero también pueden tener diferentes tipos de alcance.", "team-scope": "Alcance del equipo", "team-structure": "Estructura de equipo", "team-vs-role-permissions": "<PERSON><PERSON><PERSON> de equipo vs.", "teams": "Equipos", "teams-and-permissions": "Equipos y permisos", "teams-or-groups-allow-you-to-organize-users-and-grant-permissions-based-on-the-resources-they-need-to-access-teams-are-particularly-useful-for-departmental-or-project-based-access-control": "Los equipos (o grupos) le permiten organizar a los usuarios y otorgar permisos en función de los recursos a los que necesitan acceder. \nLos equipos son particularmente útiles para el control de acceso departamental o basado en proyectos.", "the-effective-scope-will-be-global-as-its-broader-than-team": "El alcance efectivo será global, ya que es más amplio que el equipo.", "the-leadership-team-with-global-scope": "El equipo de liderazgo (con alcance global)", "the-same-user-in-the-marketing-team-can-manage-marketing-resources": "El mismo usuario en el equipo de marketing puede administrar los recursos de marketing.", "the-specific-resources-projects-data-etc-that-the-team-has-access-to": "Los recursos específicos (proyectos, datos, etc.) a los que el equipo tiene acceso.", "their-manager-role-with-team-scope": "Su rol de gerente (con alcance del equipo)", "understanding-how-permission-scopes-limit-the-reach-of-permissions": "Comprender cómo los ámbitos de permiso limitan el alcance de los permisos.", "understanding-how-permissions-work-in-our-enterprise-system": "Comprender cómo funcionan los permisos en nuestro sistema empresarial.", "understanding-the-permission-model-and-access-control": "Comprender el modelo de permiso y el control de acceso.", "user-can-create-data-providers-only-for-their-team-s": "El usuario puede crear proveedores de datos solo para su (s) equipo (s).", "user-can-export-data-only-from-specific-projects-they-have-access-to": "El usuario puede exportar datos solo desde proyectos específicos a los que tiene acceso.", "user-can-only-manage-their-own-profile-information": "El usuario solo puede administrar su propia información de perfil.", "user-can-view-analytics-for-all-teams-and-projects-in-the-system": "El usuario puede ver el análisis para todos los equipos y proyectos en el sistema.", "user-overrides": "Anulaciones de usuarios", "users-are-assigned-roles-that-grant-a-set-of-permissions-roles-provide-a-baseline-of-access-appropriate-for-different-job-functions": "A los usuarios se les asigna roles que otorgan un conjunto de permisos. \nLos roles proporcionan una línea de base de acceso apropiado para diferentes funciones de trabajo.", "users-belong-to-teams-that-grant-additional-permissions-team-permissions-are-typically-scoped-to-the-teams-resources": "Los usuarios pertenecen a equipos que otorgan permisos adicionales. \nLos permisos del equipo suelen alcanzar los recursos del equipo.", "users-who-belong-to-the-team-and-inherit-its-permissions": "Usuarios que pertenecen al equipo y heredan sus permisos.", "view-analytics": "<PERSON><PERSON>", "view-analytics-0": "<PERSON><PERSON>", "view-analytics-with-global-scope": "Ver Analytics (con alcance global)", "what-are-permission-scopes": "¿Qué son los ámbitos de permiso?", "what-are-roles": "¿Qué son los roles?", "when-a-user-has-the-same-permission-with-different-scopes-e-g-from-different-roles-or-teams-the-system-uses-the-broadest-scope": "Cuando un usuario tiene el mismo permiso con diferentes ámbitos (por ejemplo, de diferentes roles o equipos), el sistema utiliza el alcance más amplio.", "when-determining-if-a-user-has-a-specific-permission-the-system": "Al determinar si un usuario tiene un permiso específico, el sistema:", "while-roles-define-what-a-user-can-do-based-on-their-job-function-teams-define-what-resources-they-can-access-this-combination-provides-a-flexible-and-powerful-access-control-system": "Si bien los roles definen lo que un usuario puede hacer en función de su función de trabajo, los equipos definen a qué recursos pueden acceder. \nEsta combinación proporciona un sistema de control de acceso flexible y potente."}, "TeamsPage": {"authenticationRequiredToast": "Se requiere autenticación", "cancelButton": "<PERSON><PERSON><PERSON>", "createTeamButton": "<PERSON><PERSON><PERSON> equipo", "deleteButton": "Bo<PERSON>r", "deleteCompanyTeamWarning": "El equipo predeterminado de 'Company' no se puede eliminar.", "deleteTeamDialogDescription": "¿Estás seguro de que quieres eliminar {TeamName}? \nEsta acción no se puede deshacer y eliminará todas las asociaciones de equipo.", "deleteTeamDialogTitle": "Eliminar equipo", "deletingButton": "Eliminar ...", "errorDeletingTeamDescription": "No se pudo eliminar el equipo.", "errorDeletingTeamTitle": "Error de eliminación del equipo", "errorLoadingTeamsDescription": "Hubo un error cargando los datos de su equipo.", "errorLoadingTeamsTitle": "Error de carga de equipos", "loadingTeams": "Cargando equipos ..", "noTeamsFoundDescription": "Crea tu primer equipo para comenzar.", "noTeamsFoundTitle": "No se encontraron equipos.", "permissionDeniedToastDescription": "No tiene permiso para acceder a la página del equipo.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "searchTeamsPlaceholder": "Equipos de búsqueda ...", "teamDeletedToastDescription": "{TeamName} se ha eliminado con éxito.", "teamDeletedToastTitle": "<PERSON><PERSON><PERSON> eliminado", "teamsDescription": "Administre los equipos de su organización.", "teamsTitle": "Equipos"}, "UserCardView": {"actionsSrOnly": "Comportamiento", "activate": "Activar", "ban": "Prohibición", "createdLabel": "<PERSON><PERSON><PERSON>", "delete": "Bo<PERSON>r", "edit": "<PERSON><PERSON>", "emailLabel": "Correo electrónico", "inactiveAccount": "Cuenta inactiva", "noPermissions": "Sin permisos", "noRolesAssigned": "No se asignan roles", "noTeams": "No hay equipos", "noUsersFoundDescription": "Intente ajustar su búsqueda o filtros", "noUsersFoundTitle": "No se encontraron usuarios", "permissionsCount": "{count} permiso {count, plural, uno {} otro {s}} asignado", "permissionsLabel": "<PERSON><PERSON><PERSON>", "rolesLabel": "Roles", "statusActive": "Activo", "statusInactive": "Inactivo", "teamsLabel": "Equipos", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "UserCreation": {"cancelButton": "<PERSON><PERSON><PERSON>", "cardDescription": "Agregue un nuevo usuario con roles, permisos y tareas de equipo.", "cardTitle": "Crear un nuevo usuario", "emailLabel": "Correo electrónico", "emailPlaceholder": "<EMAIL>", "extraPermissions": {"description": "<PERSON><PERSON>gar permisos adicionales más allá de lo que proporcionan los roles y equipos del usuario.", "loading": "Cargando permisos ...", "noPermissions": "No se seleccionaron permisos adicionales.", "title": "Permisos adicionales"}, "firstNameLabel": "Nombre de pila", "firstNamePlaceholder": "<PERSON>", "formErrors": {"emailConflict": "Ya existe un usuario con este correo electrónico.", "emailInvalid": "Por favor, introduce una dirección de correo electrónico válida.", "emailRequired": "Se requiere correo electrónico.", "error": "Error", "fillInFields": "Complete los siguientes campos:", "firstNameRequired": "Se requiere el primer nombre.", "lastNameRequired": "Se requiere apellido.", "roleRequired": "Se debe seleccionar al menos un rol.", "userExists": "El usuario ya existe."}, "inheritedPermissions": {"description": "Estos permisos se otorgan automáticamente a través de los roles y equipos del usuario.", "noPermissions": "No hay permisos heredados. \n<PERSON><PERSON><PERSON> roles o equipos primero.", "revokeAction": "Revocar", "sourceRole": "Rol: {nombre}", "sourceTeam": "Equipo: {nombre}", "title": "Permisos hereditarios"}, "lastNameLabel": "Apellido", "lastNamePlaceholder": "Gama", "rolesSection": {"label": "Roles de usuario", "loading": "Roles de carga ...", "noRoles": "No hay roles disponibles"}, "submitButton": "<PERSON><PERSON><PERSON> usuario", "tabs": {"permissions": "Anulaciones de permiso", "roles": "Roles", "teams": "Equipos"}, "teamsSection": {"companyTeamRequired": "(Requerido)", "failedToLoad": "No se pudo cargar equipos: {error}", "label": "Equipos", "loading": "Cargando equipos ...", "memberCount": "{<PERSON>, Plu<PERSON>, One {miembro} O<PERSON><PERSON> {miembros}}", "noTeams": "No hay equipos disponibles"}, "toasts": {"authRequired": "Se requiere autenticación", "creationFailedDescription": "Se produjo un error durante la creación.", "creationFailedTitle": "La creación falló", "dataError": "Error de carga de datos", "permissionDeniedDescription": "No tiene permiso para crear usuarios.", "permissionDeniedTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "userCreatedSuccess": "Usuario creado con éxito"}}, "UserTableView": {"actionsTableHead": "Comportamiento", "activateAction": "Activar", "banAction": "Prohibición", "deleteAction": "Bo<PERSON>r", "editUserButtonTitle": "<PERSON>ar usuario", "editUserSrOnly": "<PERSON><PERSON>", "emailTableHead": "Correo electrónico", "moreOptionsSrOnly": "Más opciones", "nameTableHead": "Nombre", "noRoles": "Sin roles", "noTeams": "No hay equipos", "noUsersFound": "No se encontraron usuarios.", "rolesTableHead": "Roles", "teamsTableHead": "Equipos", "viewDetailsButtonTitle": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "viewDetailsSrOnly": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "UserViewPage": {"accountDetailsHeading": "Detalles de la cuenta", "activeBadge": "Activo", "activityNotEnabled": "El seguimiento de la actividad no está habilitado", "activityTab": "Actividad", "authenticationErrorToastDescription": "Inicie sesión para ver los detalles del usuario.", "authenticationErrorToastTitle": "Error de autenticación", "backButton": "Atrás", "backToUsersButton": "Volver a los usuarios", "createdLabel": "<PERSON><PERSON><PERSON>", "editUserButton": "<PERSON>ar usuario", "enableActivityTracking": "Habilitar el seguimiento de la actividad en la configuración", "extraPermissionSource": "Permiso extra", "extraPermissionsHeading": "Permisos adicionales", "fromRoleSource": "De {rolename} rol", "fromTeamSource": "Del equipo {EquipoName}", "inactiveBadge": "Inactivo", "loadingUserDetails": "Cargando detalles del usuario ...", "noExtraPermissions": "No se asignan permisos adicionales", "noPermissionsAssigned": "Este usuario no tiene permisos", "noRevokedPermissions": "No hay permisos revocados", "noRolesAssigned": "No se asignan roles", "noTeamsAssigned": "No hay equipos asignados", "otherCategory": "<PERSON><PERSON>", "permissionDeniedToastDescription": "No tiene permiso para ver a los usuarios.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "permissionOverridesTab": "Anulaciones de permiso", "permissionsLabel": "<PERSON><PERSON><PERSON>", "permissionsTab": "<PERSON><PERSON><PERSON>", "revokedPermissionsHeading": "Permisos revocados", "rolesHeading": "Roles", "teamsHeading": "Equipos", "unknownSource": "Fuente desconocida", "userDetailsCardDescription": "Información completa sobre este usuario", "userDetailsCardTitle": "Detalles del usuario", "userNotFoundDescription": "El usuario solicitado no se pudo encontrar.", "userNotFoundTitle": "Usuario no encontrado", "userNotFoundToastDescription": "El usuario solicitado no se pudo encontrar.", "userNotFoundToastTitle": "Usuario no encontrado"}, "a-descriptive-name-for-this-integration": "Un nombre descriptivo para esta integración", "a-descriptive-name-for-this-integration-0": "Un nombre descriptivo para esta integración", "a-descriptive-name-for-this-integration-1": "Un nombre descriptivo para esta integración", "a-user-with-the-manager-role-in-the-engineering-team-can-manage-engineering-resources": "Un usuario con el papel de \"gerente\" en el equipo de \"ingeniería\" puede administrar recursos de ingeniería", "access-anytime": "Acceso en cualquier momento", "access-code": "Código de acceso", "access-granted": "Acceso o<PERSON>gado", "access-interview": "Entrevista de acceso", "access-powerful-ai-tools-to-streamline-your-recruitment-process-and-find-the-perfect-candidates-faster": "Acceda a las potentes herramientas de IA para optimizar su proceso de reclutamiento y encontrar los candidatos perfectos más rápido", "acme-inc": "ACME Inc.", "actions": "Comportamiento", "actions-0": "Comportamiento", "activate-user": "Activar usuario", "active": "Activo", "active-0": "Activo", "active-data-connections": "Conexiones de datos activas", "active-teams": "equipos activos", "active-users": "usuarios activos", "activity-log-coming-soon": "Registro de actividades Próximamente", "add": "Agregar", "add-candidate": "Agregar candidato", "add-column": "Agregar columna", "add-item": "Agregar elemento", "add-members": "Agregar miembros", "add-members-0": "Agregar miembros", "add-platform": "Agregar plataforma", "add-row": "Agregar fila", "add-team-members": "Agregar miembros del equipo", "add-user": "Agregar usuario", "add-users-to-teams-they-dont-need-to-be-in": "Agregar usuarios a los equipos en los que no necesitan estar", "addFieldsHeading": "Agregar campos", "adds-all-permissions-from-the-users-teams": "Agrega todos los permisos de los equipos del usuario", "adds-any-extra-permissions-granted-directly-to-the-user": "Agrega cualquier permiso adicional otorgado directamente al usuario", "administrator-role": "Rol de administrador", "advanced-ai-technology": "Tecnología de IA avanzada", "agent": "<PERSON><PERSON>", "ai-agent": "Agente de IA", "ai-email-template-generator": "Generador de plantilla de correo electrónico de IA", "ai-interview": "Entrevista de IA", "ai-interviewer": "AI Interviewer", "ai-powered-form-builder": "Constructor de formulario con IA", "ai-powered-interviewer": "AI Interviewer", "aiFieldSuggestions": {"anErrorOccurred": "Se produjo un error al generar sugerencias. \nPor favor intente de nuevo.", "applySuggestionsButton": "Aplicar sugerencias", "describeFormPurposeError": "Describe tu propósito de forma", "generateFieldsButton": "Generar campos", "generateSuggestionsFailedError": "No se pudo generar sugerencias. \nPor favor intente de nuevo.", "generatingButton": "Generando ...", "suggestionsCardFieldsCount": "{recuento} campos sugeridos", "suggestionsCardTitle": "{tí<PERSON><PERSON>}", "textareaPlaceholder": "Describa su propósito de formulario (por ejemplo, 'Formulario de solicitud de empleo para un puesto de marketing', 'Formulario de registro de eventos para una conferencia')"}, "alignment": "Alineación", "all": "Todo", "all-in-one-ai-recruitment-suite": "Suite de reclutamiento de IA todo en uno", "all-integrations": "Todas las integraciones", "all-levels": "Todos los niveles", "all-roles": "Todos los roles", "all-sources": "Todas las fuentes", "all-statuses": "Todos los estados", "all-statuses-0": "Todos los estados", "all-teams": "Todos los equipos", "all-types": "Todo tipo", "alt-text": "Texto alternativo", "always-grant-users-the-minimum-permissions-necessary-to-perform-their-job-functions-this-reduces-the-risk-of-accidental-or-intentional-misuse-of-privileges": "Siempre otorgue a los usuarios los permisos mínimos necesarios para realizar sus funciones de trabajo. \nEsto reduce el riesgo de mal uso accidental o intencional de los privilegios.", "an-error-occurred-please-try-again-later": "Ocurrió un error. \nVuelva a intentarlo más tarde.", "an-error-occurred-while-generating-the-form-please-try-again": "Se produjo un error al generar el formulario. \nPor favor intente de nuevo.", "an-unexpected-error-occurred": "Se produjo un error inesperado.", "an-unexpected-error-occurred-please-try-again-or-return-to-the-dashboard": "Se produjo un error inesperado. \nVuelva a intentarlo o regrese al tablero.", "an-unknown-error-occurred": "Ocurrió un error desconocido", "annual": "<PERSON><PERSON>", "anyone-with-this-link-can-view-and-submit-the-form": "Cualquier persona con este enlace puede ver y enviar el formulario", "api-integration": "Integración de API", "api-key-optional": "Clave API (opcional)", "api-token": "Token API", "api-token-is-required": "Se requiere token API.", "apr": "Abr", "are-you-sure-you-want-to-assign": "¿Estás seguro de que quieres asignar?", "are-you-sure-you-want-to-delete": "¿Estás seguro de que quieres eliminar?", "are-you-sure-you-want-to-delete-the-integration": "¿Estás seguro de que quieres eliminar la integración? \"", "are-you-sure-you-want-to-delete-this-chat-session-this-action-cannot-be-undone": "¿Estás seguro de que quieres eliminar esta sesión de chat? \nEsta acción no se puede deshacer.", "are-you-sure-you-want-to-delete-this-form": "¿Estás seguro de que quieres eliminar este formulario?", "are-you-sure-you-want-to-remove": "¿Estás seguro de que quieres eliminar?", "areas-of-interest": "Áreas de interés", "as-your-organization-grows-roles-make-it-easier-to-onboard-new-users-with-the-appropriate-access-levels-without-having-to-configure-permissions-individually": "A medida que su organización crece, los roles hacen que sea más fácil incorporar a los nuevos usuarios con los niveles de acceso apropiados sin tener que configurar los permisos individualmente.", "assign-administrator-roles-unnecessarily": "Asignar roles de administrador innecesariamente", "assign-integration": "Asignar integración", "assign-integrations": "Asignar integraciones", "assign-integrations-to": "Asignar integraciones a", "assign-multiple-roles-when-one-would-suffice": "<PERSON><PERSON><PERSON> roles cuando uno sería suficiente", "assign-roles-based-on-job-responsibilities": "Asignar roles basados ​​en responsabilidades laborales", "assign-to-team": "Asignar al equipo", "assign-user-to-team": "Asignar usuario al equipo", "assigning": "Asignación ...", "audit-checklist": "Lista de verificación de auditoría", "auditability": "Auditorabilidad", "authentication-required": "Se requiere autenticación", "authentication-required-0": "Se requiere autenticación", "auto": "Auto", "auto-0": "Auto", "avg-users-team": "Usuarios/equipo de AVG", "back-to-home": "Volver a casa", "back-to-roles-and-permissions": "Volver a roles", "back-to-team-details": "Volver al equipo Detalles", "back-to-team-details-0": "Volver al equipo Detalles", "back-to-teams": "Volver a los equipos", "background-color": "Color de fondo", "ban-user": "Prohibir el usuario", "bank-grade-encryption": "Cifrado de grado bancario", "behavioral": "De comportamiento", "benefits-of-role-based-access": "Beneficios del acceso basado en roles", "best-practices": "Mejores prácticas", "bg-background-shadow-none-border-rounded-lg": "bord background shadow none borde redondeado-lg", "bio": "Biografía", "bold": "<PERSON><PERSON><PERSON><PERSON>", "bolder": "<PERSON>ás audaz", "border-color": "Color de borde", "border-radius": "Radio fronterizo", "border-radius-0": "Radio fronterizo", "border-width": "<PERSON><PERSON>", "brand-colors": "Colores de la marca", "branding": "Herrada", "browse-files": "Explorar archivos", "built-for-your-workflow": "Construido para su flujo de trabajo", "bullet-list": "Lista de balas", "button": "Botón", "button-url": "URL de botón", "button-width": "<PERSON><PERSON>", "c-2025-your-company-all-rights-reserved": "© 2025 Su empresa. \nReservados todos los derechos.", "call": "<PERSON><PERSON><PERSON>", "camera-is-turned-off": "La cámara está apagada", "cancel": "<PERSON><PERSON><PERSON>", "cancel-anytime": "✓ Cancelar en cualquier momento", "candidate": "Candi<PERSON><PERSON>", "cannot-delete-default-team": "No se puede eliminar el equipo predeterminado", "caution-with-overrides": "Precaución con anulaciones", "center": "Centro", "chat": "<PERSON><PERSON><PERSON>", "chat-session-deleted-successfully": "Sesión de chat eliminada con éxito", "check-for-users-with-excessive-permissions": "Verifique los usuarios con permisos excesivos", "checkbox": "Caja", "choose-between-ftp-and-secure-sftp": "Elija entre FTP y STFTP.", "choose-role-level-and-tech-stack": "Elija el rol, el nivel y la pila tecnológica", "choose-your-plan": "Elige tu plan", "click-me": "Haz clic en mí", "close": "Cerca", "collects-all-permissions-from-the-users-roles": "Recopila todos los permisos de los roles del usuario", "color-preview": "Vista previa de color", "columns": "Columnas", "combined-permissions": "Permisos combinados", "company": "Compañía", "competitor-analysis": "Análisis de la competencia", "complete": "Completo", "complete-your-profile": "Completa tu perfil", "completed": "Terminado", "completed-0": "Terminado", "completed-at": "Completado en", "components": "Componentes", "conduct-regular-audits-of-your-permission-system-to-ensure-it-remains-effective-and-secure": "Realice auditorías regulares de su sistema de permisos para asegurarse de que siga siendo efectivo y seguro.", "configuration": "Configuración", "configuration-details-not-available": "Detalles de configuración no disponibles", "configure-your-ftp-integration-by-providing-the-required-details-below": "Configure su integración FTP proporcionando los detalles requeridos a continuación.", "configure-your-jira-integration-by-providing-the-required-details-below": "Configure su integración JIRA proporcionando los detalles requeridos a continuación.", "configure-your-search-experience": "Configure su experiencia de búsqueda", "configure-your-web-integration-by-providing-the-required-details-below": "Configure su integración web proporcionando los detalles requeridos a continuación.", "connect-your-form-to-external-services": "Conecte su formulario a servicios externos", "connection-type": "Tipo de conexión", "considers-the-scope-of-each-permission-global-team-project-self": "Considera el alcance de cada permiso (global, equipo, proyecto, yo)", "contact-sales": "Ventas de contacto", "contact-support": "Soporte de contacto", "content": "Contenido", "conv-rate": "% conv. \ntasa", "copy": "Copiar", "copy-prompt": "Indicación de copia", "copy-the-json-response": "Copiar la respuesta JSON", "copy-the-prompt-above": "Copie el indicador anterior", "create-an-account-and-get-access-to-all-features-for-15-days-no-credit-card-required": "Cree una cuenta y obtenga acceso a todas las funciones durante 15 días, no se requiere tarjeta de crédito.", "create-an-interview": "<PERSON>rea una entrevista", "create-and-manage-interview-templates-to-send-to-candidates": "Crear y administrar plantillas de entrevistas para enviar a los candidatos", "create-custom-forms-with-intelligent-field-suggestions-based-on-your-business-needs": "Cree formularios personalizados con sugerencias de campo inteligentes basadas en las necesidades de su negocio", "create-data-provider": "<PERSON><PERSON><PERSON><PERSON> de datos", "create-data-provider-with-team-scope": "<PERSON><PERSON><PERSON><PERSON> de datos con alcance del equipo", "create-ftp-integration": "Crear integración FTP", "create-jira-integration": "Crear integración jira", "create-new-form": "<PERSON><PERSON>r una nueva forma", "create-roles-with-excessive-permissions": "Crear roles con permisos excesivos", "create-team": "<PERSON><PERSON><PERSON> equipo", "create-teams-solely-for-permission-management": "Crear equipos únicamente para la gestión de permisos", "create-teams-with-overlapping-responsibilities": "Crear equipos con responsabilidades superpuestas", "create-user": "<PERSON><PERSON><PERSON> usuario", "create-web-integration": "Crear integración web", "create-your-first-form": "Crea tu primera forma", "create-your-first-form-by-describing-what-you-need-and-our-ai-will-generate-it-for-you": "Cree su primer formulario describiendo lo que necesita, y nuestra IA la generará para usted.", "createInterviewTemplatePage": {"addButton": "Agregar", "addTechnologiesHint": "Agregar tecnologías relevantes para este rol", "aiQuestionGenerationDescription": "Configure cuántas preguntas debe generar la IA para esta entrevista", "aiQuestionGenerationTitle": "Generación de preguntas de IA", "basedOnInterviewType": "Basado en el tipo de entrevista seleccionado", "basicInformationTitle": "Información básica", "behavioralInterview": "Entrevista conductual", "cancelButton": "<PERSON><PERSON><PERSON>", "completeRequiredFieldsTitle": "Completos campos requeridos", "createTemplateButton": "Crear plantilla", "creatingButton": "Creando ...", "creationFailedToastDescription": "Algo salió mal. \nPor favor intente de nuevo.", "creationFailedToastTitle": "La creación falló", "experienceLevelLabel": "Nivel de experiencia", "interviewTypeLabel": "Tipo de entrevista", "juniorLevel": "Junior (0-2 años)", "leadLevel": "Lead/Principal (8 años)", "levelRequired": "Se requiere nivel de experiencia", "midLevel": "<PERSON><PERSON> medio (2-5 años)", "missingInformationToastDescription": "Complete todos los campos requeridos antes de ahorrar.", "missingInformationToastTitle": "Información faltante", "mixedInterview": "Entrevista mixta", "noTechnologiesAdded": "No hay tecnologías agregadas todavía", "numberOfQuestionsLabel": "Número de preguntas", "numberOfQuestionsPlaceholder": "10", "pageDescription": "Diseñe una plantilla para entrevistas con IA: las preguntas se generarán automáticamente", "pageTitle": "Crear plantilla de entrevista", "permissionDeniedToastDescription": "No tiene permiso para acceder a esta página.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "questionCountHint": "Elija entre 1-50 preguntas", "questionCountRange": "El recuento de preguntas debe estar entre 1-50", "questionsGeneratedTitle": "{questionCount, plural, one {Se generará 1 pregunta} other {Se generarán # preguntas}}", "recommendedRangeLabel": "<PERSON><PERSON> recomenda<PERSON>", "requiredField": "(requerido)", "roleRequired": "Se requiere título de <PERSON>", "roleTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "roleTitlePlaceholder": "p.ej. \n<PERSON><PERSON><PERSON><PERSON> de frontend senior", "selectExperienceLevelPlaceholder": "Seleccionar nivel de experiencia", "selectInterviewTypeFirst": "Seleccione el tipo de entrevista primero", "selectInterviewTypePlaceholder": "Seleccionar el tipo de entrevista", "seniorLevel": "Senior (5 años)", "systemDesign": "Diseño del sistema", "techStackPlaceholder": "p.ej. \n<PERSON>, node.js, mecanografiado", "techStackRequired": "Se requiere al menos una tecnología", "technicalInterview": "Entrevista técnica", "technologyStackTitle": "Pila de tecnología", "templateCreatedToastDescription": "La plantilla de su entrevista se ha creado con preguntas generadas por {cuestionario}.", "templateCreatedToastTitle": "Plantilla creada", "typeRequired": "Se requiere el tipo de entrevista", "questionsGeneratedDescription": "La IA creará {questionCount} preguntas {type, select, technical {técnicas} behavioral {de comportamiento} system_design {de diseño de sistemas} mixed {mixtas} other {de entrevista}} relevantes basadas en tu plantilla"}, "created-at": "Creado a", "created-by": "<PERSON><PERSON>o por", "created-by-0": "Creado por:", "creating-more-specialized-teams-with-appropriate-permissions": "Creación de equipos más especializados con permisos apropiados", "csv-file-processed-successfully-interviews-will-be-sent-to-the-candidates": "Archivo CSV procesado correctamente. \nLas entrevistas se enviarán a los candidatos.", "csv-file-should-have-2-columns-full-name-and-email": "El archivo CSV debe tener 2 columnas: \"Nombre completo\" y \"correo electrónico\"", "csv-upload": "Subida CSV", "current-integration-status": "Estado de integración actual", "currently-processing": "Procesamiento actualmente", "currently-subscribed": "Actualmente suscrito", "custom": "Costumbre", "custom-features": "Características personalizadas", "custom-integration": "Integración personalizada", "custom-solutions-for-large-teams": "Soluciones personalizadas para equipos grandes", "customizable": "Personalizable", "customize-the-colors-to-match-your-brand-identity": "Personaliza los colores para que coincida con la identidad de tu marca", "dashboard": "Panel", "dashboardPreview": {"createdLabel": "Creado:", "deleteButtonTooltip": "Bo<PERSON>r", "deleteConfirmation": "¿Estás seguro de que quieres eliminar este formulario?", "editButton": "<PERSON><PERSON>", "fieldsCount": "{count} campos", "loadingForms": "Cargas de formularios ...", "noDescription": "Sin descripción", "noFormsCardDescription": "Todavía no has creado ningún formularios. \nUse el formulario de formulario anterior para comenzar.", "noFormsCardTitle": "<PERSON><PERSON> formas", "responsesButton": "Respuestas", "viewButton": "Vista", "yourFormsTitle": "<PERSON><PERSON> formas"}, "dashed": "Discontinuos", "data-integrations": "Integraciones de datos", "dedicated-support": "Soporte dedicado", "delete": "Bo<PERSON>r", "delete-chat-session": "Eliminar sesi<PERSON> de chat", "delete-error": "Error de eliminación:", "delete-integration": "Eliminar la integración", "delete-session": "Eliminar sesi<PERSON>", "delete-user": "Eliminar usuario", "deleting": "Eliminar ...", "describe-any-conditional-logic-e-g-show-extra-fields-based-on-selection": "Describa cualquier lógica condicional (por ejemplo, muestre campos adicionales basados ​​en la selección).", "describe-the-email-you-want-to-create-for-example-create-a-welcome-email-for-new-customers-of-my-fitness-company-fitlife-with-a-blue-color-scheme": "Describa el correo electrónico que desea crear. \n<PERSON>r ejemplo: \"Cree un correo electrónico de bienvenida para nuevos clientes de mi compañía de fitness FitLife, con un esquema de color azul\".", "describe-the-email-you-want-to-create-then-use-the-generated-prompt-with-your-preferred-ai-model": "Describa el correo electrónico que desea crear, luego use el indicador generado con su modelo AI preferido.", "describe-the-tone-formal-casual-exciting": "Describe el tono (formal, informal, emocionante)", "describe-the-type-of-email-you-want-to-create-and-our-ai-will-generate-a-template-for-you": "Describa el tipo de correo electrónico que desea crear, y nuestra IA generará una plantilla para usted.", "describe-validation-rules-e-g-required-fields-email-format-character-limits": "Describa las reglas de validación (por ejemplo, campos requeridos, formato de correo electrónico, límites de caracteres).", "describe-your-form": "Describe tu forma", "describe-your-form-purpose-e-g-job-application-form-for-a-marketing-position-event-registration-form-for-a-conference": "Describa su propósito de formulario (por ejemplo, 'Formulario de solicitud de empleo para un puesto de marketing', 'Formulario de registro de eventos para una conferencia')", "description": "Descripción", "description-enhanced": "Descripción mejorada!", "description-tips": "Consejos de descripción:", "designTab": "Diseño", "detailed-information-about-this-team": "Información detallada sobre este equipo", "details": "Detalles", "dialogDescription": "Envíe la entrevista \"{rol} {nivel}\" a los candidatos.", "different-teams-can-have-different-permission-sets-based-on-their-needs": "Diferentes equipos pueden tener diferentes conjuntos de permisos en función de sus necesidades.", "divider": "Divisor", "divider-color": "Color divisor", "divider-style": "Estilo divisor", "divider-width": "<PERSON><PERSON> de <PERSON>visor", "do": "<PERSON><PERSON>", "document-any-exceptions-to-standard-security-policies": "Documentar cualquier excepción a las políticas de seguridad estándar", "doe": "Gama", "domain": "<PERSON>inio", "domain-name": "Nombre de dominio", "domain-name-is-required": "Se requiere nombre de dominio.", "dotted": "Punteado", "drag-and-drop-a-csv-file-or-click-to-browse": "Arrastre y suelte un archivo CSV, o haga clic para navegar", "drag-components-here": "Arrastre los componentes aquí", "drag-components-to-the-canvas-or-click-to-add-them-to-the-bottom-of-your-email": "Arrastre los componentes al lienzo o haga clic para agregarlos a la parte inferior de su correo electrónico.", "dropdown": "Desplegable", "e-g-frontend-developer": "p.ej. \nDesarrollador frontend", "e-g-prd": "p.ej. \nPRD", "e-g-prd-not-the-full-project-name": "(por ejemplo, \"PRD\", no el nombre completo del proyecto).", "e-g-react": "p.ej. \n<PERSON><PERSON><PERSON><PERSON>", "e-g-web-development-ai-ml": "por ejemplo, desarrollo web, IA/ML", "each-role-contains-a-collection-of-permissions-with-specific-scopes-for-example": "Cada rol contiene una colección de permisos con ámbitos específicos. \nPor ejemplo:", "edit": "<PERSON><PERSON>", "edit-0": "<PERSON><PERSON>", "edit-description": "Editar descripción", "edit-integration": "Editar integración", "edit-interview-template": "Editar plantilla de entrevista", "edit-permissions": "<PERSON><PERSON> per<PERSON>", "edit-team": "Equipo de edición", "edit-team-0": "Equipo de edición", "edit-template": "Plantilla de edición", "editIntegrationPage": {"backToIntegrationDetails": "Volver a los detalles de integración", "editIntegrationTitle": "Editar integración", "integrationDetailsTitle": "Detalles de integración", "integrationNotFound": "Integración no encontrada.", "loadingIntegration": "Integración de carga ...", "modifySettingsDescription": "Modifique los campos a continuación para actualizar su configuración de integración.", "permissionDeniedToastDescription": "No tiene permiso para editar esta integración.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "updateDescription": "Actualice la configuración para {IntegrationName}."}, "editorPage": {"backToGeneratorButton": "Volver al generador", "loadingEditor": "Editor de carga ...", "permissionDeniedToastDescription": "No tiene permiso para acceder a la página del creador de correo electrónico.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado"}, "effective-permissions": "Permisos efectivos", "email": "Correo electrónico", "email-associated-with-your-jira-account": "Correo electrónico asociado con su cuenta JIRA", "email-builder": "Email Builder", "email-description": "Descripción del correo electrónico", "email-field": "Campo de correo electrónico", "email-preview": "Vista previa por correo electrónico", "email-preview-0": "Vista previa por correo electrónico", "emailBuilderPage": {"aiTemplateGenerator": {"aiProcessingToastDescription": "Se está generando su plantilla de correo electrónico. \nEsto podría tomar un momento.", "aiProcessingToastTitle": "AI está procesando ...", "emailDescriptionLabel": "Descripción del correo electrónico (opcional)", "emailDescriptionPlaceholder": "Proporcione más detalles para la IA, por ejemplo, \"un correo electrónico bienvenido para nuevos registros, destacando nuestras mejores características y un llamado a la acción para explorar productos\".", "emailSubjectLabel": "Asunto de correo electrónico", "emailSubjectPlaceholder": "por ejemplo, 'Bienvenido a nuestro boletín', '<PERSON><PERSON><PERSON> de lanzamiento de nuevos productos'", "enhanceDescriptionButton": "Mejorar la descripción con AI", "errorEnhancingDescriptionToastDescription": "Hubo un error que mejoró su descripción. \nPor favor intente de nuevo.", "errorEnhancingDescriptionToastTitle": "Descripción para mejorar el error", "errorGeneratingTemplateToastDescription": "Hubo un error de generar su plantilla de correo electrónico. \nPor favor intente de nuevo.", "errorGeneratingTemplateToastTitle": "Plantilla de generación de errores", "generateTemplateButton": "Generar plantilla", "generatingTemplateButton": "Generación de plantilla ...", "heading": "Generador de plantilla de correo electrónico de IA", "noSubjectError": "Proporcione un tema de correo electrónico.", "subheading": "Genere hermosas plantillas de correo electrónico con IA en segundos."}, "c-2025-your-company-all-rights-reserved": "© 2025 Su empresa. \nReservados todos los derechos.", "click-me": "Haz clic en mí", "email-image-alt-text": "Imagen de correo electrónico", "email-template-title": "Plantilla de correo electrónico", "error-loading-template": "Plantilla de carga de error", "error-saving-template": "Plantilla de ahorro de errores", "permissionDeniedToastDescription": "No tiene permiso para acceder a la página del creador de correo electrónico.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "template-loaded": "Plantilla cargada", "template-name-has-been-loaded-successfully": "La plantilla \"{name}\" se ha cargado con éxito.", "template-name-has-been-saved-successfully": "La plantilla \"{name}\" se ha guardado con éxito.", "template-saved": "Plantilla guardada", "there-was-an-error-loading-your-template": "Hubo un error cargando su plantilla.", "there-was-an-error-saving-your-template": "Hubo un error guardando su plantilla.", "this-is-a-paragraph-of-text-you-can-edit-this-text-to-add-your-own-content": "Este es un párrafo de texto. \nPuede editar este texto para agregar su propio contenido.", "viewTemplatesButton": "Ver plantillas", "your-email-header": "Tu encabezado de correo electrónico"}, "embed-code": "Código de incrustación", "empty": "Vacío", "empty-description": "Descripción vacía", "empty-prompt": "Aviso vacío", "end": "Fin", "engineering-team": "Equipo de ingeniería", "english": "🇺🇸 Inglés", "enhance-description": "Mejorar la descripción", "enhance-description-0": "Mejorar la descripción", "enhanced-description": "Descripción mejorada", "enhanced-description-0": "Descripción mejorada:", "enhanced-description-applied": "Descripción mejorada Aplicada!", "enhancement-failed": "La mejora falló", "enhancing": "Mejora ...", "enter-a-question": "Ingrese una pregunta ...", "enter-the-code-from-your-email": "Ingrese el código desde su correo electrónico", "enter-the-code-from-your-invitation": "Ingrese el código desde su invitación", "enter-your-email-address-and-well-send-you-a-password-reset-code": "Ingrese su dirección de correo electrónico y le enviaremos un código de restablecimiento de contraseña", "enter-your-email-address-and-well-send-you-a-password-reset-code-0": "Ingrese su dirección de correo electrónico y le enviaremos un código de restablecimiento de contraseña", "enter-your-email-and-access-code-to-start-your-interview": "Ingrese su correo electrónico y código de acceso para comenzar su entrevista", "enter-your-new-password-and-the-reset-code-we-sent-to-your-email": "Ingrese su nueva contraseña y el código de reinicio que enviamos a su correo electrónico", "enterprise": "Empresa", "enterprise-features": "Características empresariales", "enterprise-security": "Seguridad empresarial", "error": "Error", "error-adding-members": "Error a agregar miembros", "error-assigning-integration": "Error de asignación de integración:", "error-deleting-user": "Error de eliminación del usuario", "error-loading-data": "Error de carga de datos", "error-loading-integrations": "Error de carga de integraciones", "error-loading-knowledge-bases": "Error de carga de bases de conocimiento:", "error-loading-team-data": "Error de carga de datos del equipo", "error-loading-template": "Plantilla de carga de error", "error-loading-users": "Error de carga de usuarios", "error-removing-member": "Error de eliminación del miembro", "error-saving-template": "Plantilla de ahorro de errores", "error-updating-user-status": "Error de actualización del estado del usuario", "error-uploading-file": "Error de carga de archivo:", "error-verifying-account-status": "Error de verificación del estado de la cuenta", "error-while-fetching-the-templates": "Error mientras busca las plantillas", "every-integration-updatetime-days": "Cada {0} días", "everything-worked-perfectly-your-integration-successfully-brought-in-the-data": "Todo funcionó perfectamente. \nSu integración trajo con éxito los datos.", "example-scope-resolution": "Ejemplo: resolución de alcance", "excessive-use-of-permission-overrides-can-make-your-security-model-difficult-to-understand-and-audit-if-you-find-yourself-frequently-using-overrides-consider": "El uso excesivo de las anulaciones de permisos puede dificultar su modelo de seguridad de entender y auditar. \nSi se encuentra con frecuencia utilizando anulaciones, considere:", "experience-level": "Nivel de experiencia", "export": "Exportar", "export-data-with-project-scope": "Datos de exportación con alcance del proyecto", "export-form-as-document": "Formulario de exportación como documento", "export-form-configuration": "Configuración de formulario de exportación", "export-form-responses": "Respuestas de formulario de exportación", "export-options": "Opciones de exportación", "export-your-form-in-different-formats": "Exportar su forma en diferentes formatos", "exportCsvFutureUpdate": "La exportación a CSV se implementará en una actualización futura.", "exportPdfFutureUpdate": "La exportación a PDF se implementará en una actualización futura.", "extra-permissions": "Permisos adicionales", "extra-permissions-0": "Permisos adicionales", "failed": "Fallido", "failed-0": "Fallido", "failed-to-add-team-members": "No se pudo agregar a los miembros del equipo.", "failed-to-assign-integration": "No se pudo asignar integración", "failed-to-delete-chat-session": "No se pudo eliminar la sesión de chat", "failed-to-delete-the-template-please-try-again": "No se pudo eliminar la plantilla. \nPor favor intente de nuevo.", "failed-to-enhance-description-please-try-again": "No se pudo mejorar la descripción. \nPor favor intente de nuevo.", "integration-metrics": "Métricas de integración", "storage-usage": "Uso de almacenamiento", "storage-usage-title": "Uso de almacenamiento", "storage-usage-description": "Monitorea tu consumo de almacenamiento", "normal-usage": "Uso normal", "high-usage": "Uso alto", "critical-usage": "<PERSON><PERSON> c<PERSON>", "used": "Usado", "available": "Disponible", "total": "Total", "loading-storage": "Cargando uso de almacenamiento...", "failed-to-load-storage": "Error al cargar el uso de almacenamiento.", "storage-critical-message": "Crítico: El almacenamiento está casi lleno. Considera actualizar o limpiar las integraciones.", "storage-warning-message": "Advertencia: El uso de almacenamiento es alto. Considera revisar tus integraciones.", "gb": "GB", "percentage-used": "% usado", "failed-to-fetch-integration-metrics": "No logró obtener métricas de integración", "failed-to-fetch-integrations": "No logró obtener integraciones", "failed-to-initialize-checkout": "No se pudo inicializar el pago", "failed-to-load-data": "No se pudo cargar datos", "failed-to-load-plans": "No se pudo cargar planes", "failed-to-load-subscription-details": "No se pudo cargar detalles de suscripción", "failed-to-load-subscription-information": "No se pudo cargar información de suscripción", "failed-to-load-team-and-member-data": "No se pudo cargar datos de equipo y miembro.", "failed-to-load-team-integrations": "No se pudo cargar integraciones de equipo", "failed-to-remove-team-member": "No se pudo eliminar al miembro del equipo.", "failed-to-update-usage-tracking": "No se pudo actualizar el seguimiento de uso", "feb": "Feb", "file-storage": "Almacenamiento de archivos", "file-upload": "Carga de archivo", "file-uploaded": "Archivo cargado", "files": "Archivos", "filter": "Filtrar", "filter-by-name": "Filtrar por nombre ...", "filter-by-role": "Filtrar por rol", "filter-by-source": "Filtrar por fuente", "filter-by-status": "Filtrar por estado", "filter-by-team": "Filtrar por equipo", "filter-candidates": "<PERSON><PERSON><PERSON> candidatos", "filter-templates": "Plantillas de filtro", "financial-reports": "Informes financieros", "find-candidates-by-name-email-or-status": "Encuentre candidatos por nombre, correo electrónico o estado", "find-templates-by-role-type-or-level": "Encontrar plantillas por rol, tipo o nivel", "first-name": "Nombre de pila", "first-page": "Primera página", "font-size": "Tamaño de fuente", "font-weight": "Peso de la fuente", "footer": "Pie de página", "forgot-your-password": "¿Olvidaste tu contraseña?", "form-builder": "Formulario", "form-errors": "Errores de formulario:", "form-not-found": "Forma no encontrada", "form-submissions-will-be-sent-to-this-url": "Los envíos de formularios se enviarán a esta URL", "formSavedSuccess": "Forma guardada con éxito!", "forms": "Formularios", "francais": "🇫🇷 Français", "free": "<PERSON><PERSON><PERSON>", "free-0": "<PERSON><PERSON><PERSON>", "free-plan-subscription-limit-reached": "Límite de suscripción de plan gratuito alcanzado", "ftp-account-password-stored-securely": "Contraseña de cuenta FTP (almacenada de forma segura)", "ftp-account-username": "Nombre de usuario de la cuenta FTP", "ftp-integration": "Integración FTP", "ftp-integration-0": "Integración FTP", "ftp-not-encrypted": "FTP (no encriptado)", "ftp-server-address": "Dirección del servidor FTP", "ftp-server-port-usually-21": "Puerto del servidor FTP (generalmente 21)", "full-name": "Nombre completo", "full-width": "<PERSON><PERSON> completo", "generate-ai-prompt": "Generar un aviso de IA", "generate-form": "Generar forma", "generate-template": "Generar plantilla", "generate-the-interview": "Generar la entrevista", "generated-prompt": "Aviso generado", "generating": "Generando ...", "generating-form": "Forma generadora ...", "generation-failed": "La generación falló", "get-immediate-insights-on-your-performance": "Obtenga información inmediata sobre su rendimiento", "get-your-token-here": "Consigue tu token aquí", "global": "Global", "global-0": "Global", "global-scope": "Alcance global", "go-to-dashboard": "<PERSON><PERSON> al <PERSON>ro", "grant-global-permissions-to-teams-unnecessarily": "Otorgar permisos globales a los equipos innecesariamente", "grant-when-a-user-needs-a-specific-permission-that-isnt-included-in-their-roles-or-teams-but-doesnt-warrant-a-role-change": "Otorgue cuando un usuario necesita un permiso específico que no esté incluido en sus roles o equipos, pero no garantiza un cambio de roles.", "guidelines-for-implementing-effective-access-control": "Directrices para implementar un control de acceso efectivo", "has-errors": "Tiene errores:", "have-questions-about-which-plan-is-right-for-you-our-team-is-happy-to-help": "¿Tiene preguntas sobre qué plan es adecuado para usted? \nNuestro equipo está feliz de ayudar.", "having-trouble-contact-support-at": "¿Tiene problemas? \nContactar soporte en", "header": "Encabezamiento", "header-background-color": "Color de fondo de encabezado", "height": "Altura", "how-often-to-sync-data-minimum-1-day": "Con qué frecuencia sincronizar datos (mínimo 1 día)", "how-roles-provide-a-foundation-for-permissions-in-the-system": "Cómo los roles proporcionan una base para los permisos en el sistema", "how-teams-provide-contextual-access-to-resources": "Cómo los equipos proporcionan acceso contextual a recursos", "how-to-use-this-prompt": "Cómo usar este aviso:", "icon-color": "Color de icono", "icon-size": "Tamaño del icono", "icon-spacing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "identify-and-review-permission-overrides": "Identificar y revisar anulaciones de permiso", "if-a-user-has-the-view-analytics-permission-from": "Si un usuario tiene el permiso \"Ver Analytics\" de:", "image": "Imagen", "image-description": "Descripción de la imagen", "image-url": "URL de imagen", "implementing-a-more-granular-permission-model": "Implementación de un modelo de permiso más granular", "important-note": "Nota importante", "in-progress": "En curso", "inactive": "Inactivo", "include-specific-sections-you-want-in-the-email": "Incluya secciones específicas que desee en el correo electrónico", "include-your-company-or-brand-name": "Incluya su empresa o marca", "include-your-company-or-brand-name-for-customization": "Incluya su empresa o nombre de marca para la personalización.", "included-for-all-plans": "Incluido para todos los planes", "indicate-if-you-want-branding-elements-like-logos-and-theme-colors": "Indique si desea elementos de marca como logotipos y colores temáticos.", "individual-users-can-be-granted-extra-permissions-or-have-inherited-permissions-revoked-to-handle-exceptions": "A los usuarios individuales se les puede otorgar permisos adicionales o han heredado los permisos revocados para manejar las excepciones.", "information-about-this-interview": "Información sobre esta entrevista", "instant-feedback": "Comentarios instantáneos", "instant-feedback-0": "Comentarios instantáneos", "instead-of-assigning-individual-permissions-to-each-user-you-can-assign-roles-that-contain-predefined-sets-of-permissions-making-user-management-more-efficient": "En lugar de asignar permisos individuales a cada usuario, puede asignar roles que contengan conjuntos predefinidos de permisos, lo que hace que la gestión del usuario sea más eficiente.", "instructions": "Instrucciones", "integration": "Integración", "integration-assigned": "Integración asignada", "integration-has-been-successfully-assigned-to-currentteam-name": "La integración se ha asignado correctamente a {0}.", "integration-name": "Nombre de integración", "integration-s-total": "integración (s) total", "integration-status-guide": "Guía de estado de integración", "integrationPage": {"backToIntegrations": "Volver a las integraciones", "integrationDescription": "Ver la configuración y el estado de esta integración.", "integrationNameTitle": "{IntegrationName}", "integrationNotFoundDescription": "La integración solicitada no existe o ha sido eliminada.", "integrationNotFoundTitle": "Integración no encontrada", "loadingIntegration": "Integración de carga ...", "overviewTitle": "Descripción general de la integración", "permissionDeniedToastDescription": "No tiene permiso para ver las integraciones.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado"}, "integrations": "Integración", "interview-access": "Acceso a la entrevista", "interview-candidates": "Entrevistar a los candidatos", "interview-details": "Detalles de la entrevista", "interview-id-or-user-id-is-missing": "Falta la identificación de la entrevista o la ID de usuario.", "interview-not-found": "Entrevista no encontrada", "interview-questions": "Preguntas de entrevista", "interview-templates": "Plantillas de entrevista", "interview-type": "Tipo de entrevista", "interviewTemplatesPage": {"addCandidateButton": "Agregar candidato", "all-levels": "Todos los niveles", "all-types": "Todo tipo", "behavioral": "De comportamiento", "browseFilesButton": "Explorar archivos", "candidatesCount": "{count} candidatos", "completedCount": "{recuento} completado", "created": "Creado:", "createdBy": "Creado por:", "csvDropzoneText": "Arrastre y suelte un archivo CSV, o haga clic para navegar", "csvFormatHint": "El archivo CSV debe tener 2 columnas: \"Nombre completo\" y \"correo electrónico\"", "csvUploadTab": "Subida CSV", "deleteButton": "Bo<PERSON>r", "editButton": "<PERSON><PERSON>", "emailPlaceholder": "Correo electrónico", "errorDeletingTemplateToastDescription": "No se pudo eliminar la plantilla. \nPor favor intente de nuevo.", "errorDeletingTemplateToastTitle": "Error", "errorSendingCandidates": "Error al enviar candidatos:", "fileUploadedToastDescription": "Archivo CSV procesado correctamente. \nLas entrevistas se enviarán a los candidatos.", "fileUploadedToastTitle": "Archivo cargado", "filterTemplatesDescription": "Encontrar plantillas por rol, tipo o nivel", "filterTemplatesTitle": "Plantillas de filtro", "fullNamePlaceholder": "Nombre completo", "invitationsSentSuccessToast": "¡Invitaciones enviadas con éxito!", "junior": "<PERSON><PERSON><PERSON>", "lead": "<PERSON><PERSON><PERSON>", "level": "<PERSON><PERSON>", "loading-interviews": "Cargando plantillas de entrevista ...", "manualEntryTab": "Entrada manual", "mid-level": "De nivel medio", "missingFileToastDescription": "Sube un archivo CSV con direcciones de correo electrónico.", "missingFileToastTitle": "Falta de archivo", "missingInformationToastDescriptionEmails": "Ingrese al menos una dirección de correo electrónico.", "missingInformationToastTitle": "Información faltante", "missingTemplateToastDescription": "Seleccione una plantilla de entrevista.", "missingTemplateToastTitle": "Plantilla faltante", "mixed": "Mezclado", "moreQuestions": "{contar} más preguntas", "newTemplateButton": "Nueva plantilla", "noTemplatesFoundDescription": "Cree su primera plantilla de entrevista para comenzar.", "noTemplatesFoundTitle": "No se encontraron plantillas", "pageDescription": "Crear y administrar plantillas de entrevistas para enviar a los candidatos", "pageTitle": "Plantillas de entrevista", "permissionDeniedToastDescription": "No tiene permiso para acceder a la página de entrevistas.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "questionsLabel": "Preguntas: {count}", "search": "Buscar", "search-by-role-or-tech": "Buscar por rol o tecnología", "select-level": "<PERSON><PERSON> de se<PERSON>cci<PERSON>", "select-type": "Tipo de selección", "selectedFile": "Seleccionado: {nombre de archivo}", "sendButton": "Enviar", "sendInterviewDialogDescription": "Envíe la entrevista \"{rol} {nivel}\" a los candidatos.", "sendInterviewDialogTitle": "<PERSON><PERSON><PERSON> candida<PERSON>", "sendInvitationsButton": "Enviar invitaciones", "senior": "Sénior", "serverErrorToastDescription": "Algo salió mal mientras eliminaba la plantilla.", "serverErrorToastTitle": "Error del servidor", "system-design": "Diseño del sistema", "techStackLabel": "Pila tecnológica:", "technical": "Técnico", "templateDeletedToastDescription": "Se ha eliminado la plantilla \"rol} {nivel}\".", "templateDeletedToastTitle": "Plantilla eliminada", "type": "Tipo", "unexpectedError": "Se produjo un error inesperado.", "uploadAndSendButton": "Subir y enviar", "uploadCsvFileLabel": "Cargar el archivo CSV", "uploadFailedToastDescription": "Hubo un error procesando el archivo. \nPor favor intente de nuevo.", "uploadFailedToastTitle": "Carga falló", "viewButton": "Vista"}, "interviews": "Entrevistas", "invalid-email-or-access-code-please-check-your-credentials-and-try-again": "Correo electrónico o código de acceso no válido. \nConsulte sus credenciales y vuelva a intentarlo.", "invalid-email-or-access-code-please-check-your-credentials-and-try-again-0": "Correo electrónico o código de acceso no válido. \nConsulte sus credenciales y vuelva a intentarlo.", "invitations-sent-successfully": "¡Invitaciones enviadas con éxito!", "invite-candidates": "<PERSON><PERSON><PERSON> candida<PERSON>", "it-looks-like-no-messages-have-been-sent-yet": "Parece que todavía no se han enviado mensajes.", "jan": "Ene", "jane-smith": "fulana", "jira": "<PERSON><PERSON>", "jira-integration": "Integración de Jira", "john": "<PERSON>", "john-doe": "<PERSON>", "join-100-users": "Unirse a 100 usuarios", "jun": "Jun", "junior": "<PERSON><PERSON><PERSON>", "junior-0-2-years": "Junior (0-2 años)", "key": "De %", "key-0": "→", "knowledge-bases": "Bases de conocimiento", "knowledge-bases-0": "Bases de conocimiento", "last-name": "Apellido", "last-page": "Última página", "last-rerun": "Última repetición", "last-updated": "Última actualización:", "lead": "<PERSON><PERSON><PERSON>", "leave-blank-to-keep-current-password": "Deje en blanco para mantener la contraseña actual", "leave-blank-to-keep-current-token": "Deje en blanco para mantener el token actual", "leave-unused-roles-assigned-to-users": "Dejar roles no utilizados asignados a los usuarios", "left": "Iz<PERSON>erda", "level": "<PERSON><PERSON>", "lighter": "Encendedor", "limit-team-permissions-to-necessary-resources": "Limitar los permisos del equipo a los recursos necesarios", "line-height": "Altura de línea", "link-image": "<PERSON><PERSON> <PERSON>", "link-url": "URL de enlace", "linkedin-profile-optional": "Perfil de LinkedIn (opcional)", "list": "Lista", "list-items": "Lista de elementos", "list-type": "Tipo de lista", "listening": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loading": "Cargando", "loading-candidates": "Cargando candidatos ...", "loading-integration-metrics": "Cargando métricas de integración ...", "loading-integrations": "Cargando integraciones ...", "loading-interviews": "Cargando entrevistas ...", "loading-knowledge-bases": "Cargando bases de conocimiento ...", "loading-subscription-details": "Cargando detalles de suscripción ...", "loading-team": "Equipo de carga ...", "loading-team-details": "Cargando detalles del equipo ...", "loading-team-members": "Cargando miembros del equipo ...", "loading-template": "Plantilla de carga ...", "loading-users": "Cargando usuarios ...", "logo": "Logo", "logo-preview": "Vista previa del logotipo", "manage-and-connect-your-enterprise-data-from-a-variety-of-sources-all-in-one-place": "Administre y conecte sus datos empresariales desde una variedad de fuentes, todo en un solo lugar.", "manage-billing": "Administrar facturación", "manage-integrations": "Administrar integraciones", "manage-members": "Administrar <PERSON>", "manage-members-0": "Administrar <PERSON>", "manage-members-of-this-team": "Gestionar a los miembros de este equipo.", "manage-profile-with-self-scope": "Administrar el perfil con un alcance", "manage-your-organizations-users": "Administre los usuarios de su organización.", "manager-role": "<PERSON><PERSON> <PERSON> gerente", "manual-entry": "Entrada manual", "mar": "Mar", "margin": "Margen", "may": "<PERSON><PERSON><PERSON>", "member": "Miembro", "member-removed": "Miembro eliminado", "members": "Mi<PERSON><PERSON><PERSON>", "members-0": "Mi<PERSON><PERSON><PERSON>", "members-added": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "members-of-team": "Mi<PERSON>bros de {Equipname}", "mention-any-color-preferences": "Mencionar cualquier preferencia de color", "mention-any-specific-images-or-buttons-you-want": "Mencione cualquier imagen o botón específico que desee", "mention-if-you-need-file-uploads-e-g-resumes-images-documents": "Mencione si necesita cargas de archivos (por ejemplo, currículums, imágenes, documentos).", "mention-if-you-want-special-elements-like-social-icons": "Mencione si quieres elementos especiales como íconos sociales", "mention-required-fields-e-g-name-email-phone-number": "Mencione los campos requeridos (por ejemplo, nombre, correo electrónico, número de teléfono).", "mid-level": "De nivel medio", "mid-level-3-5-years": "<PERSON><PERSON> medio (3-5 años)", "missing-file": "Falta de archivo", "missing-information": "Información faltante", "missing-template": "Plantilla faltante", "mixed": "Mezclado", "monitor-your-improvement-over-time": "Monitoree su mejora con el tiempo", "monthly": "<PERSON><PERSON><PERSON>", "more-options": "Más opciones", "more-questions": "más preguntas", "multi-layered-permission-system": "Sistema de permiso de múltiples capas", "must-avoid": "¡Debe evitar!", "name": "Nombre", "name-must-be-at-least-2-characters": "El nombre debe ser al menos 2 caracteres.", "name-must-be-at-least-2-characters-0": "El nombre debe ser al menos 2 caracteres.", "needs-attention": "Necesita atención", "new-chat": "Nuevo chat", "new-integration": "Nueva integración", "new-password": "Nueva contraseña", "new-template": "Nueva plantilla", "newIntegrationPage": {"backToIntegrations": "Volver a las integraciones", "createIntegrationDescription": "Conecte sus fuentes de datos para automatizar la sincronización de datos.", "createIntegrationTitle": "Crear una nueva integración", "fillFormDescription": "Complete el siguiente formulario para configurar su integración.", "integrationDetailsTitle": "Detalles de integración"}, "next": "Próximo", "next-page": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "no": "No", "no-active-subscription": "Sin suscripción activa", "no-active-subscription-0": "Sin suscripción activa", "no-available-users-found": "No se encontraron usuarios disponibles.", "no-candidates-added": "No se agregaron candidatos.", "no-candidates-found": "No se encontraron candidatos", "no-components-added-yet": "No hay componentes agregados todavía", "no-configuration-available": "No hay configuración disponible.", "no-credit-card-required": "✓ No se requiere tarjeta de crédito", "no-description": "Sin descripción", "no-description-provided": "No se proporciona una descripción", "no-description-provided-0": "No se proporciona una descripción", "no-forms-yet": "Todavía no hay formas", "no-integrations-available": "No hay integraciones disponibles", "no-integrations-found": "No se encontraron integraciones", "no-integrations-found-0": "No se encontraron integraciones.", "no-logo-uploaded": "No hay logotipo cargado", "no-members-found": "No se encontraron miembros.", "no-messages-found": "No se encuentran mensajes", "no-permissions": "Sin permisos", "no-permissions-assigned": "No se asignan permisos", "no-questions-added-yet-add-some-questions-to-create-your-interview-template": "No hay preguntas agregadas todavía. \nAgregue algunas preguntas para crear su plantilla de entrevista.", "no-questions-available-for-this-interview": "No hay preguntas disponibles para esta entrevista", "no-team-members-found": "No se encontraron miembros del equipo", "no-teams-found": "No se encontraron equipos", "no-teams-found-0": "No se encontraron equipos.", "no-teams-match-your-search-criteria-or-no-teams-have-been-created-yet": "No hay equipos que coincidan con sus criterios de búsqueda o no se han creado equipos todavía.", "normal": "Normal", "not-updated": "No actualizado:", "not-updated-0": "No actualizado", "not-updated-1": "No actualizado", "numbered-list": "Lista numerada", "of-teams-have-members": "de los equipos tienen miembros", "open-menu": "<PERSON><PERSON> abierto", "open-menu-0": "<PERSON><PERSON> abierto", "or-select-components-from-the-sidebar": "o seleccione componentes de la barra lateral", "organize-teams-based-on-functional-areas": "Organizar equipos basados ​​en áreas funcionales", "our-security-system-uses-a-multi-layered-approach-to-permissions-allowing-for-fine-grained-access-control-while-maintaining-flexibility-and-ease-of-management": "Nuestro sistema de seguridad utiliza un enfoque de múltiples capas para los permisos, lo que permite el control de acceso de grano fino mientras mantiene flexibilidad y facilidad de gestión.", "overview": "Descripción general", "overview-0": "Descripción general", "padding": "<PERSON><PERSON><PERSON>", "password": "Contraseña", "password-is-required": "Se requiere contraseña.", "paste-it-to-chatgpt-claude-or-your-preferred-ai-model": "<PERSON><PERSON><PERSON><PERSON> para chatgpt, <PERSON> o su modelo de IA preferido", "payment-successful": "¡Pago exitoso!", "pending": "Pendiente", "permission-applies-across-the-entire-system-to-all-resources-of-the-relevant-type": "El permiso se aplica en todo el sistema a todos los recursos del tipo relevante.", "permission-applies-only-to-resources-created-by-or-directly-assigned-to-the-user": "El permiso se aplica solo a los recursos creados o directamente asignados al usuario.", "permission-applies-only-to-resources-owned-by-or-associated-with-the-users-team-s": "El permiso se aplica solo a los recursos propiedad o asociados con el equipo del usuario.", "permission-applies-only-to-specific-projects-regardless-of-team-ownership": "El permiso se aplica solo a proyectos específicos, independientemente de la propiedad del equipo.", "permission-denied": "<PERSON><PERSON><PERSON> den<PERSON>ado", "permission-denied-0": "<PERSON><PERSON><PERSON> den<PERSON>ado", "permission-denied-1": "<PERSON><PERSON><PERSON> den<PERSON>ado", "permission-inheritance-flow": "Flujo de herencia de permiso", "permission-model-overview": "Descripción general del modelo de permiso", "permission-overrides": "Anulaciones de permiso", "permission-resolution": "Resolución de permiso", "permission-scopes": "Alcance de permiso", "permission-scopes-define-the-boundaries-within-which-a-permission-applies-they-allow-for-fine-grained-control-over-what-resources-a-user-can-access-with-a-given-permission": "Los ámbitos de permiso definen los límites dentro de los cuales se aplica un permiso. \nPermiten un control de grano fino sobre qué recursos puede acceder un usuario con un permiso determinado.", "permissions": "<PERSON><PERSON><PERSON>", "permissions-apply-only-to-resources-owned-by-the-team-this-is-the-most-common-scope-for-team-permissions": "Los permisos se aplican solo a los recursos propiedad del equipo. \nEste es el alcance más común para los permisos de equipo.", "permissions-assigned": "<PERSON><PERSON><PERSON> as<PERSON>", "permissions-assigned-to-this-team-are-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users": "Los permisos asignados a este equipo se otorgan a todos los miembros del equipo, a menos que se revocen explícitamente para usuarios específicos.", "permissions-assigned-to-this-team-will-be-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users": "Los permisos asignados a este equipo se otorgarán a todos los miembros del equipo, a menos que se revocen explícitamente para usuarios específicos.", "permissions-granted-to-all-members-of-the-team-typically-scoped-to-team-resources": "Permisos otorgados a todos los miembros del equipo, típicamente alcanzados a los recursos del equipo", "permissions-may-be-limited-to-specific-projects-that-the-team-is-working-on-even-if-those-projects-are-shared-with-other-teams": "Los permisos pueden limitarse a proyectos específicos en los que el equipo está trabajando, incluso si esos proyectos se comparten con otros equipos.", "personal-info": "Información personal", "pick-a-color": "Elige un color", "plan-features": "Características del plan", "platform": "Plataforma", "please-complete-onboarding-first": "Complete primero la incorporación", "please-describe-your-form-purpose": "Describe tu propósito de forma", "please-enter-a-description-of-the-email-you-want-to-create": "Ingrese una descripción del correo electrónico que desea crear.", "please-enter-a-description-to-enhance": "Ingrese una descripción para mejorar.", "please-enter-a-form-description-first": "Ingrese primero una descripción del formulario", "please-enter-a-prompt-to-generate-a-template": "Ingrese un mensaje para generar una plantilla.", "please-enter-a-valid-email-address": "Por favor, introduce una dirección de correo electrónico válida.", "please-enter-a-valid-url": "Ingrese una URL válida.", "please-enter-both-email-and-access-code": "Ingrese tanto el correo electrónico como el código de acceso", "please-fill-in-all-required-fields-before-saving": "Complete todos los campos requeridos antes de ahorrar.", "please-fill-in-the-following-fields": "Complete los siguientes campos:", "please-select-an-interview-template": "Seleccione una plantilla de entrevista.", "please-try-refreshing-the-page": "Por favor intente actualizar la página", "please-upload-a-csv-file-with-email-addresses": "Sube un archivo CSV con direcciones de correo electrónico.", "port": "Puerto", "port-is-required": "Se requiere puerto.", "practice-your-interview-skills-with-our-ai-interviewer-get-real-time-feedback-and-improve-your-chances-of-landing-your-dream-job": "Agilice su proceso de contratación. \nAutomatice la detección de candidatos e identifique rápidamente el ajuste perfecto más rápido, más fácil y con una mejor precisión.", "preferences": "Preferencias", "preview": "Avance", "preview-and-send": "Avance", "preview-how-your-email-will-look-to-recipients": "Vista previa de cómo se verá su correo electrónico a los destinatarios", "previewPage": {"editTemplateButton": "Plantilla de edición", "editToGenerateHtml": "Editar para generar html", "errorTitle": "Error", "failedToLoadTemplate": "No se pudo cargar la plantilla. \nVuelva a intentarlo más tarde.", "goBackToTemplates": "Volver a las plantillas", "loadingEditor": "Editor de carga ...", "noHtmlPreview": "No hay una vista previa HTML disponible para esta plantilla.", "templateNotFoundDescription": "La plantilla que está buscando no existe o ha sido eliminada.", "templateNotFoundTitle": "Plantilla no encontrada"}, "previewTab": "Avance", "previous": "Anterior", "previous-30-days": "30 días anteriores", "previous-7-days": "7 días anteriores", "previous-page": "Página anterior", "primary-button": "Botón principal", "primary-color": "Color primario", "principle-of-least-privilege": "Principio de menor privilegio", "priority-24-7-support": "Apoyo prioritario las 24 horas, los 7 días de la semana,", "product-backlog": "Cartera de productos", "professional": "Profesional", "project": "Proyecto", "project-is-required": "Se requiere proyecto.", "project-key": "Llave de proyecto", "project-key-0": "llave de proyecto", "project-scope": "Alcance del proyecto", "prompt": "Inmediato", "prompt-copied": "¡Aviso copiado!", "prompt-tips": "Consejos rápidos:", "public-form-url": "URL de forma pública", "questions": "Preguntas", "quick-actions": "Acciones rápidas", "radio-group": "Grupo de radio", "real-time-analysis": "Análisis en tiempo real", "realistic-experience": "Experiencia realista", "recent": "Reciente", "recent-activity": "Actividad reciente", "redirecting-to-checkout": "Redirigir para pagar ...", "redirecting-you-to-your-interview": "Redirigirte a tu entrevista ...", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "refreshing": "Refrescante", "regenerate": "Regenerado", "regular-security-audits": "Auditorías de seguridad regulares", "regularly-review-and-audit-role-assignments": "Revisar y auditorear regularmente las tareas de roles", "regularly-review-team-memberships": "Revise regularmente las membresías del equipo", "remove": "Eliminar", "remove-column": "Eliminar la columna", "remove-from-team": "Eliminar del equipo", "remove-permissions-for-inactive-users": "Eliminar permisos para usuarios inactivos", "remove-row": "Eliminar la fila", "remove-team-member": "Eliminar miembro del equipo", "remove-unnecessary-roles-when-job-functions-change": "Eliminar roles innecesarios cuando cambien las funciones del trabajo", "removes-any-permissions-explicitly-revoked-for-the-user": "Elimina cualquier permiso revocado explícitamente para el usuario", "reset-code": "Código de reinicio", "reset-password": "Restablecer contraseña", "resource-scope": "Alcance de recursos", "responses": "Respuestas", "retry": "<PERSON>er", "retry-integration": "Reintento de integración", "retrying": "Volver a intentar ...", "return-to-dashboard": "Volver al tablero", "return-to-interviews": "Volver a las entrevistas", "return-to-templates": "Volver a las plantillas", "review-user-role-assignments-for-appropriateness": "Revise las tareas de rol de usuario para la idoneidad", "reviewing-and-adjusting-your-role-definitions": "Rev<PERSON>r y ajustar sus definiciones de roles", "revoked-permissions": "Permisos revocados", "revoked-permissions-0": "Permisos revocados", "right": "Bien", "robert-johnson": "<PERSON>", "role": "Role", "role-0": "Role", "role-assignment": "Asignación de roles", "role-based-access-control": "Control de acceso basado en roles", "role-permissions": "Per<PERSON><PERSON> de <PERSON>", "role-structure": "Estructura de papel", "roles": "Roles", "roles-and-permissions": "Roles", "roles-are-defined-at-the-system-level-and-cannot-be-created-or-modified-by-regular-users-contact-your-system-administrator-if-you-need-a-new-role-or-modifications-to-existing-roles": "Los roles se definen a nivel del sistema y no pueden ser creados o modificados por usuarios regulares. \nPóngase en contacto con su administrador del sistema si necesita un nuevo rol o modificaciones en los roles existentes.", "roles-are-predefined-sets-of-permissions-that-represent-common-job-functions-or-responsibility-levels-within-your-organization-each-user-can-be-assigned-one-or-more-roles": "Los roles son conjuntos predefinidos de permisos que representan funciones de trabajo comunes o niveles de responsabilidad dentro de su organización. \nA cada usuario se le puede asignar uno o más roles.", "roles-ensure-that-users-with-similar-responsibilities-have-consistent-access-rights-reducing-the-risk-of-permission-inconsistencies": "Los roles as<PERSON>uran que los usuarios con responsabilidades similares tengan derechos de acceso consistentes, reduciendo el riesgo de inconsistencias de permiso.", "roles-provide-a-clear-structure-for-access-rights-making-it-easier-to-audit-who-has-access-to-what-and-why-they-have-that-access": "Los roles proporcionan una estructura clara para los derechos de acceso, lo que facilita la auditoría de quién tiene acceso a qué y por qué tienen ese acceso.", "rows-per-page": "<PERSON>las por página:", "save-20": "(Ahorrar 20%)", "save-changes": "Guardar cambios", "save-integration-settings": "Guardar la configuración de integración", "save-template": "Guardar plantilla", "saveButton": "<PERSON><PERSON><PERSON>", "scalability": "Escalabilidad", "scope-examples": "Ejemplos de alcance", "scope-hierarchy-broadest-to-narrowest": "Jerarq<PERSON><PERSON> de alcance (más estrecha a más estrecha)", "scope-resolution": "Resolución de alcance", "scope-types": "Tipos de alcance", "scopes": "Escopas", "score": "<PERSON><PERSON><PERSON>", "search": "Buscar", "search-0": "Buscar", "search-by-name-or-email": "Buscar por nombre o correo electrónico ...", "search-by-role-or-tech": "Buscar por rol o tecnología ...", "search-members": "Miembros de búsqueda ...", "search-settings": "Configuración de búsqueda", "search-users": "Buscar usuarios ...", "secondary-button": "Botón secundario", "secure-connection": "Conexión segura", "security-best-practices": "Las mejores prácticas de seguridad", "security-system": "Sistema de seguridad", "select-alignment": "Seleccionar alineación", "select-connection-type": "Seleccionar el tipo de conexión", "select-experience-level": "Seleccionar nivel de experiencia", "select-level": "<PERSON><PERSON> de se<PERSON>cci<PERSON>", "select-list-type": "Seleccionar tipo de lista", "select-role": "Rol de selección", "select-status": "Seleccionar estado", "select-style": "<PERSON><PERSON><PERSON>", "select-team": "<PERSON><PERSON><PERSON>", "select-type": "Tipo de selección", "select-users-to-add-to-the": "Seleccione los usuarios para agregar al", "select-weight": "Seleccionar peso", "select-which-knowledge-bases-to-search-when-performing-searches": "Seleccione qué bases de conocimiento buscar al realizar búsquedas", "select-width": "<PERSON><PERSON><PERSON><PERSON><PERSON> ancho", "send": "Enviar", "send-invitations": "Enviar invitaciones", "send-reset-code": "Enviar código de reinicio", "send-reset-code-0": "Enviar código de reinicio", "sendEmailDialog": {"cancelButton": "<PERSON><PERSON><PERSON>", "description": "Envíe su plantilla de correo electrónico a un destinatario. \nComplete los detalles a continuación.", "emailContentError": "El contenido de correo electrónico no se pudo generar", "fromEmailLabel": "Desde el correo electrónico", "fromEmailPlaceholder": "<EMAIL>", "fromNameLabel": "Desde el nombre", "fromNamePlaceholder": "Su nombre o empresa o correo electrónico", "htmlGenerationError": "Error de generación de correo electrónico HTML. \nPor favor intente de nuevo.", "recipientSubjectRequiredError": "Se requieren correo electrónico y asunto del destinatario", "sendButton": "Enviar correo electrónico", "sendFailToastDescription": "Hubo un error al enviar su correo electrónico. \nPor favor intente de nuevo.", "sendFailToastTitle": "No se pudo enviar correo electrónico", "sendSuccessToastDescription": "El correo electrónico se ha enviado a {DestinatorEmail}", "sendSuccessToastTitle": "Correo electrónico enviado correctamente", "sendingButton": "Envío...", "subjectLabel": "Sujeto", "subjectPlaceholder": "Asunto de correo electrónico", "title": "Enviar correo electrónico", "toLabel": "A", "toPlaceholder": "<EMAIL>"}, "senior": "Sénior", "senior-5-years": "Senior (5 años)", "server": "<PERSON><PERSON><PERSON>", "server-0": "Servidor:", "server-error": "Error del servidor", "server-ip": "IP del servidor", "server-ip-is-required": "Se requiere IP del servidor.", "settings": "<PERSON><PERSON><PERSON><PERSON>", "settingsTab": "<PERSON><PERSON><PERSON><PERSON>", "sftp-secure": "SFTP (seguro)", "share": "Compartir", "share-your-form": "Comparte tu forma", "share-your-form-with-others-to-collect-responses": "Comparta su forma con otros para recopilar respuestas", "shared-with-me": "Compartido conmigo", "showing": "Demostración", "simplified-management": "Gestión simplificada", "simulates-real-interview-scenarios": "Simula escenarios de entrevista reales", "social-icons": "Íconos sociales", "social-media-links": "Enlaces de redes sociales", "software-engineer": "Ingeniero de software", "solid": "<PERSON><PERSON><PERSON><PERSON>", "some-team-permissions-may-have-global-scope-allowing-team-members-to-perform-actions-across-the-entire-system": "Algunos permisos de equipo pueden tener un alcance global, lo que permite a los miembros del equipo realizar acciones en todo el sistema.", "something-went-wrong": "Algo salió mal.", "something-went-wrong-0": "¡Algo salió mal!", "something-went-wrong-please-try-again": "Algo salió mal. \nPor favor intente de nuevo.", "something-went-wrong-the-integration-couldnt-fetch-the-data-properly": "Algo salió mal. \nLa integración no pudo obtener los datos correctamente.", "something-went-wrong-while-deleting-the-template": "Algo salió mal mientras eliminaba la plantilla.", "source-type": "Tipo de fuente", "specify-form-actions-e-g-email-responses-save-to-database-integrate-with-apis": "Especifique las acciones de formulario (por ejemplo, respuestas de correo electrónico, guarda en la base de datos, integren con API).", "specify-if-you-need-auto-confirmation-emails-or-notifications": "Especifique si necesita correos electrónicos o notificaciones de confirmación automática.", "specify-the-purpose-of-the-form-job-application-event-registration-feedback-etc": "Especifique el propósito del formulario (solicitud de empleo, registro de eventos, comentarios, etc.).", "specify-the-type-of-email-welcome-newsletter-promotion-etc": "Especifique el tipo de correo electrónico (bienvenido, boletín, promoción, etc.)", "standardization": "Normalización", "start-free-trial": "Iniciar prueba gratis", "start-growing-your-business-quickly": "Empiece a hacer crecer su negocio rápidamente", "status": "Estado", "status-0": "Estado", "status-1": "Estado", "subscribe-now": "Sus<PERSON>r<PERSON><PERSON><PERSON> ahora", "successfully-processed": "Procesado con éxito", "support": "Apoyo", "switch-language": "Lenguaje de cambio", "syncing": "Sincronización", "system-design": "Diseño del sistema", "table": "Mesa", "table-headers": "Cabezales de mesa", "table-rows": "Hileras de mesa", "tailored-pricing-for-your-specific-needs": "Precios a medida para sus necesidades específicas", "tailored-to-your-needs": "Personalizado a sus necesidades", "team": "Equipo", "team-0": "Equipo", "team-1": "Equipo", "team-activity-rate": "Tasa de actividad del equipo", "team-activity-tracking-will-be-available-in-a-future-update": "El seguimiento de actividades del equipo estará disponible en una actualización futura.", "team-based-permissions": "Permisos basados ​​en el equipo", "team-description-is-required": "Se requiere una descripción del equipo.", "team-details": "Detalles del equipo", "team-details-0": "Detalles del equipo", "team-information": "Información del equipo", "team-information-and-statistics": "Información y estadísticas del equipo", "team-integrations": "Integraciones de equipo", "team-management": "Gestión de equipo", "team-members": "Miembros del equipo", "team-members-0": "Miembros del equipo:", "team-members-1": "miembros del equipo", "team-name": "Nombre del equipo", "team-name-is-required": "Se requiere el nombre del equipo.", "team-not-found": "Equipo no encontrado", "team-permission-scopes": "Alcances de permiso de equipo", "team-permissions": "Permisos de equipo", "team-permissions-0": "Permisos de equipo", "team-permissions-1": "Permisos de equipo:", "team-permissions-2": "Permisos de equipo", "team-permissions-3": "Permisos de equipo", "team-permissions-are-typically-scoped-to-the-teams-resources-but-they-can-also-have-different-scope-types": "Los permisos del equipo suelen alcanzar los recursos del equipo, pero también pueden tener diferentes tipos de alcance:", "team-scope": "Alcance del equipo", "team-status": "Estatus de equipo", "team-structure": "Estructura de equipo", "team-updated": "Equipo actual<PERSON>", "team-vs-role-permissions": "<PERSON><PERSON><PERSON> de equipo vs.", "teams": "Equipos", "teams-and-permissions": "Equipos y permisos", "teams-or-groups-allow-you-to-organize-users-and-grant-permissions-based-on-the-resources-they-need-to-access-teams-are-particularly-useful-for-departmental-or-project-based-access-control": "Los equipos (o grupos) le permiten organizar a los usuarios y otorgar permisos en función de los recursos a los que necesitan acceder. \nLos equipos son particularmente útiles para el control de acceso departamental o basado en proyectos.", "tech-stack": "Pila tecnológica:", "tech-stack-0": "Pila de tecnología", "technical": "Técnico", "tell-us-about-yourself": "Cuéntanos sobre ti", "tell-us-more-about-yourself-to-get-started": "Cuéntanos más sobre ti para empezar", "tell-us-what-kind-of-form-you-need-and-our-ai-will-generate-it-for-you": "Cuéntanos qué tipo de forma necesitas, y nuestra IA lo generará para ti", "template-deleted": "Plantilla eliminada", "template-generated": "Plantilla generada!", "template-loaded": "Plantilla cargada", "template-name-has-been-loaded-successfully": "La plantilla \"{0}\" se ha cargado con éxito.", "template-name-has-been-saved-successfully": "La plantilla \"{0}\" se ha guardado con éxito.", "template-not-found": "Plantilla no encontrada", "template-saved": "Plantilla guardada", "template-updated": "Plantilla actualizada", "templatesPage": {"cancelButton": "<PERSON><PERSON><PERSON>", "componentsCount": "{count} componentes", "createFirstTemplateButton": "Crea tu primera plantilla", "createNewTemplateButton": "Crear nueva plantilla", "deleteAlertDialogDescription": "Esta acción no se puede deshacer. \nEsto eliminará permanentemente la plantilla y la eliminará de nuestros servidores.", "deleteAlertDialogTitle": "¿E<PERSON>á seguro?", "deleteButton": "Bo<PERSON>r", "deleteErrorToastDescription": "No se pudo eliminar la plantilla. \nPor favor intente de nuevo.", "deleteMenuItem": "Bo<PERSON>r", "duplicateMenuItem": "Dup<PERSON><PERSON>", "editMenuItem": "<PERSON><PERSON>", "editTooltip": "Editar esta plantilla", "emailTemplatesTitle": "Plantillas de correo electrónico", "errorToastTitle": "Error", "failedToLoadTemplates": "No se pudo cargar plantillas. \nVuelva a intentarlo más tarde.", "noDescription": "Sin descripción", "noTemplatesFoundDescription": "Todavía no has creado ninguna plantilla de correo electrónico.", "noTemplatesFoundTitle": "No se encontraron plantillas", "permissionDeniedToastDescription": "No tiene permiso para acceder a la página del creador de correo electrónico.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> den<PERSON>ado", "previewMenuItem": "Avance", "previewTooltip": "Vista previa de esta plantilla", "templateDeletedToastDescription": "La plantilla se ha eliminado con éxito.", "templateDeletedToastTitle": "Plantilla eliminada"}, "text-area": "Área de texto", "text-block": "Bloque de texto", "text-color": "Color de texto", "text-field": "Campo de texto", "the-ai-will-generate-a-json-template-based-on-your-description": "La IA generará una plantilla JSON basada en su descripción", "the-effective-scope-will-be-global-as-its-broader-than-team": "El alcance efectivo será global, ya que es más amplio que el equipo.", "the-form-youre-looking-for-doesnt-exist-or-has-been-deleted": "La forma que buscas no existe o ha sido eliminada.", "the-integration-already-worked-before-and-now-its-trying-to-update-its-data-again": "La integración ya funcionó antes, y ahora está tratando de actualizar sus datos nuevamente.", "the-leadership-team-with-global-scope": "El equipo de \"liderazgo\" con alcance global", "the-same-user-in-the-marketing-team-can-manage-marketing-resources": "El mismo usuario en el equipo de \"marketing\" puede administrar los recursos de marketing", "the-specific-resources-projects-data-etc-that-the-team-has-access-to": "Los recursos específicos (proyectos, datos, etc.) a los que el equipo tiene acceso", "the-system-tried-to-refresh-the-data-but-it-didnt-work-this-time-it-stayed-as-it-was-before": "El sistema intentó actualizar los datos, pero esta vez no funcionó. \nSe quedó como era antes.", "the-team-already-exists": "El equipo ya existe", "the-web-url-to-fetch-data-from": "La URL web para obtener datos de", "their-manager-role-with-team-scope": "Su papel de \"gerente\" con el alcance del equipo", "there-are-no-integrations-to-assign-at-this-time": "No hay integraciones para asignar en este momento", "there-was-an-error-enhancing-your-description-please-try-again": "Hubo un error mejorando su descripción. \nPor favor intente de nuevo.", "there-was-an-error-generating-your-template-please-try-again": "Hubo un error generando su plantilla. \nPor favor intente de nuevo.", "there-was-an-error-loading-the-team-data": "Hubo un error cargando los datos del equipo.", "there-was-an-error-loading-your-template": "Hubo un error cargando su plantilla.", "there-was-an-error-processing-the-file-please-try-again": "Hubo un error procesando el archivo. \nPor favor intente de nuevo.", "there-was-an-error-saving-your-template": "Hubo un error guardando su plantilla.", "there-was-an-error-updating-the-team-information": "Hubo un error actualizar la información del equipo.", "this-action-cannot-be-undone": "Esta acción no se puede deshacer.", "this-action-cannot-be-undone-0": "? \nEsta acción no se puede deshacer.", "this-is-a-paragraph-of-text-you-can-edit-this-text-to-add-your-own-content": "Este es un párrafo de texto. \nPuede editar este texto para agregar su propio contenido.", "this-is-the-unique": "Este es el único", "this-means-your-integration-just-started-collecting-information-its-the-first-step-after-setting-it-up": "Esto significa que su integración acaba de comenzar a recopilar información. \nEs el primer paso después de configurarlo.", "this-team-doesnt-have-any-integrations-assigned-yet": "Este equipo aún no tiene ninguna integración asignada", "this-team-doesnt-have-any-permissions-assigned-yet": "Este equipo aún no tiene permisos asignados.", "this-team-has-no-members-or-none-match-your-search": "Este equipo no tiene miembros o ninguno coincide con su búsqueda", "this-will-give-all-team-members-access-to-this-integration": "? \nEsto dará a todos los miembros del equipo acceso a esta integración.", "today": "Hoy", "toolbar": {"cancelButton": "<PERSON><PERSON><PERSON>", "copyHtmlButton": "Copiar html", "designMode": "Modo de <PERSON>", "desktopView": "Vista de escritorio", "htmlCode": "Código HTML", "htmlCopiedToastDescription": "El código HTML se ha copiado en su portapapeles.", "htmlCopiedToastTitle": "Html copiado", "importButton": "Importar", "importJsonDialogTitle": "Plantilla de importación JSON", "importJsonTemplateButton": "Plantilla de importación JSON", "invalidJsonToastDescription": "Proporcione una matriz de componentes JSON válida.", "invalidJsonToastTitle": "JSON no válido", "loadButton": "Carga", "loadTemplateButton": "Plantilla de carga", "loadTemplateDialogTitle": "Plantilla de carga", "mobileView": "Vista móvil", "pasteJsonDescription": "Pegue la matriz JSON generada por un modelo AI basado en su aviso.", "pasteJsonLabel": "Pegar la plantilla JSON", "pasteJsonPlaceholder": "[{\"id\": \"1\", \"tipo\": \"encabezado\", \"contenido\": \"bienvenido\", \"configuración\": {...}}]", "previewMode": "Modo de vista previa", "redoButton": "<PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON><PERSON>", "saveTemplateButton": "Guardar plantilla", "saveTemplateDialogTitle": "Guardar plantilla", "selectTemplateLabel": "Seleccionar plantilla", "selectTemplatePlaceholder": "Seleccione una plantilla", "tabletView": "Vista de tableta", "templateImportedToastDescription": "La plantilla JSON se ha importado con éxito.", "templateImportedToastTitle": "Plantilla importada", "templateNameLabel": "Nombre de plantilla", "templateNamePlaceholder": "Mi plantilla de correo electrónico", "undoButton": "<PERSON><PERSON><PERSON>", "viewAllTemplatesButton": "Ver todas las plantillas"}, "total-integrations": "Integraciones totales", "total-teams": "Total de equipos", "total-users": "Usuarios totales", "track-progress": "Progreso de la pista", "transcript": "Transcripción", "trash": "<PERSON><PERSON><PERSON>", "try-again": "Intentar otra vez", "try-again-0": "Intentar otra vez", "two-factor-authentication-is-required-but-this-ui-does-not-handle-that": "Se requiere autenticación de dos factores, pero esta interfaz de usuario no maneja eso", "type": "Tipo", "understand-what-each-status-means-in-simple-words": "Comprenda lo que significa cada estado en palabras simples.", "understanding-how-permission-scopes-limit-the-reach-of-permissions": "Comprender cómo los ámbitos de permiso limitan el alcance de los permisos", "understanding-how-permissions-work-in-our-enterprise-system": "Comprender cómo funcionan los permisos en nuestro sistema empresarial", "understanding-the-permission-model-and-access-control": "Comprender el modelo de permiso y el control de acceso.", "unit": "Unidad", "unit-0": "Unidad", "unknown": "Desconocido", "update-failed": "La actualización falló", "update-failed-0": "La actualización falló", "update-frequency": "Actualización de frecuencia", "update-frequency-days": "Frecuencia de actualización (días)", "update-frequency-days-0": "Frecuencia de actualización (días)", "update-team-information-and-permissions": "Actualizar información del equipo y permisos.", "update-time": "Tiempo de actualización:", "update-time-must-be-at-least-1-day": "El tiempo de actualización debe ser al menos 1 día.", "update-time-must-be-at-least-1-day-0": "El tiempo de actualización debe ser al menos 1 día.", "updating": "Actualización", "upload-and-send": "Subir y enviar", "upload-csv-file": "Cargar el archivo CSV", "upload-failed": "Carga falló", "upload-failed-0": "Carga falló", "upload-logo": "Subir logotipo", "upload-your-company-logo-to-display-on-the-form": "Cargue el logotipo de su empresa para mostrar en el formulario", "url-copied-to-clipboard": "¡URL copiada en el portapapeles!", "use-permission-overrides-sparingly-and-only-when-necessary-they-should-be-the-exception-not-the-rule": "Use anulaciones de permiso con moderación y solo cuando sea necesario. \nDeben ser la excepción, no la regla.", "use-team-scoped-permissions-when-possible": "Use permisos con escolción del equipo cuando sea posible", "use-template": "Usar plantilla", "use-the-import-json-feature-in-our-email-builder-to-load-your-template": "Use la función \"import json\" en nuestro constructor de correo electrónico para cargar su plantilla", "use-the-most-restrictive-role-that-meets-the-users-needs": "Utilice el papel más restrictivo que satisfaga las necesidades del usuario", "use-this": "Usa esto", "use-this-description": "Usa esta descripción", "use-when-a-user-has-a-role-or-team-membership-that-grants-permissions-they-shouldnt-have-but-they-still-need-the-role-or-team-for-other-permissions": "Use cuando un usuario tiene una membresía de rol o equipo que otorga permisos que no deberían tener, pero aún así necesitan el papel o el equipo para otros permisos.", "user-can-create-data-providers-only-for-their-team-s": "El usuario puede crear proveedores de datos solo para su (s) equipo (s).", "user-can-export-data-only-from-specific-projects-they-have-access-to": "El usuario puede exportar datos solo desde proyectos específicos a los que tiene acceso.", "user-can-only-manage-their-own-profile-information": "El usuario solo puede administrar su propia información de perfil.", "user-can-view-analytics-for-all-teams-and-projects-in-the-system": "El usuario puede ver el análisis para todos los equipos y proyectos en el sistema.", "user-deleted": "<PERSON><PERSON>rio eliminado", "user-growth": "Crecimiento de los usuarios", "user-not-found": "Usuario no encontrado.", "user-overrides": "Anulaciones de usuarios", "username": "Nombre de usuario", "username-is-required": "Se requiere nombre de usuario.", "users": "Usuarios", "users-0": "Usuarios", "users-are-assigned-roles-that-grant-a-set-of-permissions-roles-provide-a-baseline-of-access-appropriate-for-different-job-functions": "A los usuarios se les asigna roles que otorgan un conjunto de permisos. \nLos roles proporcionan una línea de base de acceso apropiado para diferentes funciones de trabajo.", "users-belong-to-teams-that-grant-additional-permissions-team-permissions-are-typically-scoped-to-the-teams-resources": "Los usuarios pertenecen a equipos que otorgan permisos adicionales. \nLos permisos del equipo suelen alcanzar los recursos del equipo.", "users-per-team": "Usuarios por equipo", "users-who-belong-to-the-team-and-inherit-its-permissions": "Usuarios que pertenecen al equipo y heredan sus permisos", "verify-team-memberships-and-permissions": "Verificar membresías y permisos del equipo", "verifying-account-status": "Verificación del estado de la cuenta ...", "view": "Vista", "view-analytics": "<PERSON><PERSON>", "view-analytics-0": "<PERSON><PERSON>", "view-analytics-with-global-scope": "Ver análisis con alcance global", "view-details": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "view-details-0": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "view-filter-and-manage-your-data-integrations": "<PERSON>er, filtrar y administrar sus integraciones de datos", "view-integrations": "Ver integraciones", "view-interviews-templates": "Ver plantillas de entrevistas", "view-team-members": "Ver miembros del equipo", "view-your-forms-dashboard": "Vea el tablero de sus formularios", "viewButton": "Vista", "visit-website": "Visitar el sitio web", "we-couldnt-find-the-interview-youre-looking-for-it-may-have-been-deleted-or-doesnt-exist": "No pudimos encontrar la entrevista que estás buscando. \nPuede haber sido eliminado o no existe.", "we-couldnt-find-the-template-youre-looking-for-it-may-have-been-deleted-or-doesnt-exist": "No pudimos encontrar la plantilla que estás buscando. \nPuede haber sido eliminado o no existe.", "web": "Web", "web-integration": "Integración web", "web-integration-0": "Integración web", "webhook-url": "URL webhook", "welcome-to": "Bienvenido", "what-are-permission-scopes": "¿Qué son los ámbitos de permiso?", "what-are-roles": "¿Qué son los roles?", "when-a-user-has-the-same-permission-with-different-scopes-e-g-from-different-roles-or-teams-the-system-uses-the-broadest-scope": "Cuando un usuario tiene el mismo permiso con diferentes ámbitos (por ejemplo, de diferentes roles o equipos), el sistema utiliza el alcance más amplio:", "when-determining-if-a-user-has-a-specific-permission-the-system": "Al determinar si un usuario tiene un permiso específico, el sistema:", "when-to-use-permission-overrides": "Cuándo usar anulaciones de permiso", "while-roles-define-what-a-user-can-do-based-on-their-job-function-teams-define-what-resources-they-can-access-this-combination-provides-a-flexible-and-powerful-access-control-system": "Si bien los roles definen lo que un usuario puede hacer en función de su función de trabajo, los equipos definen a qué recursos pueden acceder. \nEsta combinación proporciona un sistema de control de acceso flexible y potente:", "width": "<PERSON><PERSON>", "yes": "Sí", "yesterday": "Ayer", "you": "Tú", "you-can-generate-a-jira-api-token-from-your-atlassian-account-settings": "Puede generar un token API JIRA a partir de la configuración de su cuenta de Atlassian.", "you-can-now-paste-this-prompt-to-your-preferred-ai-model": "Ahora puede pegar este aviso a su modelo AI preferido.", "you-can-only-subscribe-to-the-free-plan-once-even-if-your-previous-free-plan-subscription-is-no-longer-active-you-cannot-subscribe-to-the-free-plan-again-to-continue-using-the-service-please-choose-a-paid-plan": "Solo puede suscribirse al plan gratuito una vez. \nIncluso si su suscripción de plan gratuito anterior ya no está activa, no puede suscribirse al plan gratuito nuevamente. \nPara continuar usando el servicio, elija un plan pagado.", "you-dont-have-permission-to-access-the-integrations-page": "No tiene permiso para acceder a la página de integraciones.", "you-dont-have-permission-to-access-the-interviews-page": "No tiene permiso para acceder a la página de entrevistas.", "you-dont-have-permission-to-access-the-users-page": "No tiene permiso para acceder a la página de usuarios.", "you-dont-have-permission-to-assign-integrations-to-teams": "No tiene permiso para asignar integraciones a los equipos.", "you-dont-have-permission-to-edit-teams": "No tiene permiso para editar equipos.", "you-dont-have-permission-to-view-team-details": "No tiene permiso para ver los detalles del equipo.", "you-dont-have-permission-to-view-team-integrations": "No tiene permiso para ver las integraciones del equipo.", "you-havent-created-any-forms-yet-use-the-form-builder-to-get-started": "Todavía no has creado ningún formularios. \nUse el formulario de formulario para comenzar.", "your-account-has-been-successfully-upgraded": "Su cuenta se ha actualizado con éxito.", "your-description-has-been-enhanced-with-additional-details": "Su descripción se ha mejorado con detalles adicionales.", "your-email-header": "Tu encabezado de correo electrónico", "your-email-template-has-been-created-successfully": "Su plantilla de correo electrónico se ha creado con éxito.", "your-forms-dashboard": "Tus Formularios Panel", "your-interview-template-has-been-updated-successfully": "La plantilla de su entrevista se ha actualizado con éxito.", "your-jira-instance-domain": "Tu dominio de instancia de Jira", "en": "🇺🇸 Inglés", "es": "🇪🇸 español", "fr": "🇫🇷 Français", "it": "🇮🇹 italiano", "ai-powered": "AI", "CompanyUsagePage": {"activeStatus": "Activo", "aiInterviewerUsageTitle": "Uso del entrevistador de IA", "emailBuilderUsageTitle": "Uso del constructor de correo electrónico", "emailsUnit": "{Count} correos electrónicos", "exceededStatus": "Excedido", "failedToLoadUsageInfoToast": "No se pudo cargar información de uso", "loadingPageTitle": "Información de uso de la empresa", "minutesUnit": "{recuento} minutos", "tableHeadRemaining": "Restante", "tableHeadTotalLimit": "Límite total", "tableHeadUsagePercentage": "Porcentaje de uso", "tableHeadUsed": "Usado"}, "NotFoundPage": {"dashboardButton": "Panel", "footerText": "© {año} passisto. \nReservados todos los derechos.", "goBackButton": "Volver", "pageDescription": "No pudimos encontrar la página que estás buscando. \nPodría haber sido eliminado, renombrado o no existir.", "pageHeading": "Página no encontrada", "pageTitle": "404"}, "chatPage": {"actions": {"copy": "Copiar al portapapeles", "helpful": "<PERSON><PERSON>", "notHelpful": "No es útil", "readAloud": "Leer en voz alta", "regenerate": "Regenerar la respuesta"}, "avatars": {"ai": "AI", "user": "U"}, "input": {"placeholder": "Escriba su mensaje aquí ...", "waitingPlaceholder": "Esperando la respuesta ..."}, "recording": {"start": "Comenzar a grabar", "started": "Comenz<PERSON> a grabar", "stop": "<PERSON><PERSON> <PERSON>", "stopped": "<PERSON><PERSON><PERSON>"}, "session": {"label": "Sesión"}, "sources": {"label": "<PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON><PERSON>"}, "ThemeToggle": {"darkOption": "Oscuro", "lightOption": "Luz", "systemOption": "Sistema", "toggleThemeSrOnly": "Tema de alternar"}, "OnboardingPage": {"access-denied-please-contact-support": "Acceso denegado. \nPóngase en contacto con el soporte.", "acme-inc": "Acme Inc", "areas-of-interest": "Áreas de interés", "authentication-failed-please-sign-in-again": "La autenticación falló. \nPlease sign in again.", "authentication-failed-please-try-again": "La autenticación falló. \nPor favor intente de nuevo.", "bio": "Biografía", "company": "Compañía", "complete": "Completo", "complete-your-profile": "Completa tu perfil", "doe": "Gama", "e-g-web-development-ai-ml": "por ejemplo, desarrollo web, IA/ML, Data Science ...", "error": "Error", "experience-level": "Nivel de experiencia", "first-name": "Nombre de pila", "invalid-form-data-please-check-your-input": "Datos de formulario no válidos. \nVerifique su opinión.", "john": "<PERSON>", "junior-0-2-years": "Junior (0-2 años)", "last-name": "Apellido", "lead-architect": "Plomo/arquitecto", "linkedin-profile-optional": "Perfil de LinkedIn (opcional)", "mid-level-3-5-years": "<PERSON><PERSON> medio (3-5 años)", "network-error-please-check-connection": "Error de red. \nConsulte su conexión a Internet.", "next": "Próximo", "of": "de", "personal-info": "Información personal", "please-fill-required-fields": "Complete todos los campos requeridos", "please-fill-required-fields-company-role-experience": "Complete los campos requeridos: nivel de empresa, rol y experiencia", "please-fill-required-fields-first-last-name": "Complete los campos requeridos: nombre y apellido", "please-sign-in-to-continue": "Inicie sesión para continuar", "preferences": "Preferencias", "previous": "Anterior", "professional": "Profesional", "role": "Role", "select-experience-level": "Seleccionar nivel de experiencia", "senior-5-years": "Senior (5 años)", "server-error-please-try-again-later": "Error del servidor. \nVuelva a intentarlo más tarde.", "software-engineer": "Ingeniero de software", "something-went-wrong-please-try-again": "Algo salió mal. \nPor favor intente de nuevo.", "step": "Paso", "tell-us-about-yourself": "Cuéntanos sobre ti ...", "unexpected-error-occurred": "Se produjo un error inesperado. \nPor favor intente de nuevo."}, "UsageLimitDialog": {"candidateMessage1": "Este enlace de entrevista ya no está activo.", "candidateMessage2": "Póngase en contacto con la compañía que le envió esta invitación de entrevista para obtener ayuda.", "candidateTitle": "Enlace no disponible", "chat messages": "mensajes de chat", "closeButton": "Cerca", "genericUnit": "", "interview": "entrevista", "messages": "mensa<PERSON><PERSON>", "minutes": "minutos", "processingButton": "Tratamiento...", "toastFailedAccess": "No se pudo acceder al portal de facturación", "upgradeButton": "Plan de actualización", "userMessage": "Ha alcanzado su límite {type} ({limit} {unit}). \nPara continuar usando esta función, actualice su plan.", "userTitle": "Límite de uso excedido"}, "common": {"error": "Error"}, "usageLimit": {"exceeded": "Límite de uso excedido para esta función.", "warning": "Advertencia de límite de uso: Le quedan {remaining} para esta función en su plan actual.", "checkFailed": "Error al verificar el límite de uso"}, "workflows-automation": "Automatización de flujos de trabajo", "usage": {"errorCheckingLimit": "No se pudo verificar el límite de uso", "errorTitle": "Error", "limitExceeded": "Límite de uso excedido para esta característica.", "warning": "Advertencia de límite de uso: usted tiene {restante} restante para esta característica en su plan actual."}}