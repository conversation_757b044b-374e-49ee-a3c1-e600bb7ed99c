{"24-7-available": "24/7 disponibili", "CandidateInterviewPage": {"aiInterviewer": "Intervistatore di ai", "candidateInterviewTitle": "Intervista candidata", "candidateLabel": "Candi<PERSON><PERSON>", "interviewNotFoundDescription": "Non siamo riusciti a trovare l'intervista che stai cercando. \nPotrebbe essere stato eliminato o non esiste.", "interviewNotFoundTitle": "Intervista non trovata", "interviewTypeCardTitle": "Tipo di intervista", "levelBadge": "<PERSON><PERSON>", "loadingInterviewDetails": "Caricamento dei dettagli dell'intervista ...", "permissionDeniedToastDescription": "Non hai il permesso di accedere alla pagina delle interviste.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato", "returnToInterviews": "<PERSON><PERSON><PERSON> alle interviste", "roleCardTitle": "<PERSON><PERSON><PERSON>", "scoreCardTitle": "Punt<PERSON>", "scoreExcellent": "Eccellente", "scoreGood": "<PERSON><PERSON>", "scoreNeedsImprovement": "Ha bisogno di miglioramenti", "scoreNotScoredYet": "Non ancora segnato", "statusCompleted": "Completato", "statusInProgress": "In corso", "transcriptDescription": "Record completo della conversazione", "transcriptTitle": "Trascrizione di interviste", "viewFeedbackButton": "Visualizza il feedback"}, "ChatPage": {"creatingNewChatSession": "Creazione di una nuova sessione di chat ..."}, "ChatSettings": {"allTab": "<PERSON><PERSON>", "ariaLabelSettings": "Impostazioni", "dialogDescription": "Configura la tua esperienza di chat", "dialogTitle": "Impostazioni di chat", "errorLoadingKnowledgeBases": "Errore Caricamento delle basi di conoscenza: {errore}", "ftpTab": "Ftp", "jiraTab": "<PERSON><PERSON>", "knowledge": "Conoscenza:", "knowledgeBasesDescription": "Seleziona quali basi di conoscenza per cercare quando fai domande", "knowledgeBasesLabel": "Basi di conoscenza", "loadingKnowledgeBases": "Caricamento di basi di conoscenza ...", "webTab": "Web"}, "Chatbot": {"errors": {"apiEndpointNotFound": "Endpoint API di chatbot non trovato. \nSi prega di controllare la configurazione URL API.", "authenticationError": "Errore di autenticazione. \nPotresti non avere il permesso di accedere al chatbot.", "connectionRefused": "Impossibile connettersi al server Chatbot. \nSi prega di verificare se il server è in esecuzione sulla porta 5921.", "corsError": "Errore CORS: il server Chatbot non è configurato per accettare le richieste da questa origine.", "createSessionFailed": "Impossibile creare una nuova sessione di chat. \nPer favore riprova.", "deleteSessionFailed": "Impossibile eliminare la sessione di chat. \nPer favore riprova.", "errorMessagePrefix": "<PERSON><PERSON>, ho riscontrato un errore:", "loadHistory": "Impossibile caricare la cronologia della chat. \nPer favore riprova.", "loadSessions": "Impossibile caricare le sessioni di chat. \nPer favore riprova.", "newSessionWelcome": "Nuova sessione rilevata, che mostra un messaggio di benvenuto", "noSessionsFound": "Nessuna sessione di chat trovata per questo utente.", "pleaseTryAgain": "Per favore riprova. \nAssicurati di essere connesso a Internet e configurare correttamente il chatbot.", "sendMessageFailed": "Impossibile inviare messaggio.", "serverError": "Errore del server: {Messaggio}", "userNotFound": "Informazioni sull'utente non disponibili. \nPer favore riprova tra un momento."}, "logs": {"newSessionCreated": "Creato nuova sessione di chat con ID: {sessionId}", "newSessionDetected": "Nuova sessione rilevata, che mostra un messaggio di benvenuto", "waitingForUser": "In attesa che sia disponibile il backenduser prima di recuperare le sessioni utente", "waitingForUserHistory": "Aspet<PERSON>do che il backenduser sia disponibile prima di recuperare la cronologia della chat"}, "welcomeMessage": "<PERSON>iao! \nCome posso aiutarti oggi?"}, "DashboardPage": {"active": "Attivo", "active-teams": "squadre attive", "active-users": "utenti attivi", "ai-interview": "Intervista ai", "apr": "<PERSON>e", "aug": "Agosto", "avg-users-team": "Avg. \n<PERSON><PERSON><PERSON> per squadra", "completed": "completato", "conv-rate": "% tasso di conversione", "dec": "Dec", "email-builder": "Email Builder", "email-opened": "{count} aperto", "email-sent": "{count} inviato", "emails": "E-mail", "feb": "<PERSON><PERSON><PERSON>", "form-builder": "Forma Builder", "form-submissions": "{count} Invio", "forms": "Forme", "inactive": "Inattivo", "interview-completed": "{count} completato", "interview-pending": "{count} in sospeso", "interviews": "Interviste", "jan": "Gennaio", "jul": "<PERSON><PERSON><PERSON>", "jun": "<PERSON><PERSON>", "mar": "Mar", "may": "Maggio", "nov": "Nov", "oct": "<PERSON><PERSON>", "of-teams-have-members": "di team hanno membri", "opened": "aperto", "pending": "in attesa di", "quick-actions": "Azioni rapide", "sent": "inviato", "sep": "Set", "submissions": "Invio", "team-activity-rate": "Tasso di attività di squadra", "team-status": "Stato della squadra", "total-teams": "Squadre totali", "total-users": "Utenti totali", "user-growth": "Crescita dell'utente", "users": "<PERSON><PERSON><PERSON>", "users-per-team": "utenti per squadra", "conversion-rate": "{tasso}% tasso di conversione", "emails-opened": "{Count} aperto", "emails-sent": "{Count} inviato", "error": "Errore", "failed-to-load-stats": "Impossibile caricare le statistiche", "loading": "Caricamento", "no-team-activity": "<PERSON><PERSON><PERSON> dati sull'attività del team disponibile.", "no-team-distribution": "Nessun dato di distribuzione del team disponibile.", "no-user-growth": "<PERSON><PERSON><PERSON> dati sulla crescita degli utenti disponibili.", "retry": "<PERSON><PERSON><PERSON><PERSON>", "team-activity": "Attività di squadra", "team-distribution": "Distribuzione del team"}, "EditUserPage": {"buttons": {"cancel": "Cancellare", "save": "Salva le modifiche", "saving": "Risparmio..."}, "description": "Aggiorna i dettagli dell'utente, i ruoli, le autorizzazioni e i team.", "errors": {"loadData": "Errore di caricamento dei dati", "saveFailed": "Impossibile aggiornare l'utente", "teamsLoadFailed": "Impossibile caricare le squadre"}, "formErrors": {"emailExists": "Un utente con questa e -mail esiste già.", "emailInvalid": "Si prega di inserire un indirizzo email valido.", "emailRequired": "È richiesta l'e -mail.", "fillFields": "Si prega di compilare i seguenti campi:", "firstName": "È richiesto il nome.", "lastName": "È richiesto cognome.", "roles": "Almeno un ruolo deve essere selezionato.", "userExists": "L'utente esiste già"}, "formLabels": {"email": "E-mail", "firstName": "Nome di battesimo", "lastName": "Cognome"}, "loading": {"permissions": "Autorizzazioni di caricamento ...", "roles": "Ruoli di caricamento ...", "teams": "Team di caricamento ..."}, "permissionDenied": {"description": "Non hai il permesso di aggiornare gli utenti.", "title": "<PERSON><PERSON><PERSON> negato"}, "permissions": {"effectiveDescription": "Queste sono le autorizzazioni che questo utente avrà dopo aver applicato tutti i ruoli, le squadre e le prevalezioni.", "effectiveTitle": "Autorizzazioni efficaci", "extraDescription": "Concedere ulteriori autorizzazioni oltre a ciò che i ruoli e i team dell'utente forniscono.", "extraTitle": "Autorizzazioni extra", "inheritedDescription": "Queste autorizzazioni vengono automaticamente concesse attraverso i ruoli e le squadre dell'utente.", "inheritedTitle": "Autorizzazioni ereditarie", "noEffective": "Nessuna autorizzazione efficace", "noInherited": "Nessuna autorizzazione ereditaria. \nAssegna prima ruoli o squadre.", "noTeamsDescription": "Seleziona prima le squadre nella scheda Teams.", "noTeamsTitle": "Nessuna squadra selezionata", "revoke": "Revocare", "selectProjects": "Seleziona progetti", "selectTeams": "Seleziona squadre", "sourceRole": "Ruolo:", "sourceTeam": "Squadra:"}, "placeholders": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "roles": {"title": "Ruoli utente"}, "successMessage": "Utente aggiornato correttamente", "tabs": {"permissions": "Sostituzione del permesso", "roles": "<PERSON><PERSON><PERSON>", "teams": "<PERSON><PERSON>"}, "teams": {"member": "membro", "members": "membri", "noTeams": "Nessuna squadra disponibile", "required": "Necessario", "title": "<PERSON><PERSON>"}, "title": "Modifica l'utente"}, "EditorPage": {"backToGenerator": "<PERSON>na al <PERSON>e", "loadingEditor": "Editor di caricamento ...", "permissionDeniedToastDescription": "Non hai l'autorizzazione per accedere alla pagina del costruttore di posta elettronica.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato"}, "InterviewDetailPage": {"callStartedToastDescription": "Ora sei connesso con l'intervistatore di AI.", "callStartedToastTitle": "Chiamata iniziata", "candidateInformationCardTitle": "Informazioni sui candidati", "completedAtLabel": "Completato a", "completedBadge": "Completato", "failedToStartInterviewToast": "Non è riuscito a iniziare l'intervista. \nPer favore riprova.", "feedbackAvailable": "Feedback disponibile", "feedbackOnlyVisibleToCompany": "Il feedback è visibile solo all'azienda", "feedbackStatusLabel": "Stato di feedback", "interviewCompletedCardTitle": "Intervista completata", "interviewCompletedDescription": "Questa intervista è stata completata. \nIl feedback è stato fornito all'azienda. \nDi seguito puoi visualizzare la trascrizione della tua intervista.", "interviewDetailsTitle": "Dettagli dell'intervista", "interviewNotFoundDescription": "Non siamo riusciti a trovare l'intervista che stai cercando. \nPotrebbe essere stato eliminato o non esiste.", "interviewNotFoundTitle": "Intervista non trovata", "interviewOrCandidateNotFoundDescription": "Non siamo riusciti a trovare l'intervista o il candidato che stai cercando. \nPotrebbe essere stato eliminato o non esiste.", "interviewOrCandidateNotFoundTitle": "Intervista o candidato non trovato", "interviewTypeCardTitle": "Tipo di intervista", "loadingInterview": "Intervista di caricamento ...", "loadingInterviewDetails": "Caricamento dei dettagli dell'intervista ...", "noFeedback": "Nessun feedback", "notStartedBadge": "Non è iniziato", "readyToStartInterviewDescription": "Stai per iniziare un colloquio {IntervietyType} per la posizione {InterviewRole} {InterviewLevel}. \nFai clic sul pulsante Chiama quando sei pronto.", "readyToStartInterviewTitle": "Pronto per iniziare la tua intervista?", "returnToInterviews": "<PERSON><PERSON><PERSON> alle interviste", "roleCardTitle": "<PERSON><PERSON><PERSON>", "scoreLabel": "Punt<PERSON>", "startButton": "<PERSON><PERSON><PERSON>", "startInterviewButton": "Inizia l'intervista", "statusCompleted": "Completato", "statusInProgress": "In corso", "statusLabel": "Stato", "statusPending": "In attesa di", "techStackCardTitle": "Stack tecnologico", "viewFullTranscriptButton": "Visualizza la trascrizione completa", "webcamErrorDescription": "Impossibile accedere alla tua webcam. \nSi prega di controllare le autorizzazioni.", "webcamErrorTitle": "Errore webcam"}, "InterviewDialog": {"description": "Invia l'intervista \"{Role} {Level}\" ai candidati."}, "NavUser": {"billingSettings": "Impostazioni di fatturazione", "failedToAccessBillingPortalToast": "Impossibile accedere al portale di fatturazione", "upgradeToPro": "Aggiornamento a Pro", "usageInformation": "Informazioni sull'utilizzo", "userAltText": "Utente"}, "NewTeamPage": {"authenticationRequiredToast": "Autenticazione richiesta", "backToTeams": "<PERSON>na alle squadre", "breadcrumbCreateNewTeam": "Crea nuovo team", "breadcrumbTeams": "<PERSON><PERSON>", "cancelButton": "Cancellare", "cardDescription": "Crea una nuova squadra e assegna le autorizzazioni.", "cardTitle": "Crea nuovo team", "createTeamButton": "Crea team", "creatingTeamButton": "Creazione ...", "creationFailedToastDescription": "Si è verificato un errore durante la creazione.", "creationFailedToastTitle": "La creazione non è riuscita", "descriptionLabel": "Descrizione", "descriptionPlaceholder": "Una breve descrizione dello scopo e delle responsabilità del team", "descriptionRequired": "È richiesta una descrizione.", "failedToLoadPermissions": "Impossibile caricare le autorizzazioni", "formErrorsGenericTitle": "Errore", "formErrorsTitleFillFields": "Si prega di compilare i seguenti campi:", "formErrorsTitleTeamExists": "La squadra esiste già", "loadingPermissions": "Autorizzazioni di caricamento ...", "permissionDeniedToastDescription": "Non hai il permesso di creare squadre.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato", "permissionsDescription": "Le autorizzazioni assegnate a questo team saranno concesse a tutti i membri del team, se non esplicitamente revocati per utenti specifici.", "permissionsSelected": "{count, plural, one {# autorizzazione selezionata} other {# autorizzazioni selezionate}}", "teamAlreadyExists": "Una squadra con il nome \"{teamname}\" esiste già.", "teamCreatedToastDescription": "{TeamName} è stato creato correttamente.", "teamCreatedToastTitle": "Squadra creata", "teamNameLabel": "Nome della squadra", "teamNamePlaceholder": "Ingegneria", "teamNameRequired": "È richiesto il nome del team.", "teamPermissionsTitle": "Autorizzazioni di squadra"}, "RolesPermissionsPage": {"allCategoriesOption": "<PERSON><PERSON> le categorie", "categoryTableHead": "Categoria", "errorLoadingData": "Errore di caricamento dei dati", "errorLoadingDataDescription": "Impossibile caricare ruoli e autorizzazioni.", "infoBoxDescription1": "In questo sistema, le autorizzazioni vengono assegnate direttamente agli utenti, non ereditate attraverso i ruoli. \nI ruoli vengono utilizzati solo a scopo organizzativo. \nPer assegnare le autorizzazioni a un utente, modificare l'utente direttamente dalla pagina degli utenti.", "infoBoxDescription2": "In questa pagina, è possibile visualizzare tutti i ruoli e le autorizzazioni disponibili. \nUtilizzar<PERSON> le schede per passare da una vista, la casella di ricerca per trovare elementi specifici e il filtro di categoria per organizzare le autorizzazioni per tipo.", "infoBoxTitle": "Assegnazione dell'autorizzazione diretta", "loadingPermissions": "Autorizzazioni di caricamento ...", "noPermissionsFound": "Nessuna autorizzazione trovata", "noRolesFound": "<PERSON><PERSON><PERSON> ruolo trovato", "pageDescription": "Gestisci ruoli e autorizzazioni per la tua organizzazione.", "pageTitle": "<PERSON><PERSON><PERSON>", "permissionNameTableHead": "Nome autorizzazione", "permissionSystemInfoButton": "Sistema di gestione delle autorizzazioni", "permissionsManagementDescription": "Visualizza e filtra le autorizzazioni nel sistema", "permissionsManagementTitle": "Gestione delle autorizzazioni", "permissionsTab": "Autorizzazioni", "roleAdministrator": "Amministratore", "roleGuest": "Ospite", "roleManager": "Manager", "roleMember": "Membro", "roleNameTableHead": "Nome del ruolo", "rolesManagementDescription": "Visualizza e filtrano ruoli nel tuo sistema", "rolesManagementTitle": "Gestione dei ruoli", "rolesTab": "<PERSON><PERSON><PERSON>", "searchPermissionsPlaceholder": "Autorizzazioni di ricerca ...", "searchRolesPlaceholder": "Ruoli di ricerca ...", "systemNameTableHead": "Nome del sistema", "uncategorized": "Non categorizzato"}, "SearchPage": {"noSearchQueryToastDescription": "Inserisci una query di ricerca prima di cercare.", "noSearchQueryToastTitle": "Ricerca vuota", "related-searches": "Ricerche correlate:", "search-anything": "Cerca qualsiasi cosa ...", "searchErrorDescription": "Impossibile recuperare i risultati della ricerca. \nPer favore riprova.", "searchErrorTitle": "Errore di ricerca", "searchNowButton": "Cerca ora", "searching-in": "Ricerca in:", "searchingButton": "Ricerca", "title": "Search Passisto", "titleHighlight": "<PERSON><PERSON>"}, "SecurityInfoPage": {"a-user-with-the-manager-role-in-the-engineering-team-can-manage-engineering-resources": "Un utente con il ruolo di manager nel team di ingegneria può gestire le risorse di ingegneria.", "adds-all-permissions-from-the-users-teams": "Aggiunge tutte le autorizzazioni dai team dell'utente.", "adds-any-extra-permissions-granted-directly-to-the-user": "Aggiunge eventuali autorizzazioni aggiuntive concesse direttamente all'utente.", "administrator-role": "Ruolo dell'amministratore", "as-your-organization-grows-roles-make-it-easier-to-onboard-new-users-with-the-appropriate-access-levels-without-having-to-configure-permissions-individually": "Man mano che la tua organizzazione cresce, i ruoli rendono più semplice a bordo di nuovi utenti con i livelli di accesso appropriati senza dover configurare le autorizzazioni individualmente.", "assign-user-to-team": "Assegna l'utente al team", "auditability": "Auditabilità", "back-to-roles-and-permissions": "Torna ai ruoli", "benefits-of-role-based-access": "Vantaggi dell'accesso basato sul ruolo", "best-practices": "Best practice", "collects-all-permissions-from-the-users-roles": "Raccoglie tutte le autorizzazioni dai ruoli dell'utente.", "combined-permissions": "Autorizzazioni combinate", "considers-the-scope-of-each-permission-global-team-project-self": "Considera l'ambito di ogni autorizzazione (globale, team, progetto, self).", "create-data-provider": "<PERSON><PERSON> fornitore di dati", "create-data-provider-with-team-scope": "Crea provider di dati (con l'ambito del team)", "create-user": "Crea utente", "different-teams-can-have-different-permission-sets-based-on-their-needs": "Squadre diverse possono avere diversi set di autorizzazioni in base alle loro esigenze.", "each-role-contains-a-collection-of-permissions-with-specific-scopes-for-example": "Ogni ruolo contiene una raccolta di autorizzazioni con ambiti specifici. \nPer esempio:", "effective-permissions": "Autorizzazioni efficaci", "engineering-team": "Team di ingegneria", "example-scope-resolution": "Esempio: risoluzione dell'ambito", "export-data-with-project-scope": "Dati di esportazione (con portata del progetto)", "extra-permissions": "Autorizzazioni extra", "global": "Globale", "global-0": "Globale", "global-scope": "Portata globale", "how-roles-provide-a-foundation-for-permissions-in-the-system": "Come i ruoli forniscono una base per le autorizzazioni nel sistema.", "how-teams-provide-contextual-access-to-resources": "Come i team forniscono accesso contestuale alle risorse.", "if-a-user-has-the-view-analytics-permission-from": "Se un utente ha l'autorizzazione \"Visualizza analisi\" da:", "important-note": "Nota importante", "individual-users-can-be-granted-extra-permissions-or-have-inherited-permissions-revoked-to-handle-exceptions": "I singoli utenti possono essere concessi autorizzazioni extra o avere le autorizzazioni ereditarie revocate per gestire le eccezioni.", "instead-of-assigning-individual-permissions-to-each-user-you-can-assign-roles-that-contain-predefined-sets-of-permissions-making-user-management-more-efficient": "Invece di assegnare singole autorizzazioni a ciascun utente, è possibile assegnare ruoli che contengono serie di autorizzazioni predefinite, rendendo la gestione degli utenti più efficiente.", "jane-smith": "<PERSON>", "john-doe": "<PERSON>", "key-0": "→", "manage-billing": "Gestire la fatturazione", "manage-profile-with-self-scope": "Gestisci il profilo (con l'automanificazione)", "manager-role": "<PERSON><PERSON><PERSON> del manager", "multi-layered-permission-system": "Sistema di autorizzazione a più livelli", "must-avoid": "Deve evitare!", "our-security-system-uses-a-multi-layered-approach-to-permissions-allowing-for-fine-grained-access-control-while-maintaining-flexibility-and-ease-of-management": "Il nostro sistema di sicurezza utilizza un approccio a più livelli alle autorizzazioni, consentendo il controllo degli accessi a grana fine mantenendo la flessibilità e la facilità di gestione.", "overview-0": "Panoramica", "permission-applies-across-the-entire-system-to-all-resources-of-the-relevant-type": "L'autorizzazione si applica in tutto il sistema a tutte le risorse del tipo pertinente.", "permission-applies-only-to-resources-created-by-or-directly-assigned-to-the-user": "L'autorizzazione si applica solo alle risorse create o direttamente assegnate all'utente.", "permission-applies-only-to-resources-owned-by-or-associated-with-the-users-team-s": "L'autorizzazione si applica solo alle risorse di proprietà o associate al team dell'utente.", "permission-applies-only-to-specific-projects-regardless-of-team-ownership": "L'autorizzazione si applica solo a progetti specifici, indipendentemente dalla proprietà del team.", "permission-inheritance-flow": "Flusso di eredità del permesso", "permission-model-overview": "Panoramica del modello di autorizzazione", "permission-resolution": "Risoluzione del permesso", "permission-scopes": "Ambiti di autorizzazione", "permission-scopes-define-the-boundaries-within-which-a-permission-applies-they-allow-for-fine-grained-control-over-what-resources-a-user-can-access-with-a-given-permission": "Gli ampli di autorizzazione definiscono i confini entro i quali si applica un'autorizzazione. \nConsentono il controllo a grana fine sulle risorse che un utente può accedere con una determinata autorizzazione.", "permissions-apply-only-to-resources-owned-by-the-team-this-is-the-most-common-scope-for-team-permissions": "Le autorizzazioni si applicano solo alle risorse di proprietà del team. \nQuesto è l'ambito più comune per le autorizzazioni di squadra.", "permissions-granted-to-all-members-of-the-team-typically-scoped-to-team-resources": "Le autorizzazioni concesse a tutti i membri del team, in genere sono state convocate alle risorse del team.", "permissions-may-be-limited-to-specific-projects-that-the-team-is-working-on-even-if-those-projects-are-shared-with-other-teams": "Le autorizzazioni possono essere limitate a progetti specifici su cui il team sta lavorando, anche se tali progetti sono condivisi con altri team.", "project-scope": "Portata del progetto", "removes-any-permissions-explicitly-revoked-for-the-user": "Rimuove eventuali autorizzazioni revocate esplicitamente per l'utente.", "resource-scope": "Ambito di risorse", "revoked-permissions": "Autorizzazioni revocate", "robert-johnson": "<PERSON>", "role-based-access-control": "Controllo dell'accesso basato sul ruolo", "role-permissions": "Autorizzazioni di ruolo", "role-structure": "Struttura del ruolo", "roles": "<PERSON><PERSON><PERSON>", "roles-are-defined-at-the-system-level-and-cannot-be-created-or-modified-by-regular-users-contact-your-system-administrator-if-you-need-a-new-role-or-modifications-to-existing-roles": "I ruoli sono definiti a livello di sistema e non possono essere creati o modificati da utenti normali. \nContatta l'amministratore di sistema se hai bisogno di un nuovo ruolo o modifiche ai ruoli esistenti.", "roles-are-predefined-sets-of-permissions-that-represent-common-job-functions-or-responsibility-levels-within-your-organization-each-user-can-be-assigned-one-or-more-roles": "I ruoli sono serie predefinite di autorizzazioni che rappresentano funzioni lavorative comuni o livelli di responsabilità all'interno dell'organizzazione. \nA ciascun utente può essere assegnato uno o più ruoli.", "roles-ensure-that-users-with-similar-responsibilities-have-consistent-access-rights-reducing-the-risk-of-permission-inconsistencies": "I ruoli assicurano che gli utenti con responsabilità simili abbiano diritti di accesso coerenti, riducendo il rischio di incoerenze di autorizzazione.", "roles-provide-a-clear-structure-for-access-rights-making-it-easier-to-audit-who-has-access-to-what-and-why-they-have-that-access": "I ruoli forniscono una chiara struttura per i diritti di accesso, rendendo più facile da controllare chi ha accesso a cosa e perché hanno quell'accesso.", "scalability": "Scalabilità", "scope-examples": "Esempi di ambito", "scope-hierarchy-broadest-to-narrowest": "Gerarchia di portata (più ampia a stretta)", "scope-resolution": "Risoluzione dell'ambito", "scope-types": "Tipi di portata", "scopes": "<PERSON><PERSON><PERSON>", "security-system": "Sistema di sicurezza", "simplified-management": "Gestione semplificata", "some-team-permissions-may-have-global-scope-allowing-team-members-to-perform-actions-across-the-entire-system": "Alcune autorizzazioni di team possono avere un ambito globale, consentendo ai membri del team di eseguire azioni in tutto il sistema.", "standardization": "Standardizzazione", "team": "Squadra", "team-0": "Squadra", "team-1": "Squadra", "team-based": "Basato su team", "team-based-permissions": "Autorizzazioni basate sul team", "team-members": "Membri del team", "team-members-0": "Membri del team", "team-permission-scopes": "Accopi per autorizzazione della squadra", "team-permissions": "Autorizzazioni di squadra", "team-permissions-0": "Autorizzazioni di squadra", "team-permissions-1": "Autorizzazioni di squadra", "team-permissions-are-typically-scoped-to-the-teams-resources-but-they-can-also-have-different-scope-types": "Le autorizzazioni del team sono in genere ammesse alle risorse del team, ma possono anche avere diversi tipi di ambito.", "team-scope": "Ambito di squadra", "team-structure": "Struttura della squadra", "team-vs-role-permissions": "Team vs. Autorizzazioni di ruolo", "teams": "<PERSON><PERSON>", "teams-and-permissions": "Squadre e autorizzazioni", "teams-or-groups-allow-you-to-organize-users-and-grant-permissions-based-on-the-resources-they-need-to-access-teams-are-particularly-useful-for-departmental-or-project-based-access-control": "I team (o gruppi) consentono di organizzare gli utenti e concedere le autorizzazioni in base alle risorse di cui hanno bisogno per accedere. \nI team sono particolarmente utili per il controllo dell'accesso dipartimentale o basato su progetti.", "the-effective-scope-will-be-global-as-its-broader-than-team": "L'ambito efficace sarà globale, in quanto è più ampio del team.", "the-leadership-team-with-global-scope": "The Leadership Team (con ambito globale)", "the-same-user-in-the-marketing-team-can-manage-marketing-resources": "Lo stesso utente nel team di marketing può gestire le risorse di marketing.", "the-specific-resources-projects-data-etc-that-the-team-has-access-to": "Le risorse specifiche (pro<PERSON><PERSON>, dati, ecc.) A cui il team ha accesso.", "their-manager-role-with-team-scope": "Il loro ruolo di manager (con l'ambito di squadra)", "understanding-how-permission-scopes-limit-the-reach-of-permissions": "Comprendere come gli ampli di autorizzazione limitano la portata delle autorizzazioni.", "understanding-how-permissions-work-in-our-enterprise-system": "Comprendere come funzionano le autorizzazioni nel nostro sistema aziendale.", "understanding-the-permission-model-and-access-control": "Comprensione del modello di autorizzazione e del controllo di accesso.", "user-can-create-data-providers-only-for-their-team-s": "L'utente può creare fornitori di dati solo per i loro team.", "user-can-export-data-only-from-specific-projects-they-have-access-to": "L'utente può esportare dati solo da progetti specifici a cui hanno accesso.", "user-can-only-manage-their-own-profile-information": "L'utente può gestire solo le proprie informazioni sul profilo.", "user-can-view-analytics-for-all-teams-and-projects-in-the-system": "L'utente può visualizzare l'analisi per tutti i team e i progetti nel sistema.", "user-overrides": "L'utente prevale", "users-are-assigned-roles-that-grant-a-set-of-permissions-roles-provide-a-baseline-of-access-appropriate-for-different-job-functions": "Agli utenti vengono assegnati ruoli che concedono una serie di autorizzazioni. \nI ruoli forniscono una base di accesso appropriato per diverse funzioni lavorative.", "users-belong-to-teams-that-grant-additional-permissions-team-permissions-are-typically-scoped-to-the-teams-resources": "Gli utenti appartengono a team che concedono autorizzazioni aggiuntive. \nLe autorizzazioni del team sono in genere ammesse alle risorse del team.", "users-who-belong-to-the-team-and-inherit-its-permissions": "Gli utenti che appartengono al team e ereditano le sue autorizzazioni.", "view-analytics": "Visualizza l'analisi", "view-analytics-0": "Visualizza l'analisi", "view-analytics-with-global-scope": "Visualizza analisi (con portata globale)", "what-are-permission-scopes": "Cosa sono gli ampli di autorizzazione?", "what-are-roles": "Cosa sono i ruoli?", "when-a-user-has-the-same-permission-with-different-scopes-e-g-from-different-roles-or-teams-the-system-uses-the-broadest-scope": "Quando un utente ha la stessa autorizzazione con ambiti diversi (ad esempio, da ruoli o team diversi), il sistema utilizza l'ambito più ampio.", "when-determining-if-a-user-has-a-specific-permission-the-system": "Quando si determina se un utente ha un'autorizzazione specifica, il sistema:", "while-roles-define-what-a-user-can-do-based-on-their-job-function-teams-define-what-resources-they-can-access-this-combination-provides-a-flexible-and-powerful-access-control-system": "Mentre i ruoli definiscono ciò che un utente può fare in base alla propria funzione lavorativa, i team definiscono le risorse a cui possono accedere. \nQuesta combinazione fornisce un sistema di controllo di accesso flessibile e potente."}, "TeamsPage": {"authenticationRequiredToast": "Autenticazione richiesta", "cancelButton": "Cancellare", "createTeamButton": "Crea team", "deleteButton": "Eliminare", "deleteCompanyTeamWarning": "Il team \"azienda\" predefinito non può essere eliminato.", "deleteTeamDialogDescription": "Sei sicuro di voler eliminare {TeamName}? \nQuesta azione non può essere annullata e rimuoverà tutte le associazioni di squadra.", "deleteTeamDialogTitle": "Elimina la squadra", "deletingButton": "Eliminazione ...", "errorDeletingTeamDescription": "Impossibile eliminare la squadra.", "errorDeletingTeamTitle": "Errore di eliminazione della squadra", "errorLoadingTeamsDescription": "C'è stato un errore di caricamento dei dati dei tuoi team.", "errorLoadingTeamsTitle": "Team di caricamento degli errori", "loadingTeams": "Team di caricamento ..", "noTeamsFoundDescription": "Crea la tua prima squadra per iniziare.", "noTeamsFoundTitle": "Nessuna squadra trovata.", "permissionDeniedToastDescription": "Non hai il permesso di accedere alla pagina dei team.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato", "searchTeamsPlaceholder": "Squadre di ricerca ...", "teamDeletedToastDescription": "{TeamName} è stato eliminato correttamente.", "teamDeletedToastTitle": "Team eliminato", "teamsDescription": "Gest<PERSON>ci le squadre della tua organizzazione.", "teamsTitle": "<PERSON><PERSON>"}, "UserCardView": {"actionsSrOnly": "Azioni", "activate": "<PERSON><PERSON><PERSON><PERSON>", "ban": "Divieto", "createdLabel": "<PERSON><PERSON><PERSON>", "delete": "Eliminare", "edit": "Modificare", "emailLabel": "E-mail", "inactiveAccount": "Account inattivo", "noPermissions": "Nessuna autorizzazione", "noRolesAssigned": "<PERSON><PERSON><PERSON> ruolo assegnato", "noTeams": "Nessuna squadra", "noUsersFoundDescription": "Prova a regolare la ricerca o i filtri", "noUsersFoundTitle": "<PERSON><PERSON>un utente trovato", "permissionsCount": "{count} autorizzazione {count, plurale, uno {} altri {s}}", "permissionsLabel": "Autorizzazioni", "rolesLabel": "<PERSON><PERSON><PERSON>", "statusActive": "Attivo", "statusInactive": "Inattivo", "teamsLabel": "<PERSON><PERSON>", "viewDetails": "Visualizza i dettagli"}, "UserCreation": {"cancelButton": "Cancellare", "cardDescription": "Aggiungi un nuovo utente con ruoli, autorizzazioni e assegnazioni di team.", "cardTitle": "Crea nuovo utente", "emailLabel": "E-mail", "emailPlaceholder": "<EMAIL>", "extraPermissions": {"description": "Concedere ulteriori autorizzazioni oltre a ciò che i ruoli e i team dell'utente forniscono.", "loading": "Autorizzazioni di caricamento ...", "noPermissions": "Nessuna autorizzazione extra selezionata.", "title": "Autorizzazioni extra"}, "firstNameLabel": "Nome di battesimo", "firstNamePlaceholder": "<PERSON>", "formErrors": {"emailConflict": "Un utente con questa e -mail esiste già.", "emailInvalid": "Si prega di inserire un indirizzo email valido.", "emailRequired": "È richiesta l'e -mail.", "error": "Errore", "fillInFields": "Si prega di compilare i seguenti campi:", "firstNameRequired": "È richiesto il nome.", "lastNameRequired": "È richiesto cognome.", "roleRequired": "Almeno un ruolo deve essere selezionato.", "userExists": "L'utente esiste già."}, "inheritedPermissions": {"description": "Queste autorizzazioni vengono automaticamente concesse attraverso i ruoli e le squadre dell'utente.", "noPermissions": "Nessuna autorizzazione ereditaria. \nAssegna prima ruoli o squadre.", "revokeAction": "Revocare", "sourceRole": "Ruolo: {nome}", "sourceTeam": "Team: {nome}", "title": "Autorizzazioni ereditarie"}, "lastNameLabel": "Cognome", "lastNamePlaceholder": "<PERSON><PERSON>", "rolesSection": {"label": "Ruoli utente", "loading": "Ruoli di caricamento ...", "noRoles": "<PERSON><PERSON><PERSON> ruolo disponibile"}, "submitButton": "Crea utente", "tabs": {"permissions": "Sostituzione del permesso", "roles": "<PERSON><PERSON><PERSON>", "teams": "<PERSON><PERSON>"}, "teamsSection": {"companyTeamRequired": "(Necessario)", "failedToLoad": "Impossibile caricare i team: {errore}", "label": "<PERSON><PERSON>", "loading": "Team di caricamento ...", "memberCount": "{Count, plurale, uno {membro} altri {membri}}", "noTeams": "Nessuna squadra disponibile"}, "toasts": {"authRequired": "Autenticazione richiesta", "creationFailedDescription": "Si è verificato un errore durante la creazione.", "creationFailedTitle": "La creazione non è riuscita", "dataError": "Errore di caricamento dei dati", "permissionDeniedDescription": "Non hai il permesso di creare utenti.", "permissionDeniedTitle": "<PERSON><PERSON><PERSON> negato", "userCreatedSuccess": "Utente creato correttamente"}}, "UserTableView": {"actionsTableHead": "Azioni", "activateAction": "<PERSON><PERSON><PERSON><PERSON>", "banAction": "Divieto", "deleteAction": "Eliminare", "editUserButtonTitle": "Modifica l'utente", "editUserSrOnly": "Modificare", "emailTableHead": "E-mail", "moreOptionsSrOnly": "Più opzioni", "nameTableHead": "Nome", "noRoles": "<PERSON><PERSON><PERSON> ruolo", "noTeams": "Nessuna squadra", "noUsersFound": "<PERSON><PERSON>un utente trovato.", "rolesTableHead": "<PERSON><PERSON><PERSON>", "teamsTableHead": "<PERSON><PERSON>", "viewDetailsButtonTitle": "Visualizza i dettagli", "viewDetailsSrOnly": "Visualizza i dettagli"}, "UserViewPage": {"accountDetailsHeading": "Dettagli dell'account", "activeBadge": "Attivo", "activityNotEnabled": "Il monitoraggio delle attività non è abilitato", "activityTab": "Attività", "authenticationErrorToastDescription": "Accedi per visualizzare i dettagli dell'utente.", "authenticationErrorToastTitle": "Errore di autenticazione", "backButton": "Indietro", "backToUsersButton": "Torna agli utenti", "createdLabel": "<PERSON><PERSON><PERSON>", "editUserButton": "Modifica l'utente", "enableActivityTracking": "Abilita il monitoraggio delle attività nelle impostazioni", "extraPermissionSource": "Autorizzazione extra", "extraPermissionsHeading": "Autorizzazioni extra", "fromRoleSource": "<PERSON> ruolo {rolename}", "fromTeamSource": "Dal team {TeamName}", "inactiveBadge": "Inattivo", "loadingUserDetails": "Caricamento dei dettagli dell'utente ...", "noExtraPermissions": "Nessuna autorizzazione extra assegnata", "noPermissionsAssigned": "Questo utente non ha autorizzazioni", "noRevokedPermissions": "Nessuna autorizzazione revocata", "noRolesAssigned": "<PERSON><PERSON><PERSON> ruolo assegnato", "noTeamsAssigned": "Nessuna squadra assegnata", "otherCategory": "Altro", "permissionDeniedToastDescription": "Non hai il permesso di visualizzare gli utenti.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato", "permissionOverridesTab": "Sostituzione del permesso", "permissionsLabel": "Autorizzazioni", "permissionsTab": "Autorizzazioni", "revokedPermissionsHeading": "Autorizzazioni revocate", "rolesHeading": "<PERSON><PERSON><PERSON>", "teamsHeading": "<PERSON><PERSON>", "unknownSource": "Fonte sconosciuta", "userDetailsCardDescription": "Informazioni complete su questo utente", "userDetailsCardTitle": "Dettagli dell'utente", "userNotFoundDescription": "Non è stato possibile trovare l'utente richiesto.", "userNotFoundTitle": "Utente non trovato", "userNotFoundToastDescription": "Non è stato possibile trovare l'utente richiesto.", "userNotFoundToastTitle": "Utente non trovato"}, "a-descriptive-name-for-this-integration": "Un nome descrittivo per questa integrazione", "a-descriptive-name-for-this-integration-0": "Un nome descrittivo per questa integrazione", "a-descriptive-name-for-this-integration-1": "Un nome descrittivo per questa integrazione", "a-user-with-the-manager-role-in-the-engineering-team-can-manage-engineering-resources": "Un utente con il ruolo di \"manager\" nel team \"ingegneria\" può gestire le risorse di ingegneria", "access-anytime": "Accedere in qualsiasi momento", "access-code": "Codice di accesso", "access-granted": "Accesso concesso", "access-interview": "Intervista ad accesso", "access-powerful-ai-tools-to-streamline-your-recruitment-process-and-find-the-perfect-candidates-faster": "Accedi a potenti strumenti di intelligenza artificiale per semplificare il processo di reclutamento e trovare i candidati perfetti più velocemente", "acme-inc": "Acme Inc.", "actions": "Azioni", "actions-0": "Azioni", "activate-user": "Attiva l'utente", "active": "Attivo", "active-0": "Attivo", "active-data-connections": "Connessioni di dati attivi", "active-teams": "squadre attive", "active-users": "utenti attivi", "activity-log-coming-soon": "Registro delle attività in arrivo", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add-candidate": "Aggiungi candidato", "add-column": "Aggiungi colonna", "add-item": "Aggiungi articolo", "add-members": "Aggiungi membri", "add-members-0": "Aggiungi membri", "add-platform": "Aggiungi piattaforma", "add-row": "Aggiungi riga", "add-team-members": "Aggiungi i membri del team", "add-user": "Aggiungi l'utente", "add-users-to-teams-they-dont-need-to-be-in": "Aggiungi utenti ai team in cui non hanno bisogno di essere", "addFieldsHeading": "Aggiungi campi", "adds-all-permissions-from-the-users-teams": "Aggiunge tutte le autorizzazioni dai team dell'utente", "adds-any-extra-permissions-granted-directly-to-the-user": "Aggiunge eventuali autorizzazioni aggiuntive concesse direttamente all'utente", "administrator-role": "Ruolo dell'amministratore", "advanced-ai-technology": "Tecnologia AI avanzata", "agent": "<PERSON><PERSON>", "ai-agent": "Agente AI", "ai-email-template-generator": "Generatore di modelli e -mail di AI", "ai-interview": "Intervista ai", "ai-interviewer": "AI Interviewer", "ai-powered-form-builder": "Builder di forma alimentato AI", "ai-powered-interviewer": "AI Interviewer", "aiFieldSuggestions": {"anErrorOccurred": "Si è verificato un errore durante la generazione di suggerimenti. \nPer favore riprova.", "applySuggestionsButton": "Applicare suggerimenti", "describeFormPurposeError": "Descrivi il tuo scopo del modulo", "generateFieldsButton": "Generare campi", "generateSuggestionsFailedError": "Impossibile generare suggerimenti. \nPer favore riprova.", "generatingButton": "Generare ...", "suggestionsCardFieldsCount": "{count} campi suggeriti", "suggestionsCardTitle": "{titolo}", "textareaPlaceholder": "Descrivi il tuo scopo del modulo (ad es. \"Modulo di domanda di lavoro per una posizione di marketing\", \"Modulo di registrazione degli eventi per una conferenza\")"}, "alignment": "Allineamento", "all": "<PERSON><PERSON>", "all-in-one-ai-recruitment-suite": "Suite di reclutamento all-in-one.", "all-integrations": "<PERSON>tte le integrazioni", "all-levels": "<PERSON><PERSON> i livelli", "all-roles": "<PERSON><PERSON> i ruoli", "all-sources": "<PERSON><PERSON> le fonti", "all-statuses": "Tutti gli stati", "all-statuses-0": "Tutti gli stati", "all-teams": "<PERSON><PERSON> le squadre", "all-types": "<PERSON>tti i tipi", "alt-text": "Alt text", "always-grant-users-the-minimum-permissions-necessary-to-perform-their-job-functions-this-reduces-the-risk-of-accidental-or-intentional-misuse-of-privileges": "Concedere sempre agli utenti le autorizzazioni minime necessarie per svolgere le loro funzioni lavorative. \nC<PERSON>ò riduce il rischio di uso improprio accidentale o intenzionale dei privilegi.", "an-error-occurred-please-try-again-later": "Si è verificato un errore. \nPer favore riprova più tardi.", "an-error-occurred-while-generating-the-form-please-try-again": "Si è verificato un errore durante la generazione del modulo. \nPer favore riprova.", "an-unexpected-error-occurred": "Si è verificato un errore imprevisto.", "an-unexpected-error-occurred-please-try-again-or-return-to-the-dashboard": "Si è verificato un errore imprevisto. \nRiprova o torna alla dashboard.", "an-unknown-error-occurred": "Si è verificato un errore sconosciuto", "annual": "Annuale", "anyone-with-this-link-can-view-and-submit-the-form": "Chiunque abbia questo link può visualizzare e inviare il modulo", "api-integration": "Integrazione API", "api-key-optional": "Chiave API (opzionale)", "api-token": "Token API", "api-token-is-required": "È richiesto token API.", "apr": "<PERSON>e", "are-you-sure-you-want-to-assign": "Sei sicuro di voler assegnare", "are-you-sure-you-want-to-delete": "Sei sicuro di voler eliminare", "are-you-sure-you-want-to-delete-the-integration": "Sei sicuro di voler eliminare l'integrazione \"", "are-you-sure-you-want-to-delete-this-chat-session-this-action-cannot-be-undone": "Sei sicuro di voler eliminare questa sessione di chat? \nQuesta azione non può essere annullata.", "are-you-sure-you-want-to-delete-this-form": "Sei sicuro di voler eliminare questo modulo?", "are-you-sure-you-want-to-remove": "Sei sicuro di voler rimuovere", "areas-of-interest": "<PERSON><PERSON> di interesse", "as-your-organization-grows-roles-make-it-easier-to-onboard-new-users-with-the-appropriate-access-levels-without-having-to-configure-permissions-individually": "Man mano che la tua organizzazione cresce, i ruoli rendono più semplice a bordo di nuovi utenti con i livelli di accesso appropriati senza dover configurare le autorizzazioni individualmente.", "assign-administrator-roles-unnecessarily": "Assegna i ruoli di amministratore inutilmente", "assign-integration": "Assegnare l'integrazione", "assign-integrations": "Assegnare integrazioni", "assign-integrations-to": "Assegnare integrazioni a", "assign-multiple-roles-when-one-would-suffice": "Assegnare più ruoli quando uno sarebbe sufficiente", "assign-roles-based-on-job-responsibilities": "Asse<PERSON><PERSON> ruoli in base alle responsabilità lavorative", "assign-to-team": "Assegna alla squadra", "assign-user-to-team": "Assegna l'utente al team", "assigning": "Assegnazione ...", "audit-checklist": "Elenco di controllo dell'audit", "auditability": "Auditabilità", "authentication-required": "Autenticazione richiesta", "authentication-required-0": "Autenticazione richiesta", "auto": "Auto", "auto-0": "Auto", "avg-users-team": "Utenti/team AVG", "back-to-home": "Torna a casa", "back-to-roles-and-permissions": "Torna ai ruoli", "back-to-team-details": "Torna ai dettagli della squadra", "back-to-team-details-0": "Torna ai dettagli della squadra", "back-to-teams": "<PERSON>na alle squadre", "background-color": "Colore di sfondo", "ban-user": "Vietare l'utente", "bank-grade-encryption": "Crittografia di livello bancario", "behavioral": "Comportamentale", "benefits-of-role-based-access": "Vantaggi dell'accesso basato sul ruolo", "best-practices": "Best practice", "bg-background-shadow-none-border-rounded-lg": "bordo bg-background ombretto arro<PERSON>to-lg", "bio": "Bio", "bold": "Grassetto", "bolder": "<PERSON><PERSON> audace", "border-color": "Colore del bordo", "border-radius": "Raggio di confine", "border-radius-0": "Raggio di confine", "border-width": "Larghezza del confine", "brand-colors": "Colori del marchio", "branding": "Branding", "browse-files": "Sfoglia i file", "built-for-your-workflow": "Costruito per il tuo flusso di lavoro", "bullet-list": "Elenco dei proiettili", "button": "Pulsante", "button-url": "URL del pulsante", "button-width": "Larghezza del pulsante", "c-2025-your-company-all-rights-reserved": "© 2025 la tua azienda. \nTutti i diritti riservati.", "call": "<PERSON><PERSON><PERSON>", "camera-is-turned-off": "La fotocamera è disattivata", "cancel": "Cancellare", "cancel-anytime": "✓ <PERSON><PERSON><PERSON> in qualsiasi momento", "candidate": "Candi<PERSON><PERSON>", "cannot-delete-default-team": "Impossibile eliminare la squadra predefinita", "caution-with-overrides": "ATTENZIONE CON SURARI", "center": "Centro", "chat": "Chiacchierata", "chat-session-deleted-successfully": "Sessione di chat eliminata correttamente", "check-for-users-with-excessive-permissions": "Controlla gli utenti con autorizzazioni eccessive", "checkbox": "Casella di controllo", "choose-between-ftp-and-secure-sftp": "Scegli tra FTP e SFFET SFTP.", "choose-role-level-and-tech-stack": "Scegli lo <PERSON> di ruolo, livello e tecnologia", "choose-your-plan": "<PERSON><PERSON><PERSON> il tuo piano", "click-me": "Clicca a me", "close": "<PERSON><PERSON>", "collects-all-permissions-from-the-users-roles": "Raccoglie tutte le autorizzazioni dai ruoli dell'utente", "color-preview": "Anteprima del colore", "columns": "Colonne", "combined-permissions": "Autorizzazioni combinate", "company": "Azienda", "competitor-analysis": "<PERSON><PERSON><PERSON> della concorrenza", "complete": "Completare", "complete-your-profile": "Completa il tuo profilo", "completed": "Completato", "completed-0": "Completato", "completed-at": "Completato a", "components": "Componenti", "conduct-regular-audits-of-your-permission-system-to-ensure-it-remains-effective-and-secure": "Condurre audit regolari del sistema di autorizzazione per garantire che rimanga efficace e sicuro.", "configuration": "Configurazione", "configuration-details-not-available": "Dettagli di configurazione non disponibili", "configure-your-ftp-integration-by-providing-the-required-details-below": "Configurare l'integrazione FTP fornendo i dettagli richiesti di seguito.", "configure-your-jira-integration-by-providing-the-required-details-below": "Configurare l'integrazione JIRA fornendo i dettagli richiesti di seguito.", "configure-your-search-experience": "Configura la tua esperienza di ricerca", "configure-your-web-integration-by-providing-the-required-details-below": "Configurare l'integrazione Web fornendo i dettagli richiesti di seguito.", "connect-your-form-to-external-services": "Collega il tuo modulo a servizi esterni", "connection-type": "Tipo di connessione", "considers-the-scope-of-each-permission-global-team-project-self": "Considera l'ambito di ogni autorizzazione (globale, team, progetto, self)", "contact-sales": "Vendite di contatto", "contact-support": "Supporto di contatto", "content": "<PERSON><PERSON><PERSON>", "conv-rate": "% Conv. \nvalutare", "copy": "Copia", "copy-prompt": "Copia prompt", "copy-the-json-response": "Copia la risposta JSON", "copy-the-prompt-above": "Copia il prompt sopra", "create-an-account-and-get-access-to-all-features-for-15-days-no-credit-card-required": "Crea un account e accedi a tutte le funzionalità per 15 giorni, nessuna carta di credito richiesta.", "create-an-interview": "<PERSON>rea un'intervista", "create-and-manage-interview-templates-to-send-to-candidates": "Crea e gestisci modelli di intervista da inviare ai candidati", "create-custom-forms-with-intelligent-field-suggestions-based-on-your-business-needs": "Crea moduli personalizzati con suggerimenti sul campo intelligenti in base alle esigenze aziendali", "create-data-provider": "<PERSON><PERSON> fornitore di dati", "create-data-provider-with-team-scope": "Crea un fornitore di dati con l'ambito del team", "create-ftp-integration": "Crea integrazione FTP", "create-jira-integration": "Crea integrazione JIRA", "create-new-form": "Crea una nuova forma", "create-roles-with-excessive-permissions": "Crea ruoli con autorizzazioni eccessive", "create-team": "Crea team", "create-teams-solely-for-permission-management": "Crea team esclusivamente per la gestione delle autorizzazioni", "create-teams-with-overlapping-responsibilities": "Crea team con responsabilità sovrapposte", "create-user": "Crea utente", "create-web-integration": "Crea integrazione web", "create-your-first-form": "Crea la tua prima forma", "create-your-first-form-by-describing-what-you-need-and-our-ai-will-generate-it-for-you": "Crea la tua prima forma descrivendo ciò di cui hai bisogno e la nostra intelligenza artificiale lo genererà per te.", "createInterviewTemplatePage": {"addButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addTechnologiesHint": "Aggiungi tecnologie pertinenti per questo ruolo", "aiQuestionGenerationDescription": "Configurare quante domande dovrebbe generare l'IA per questa intervista", "aiQuestionGenerationTitle": "AI Generazione di domande", "basedOnInterviewType": "Basato sul tipo di intervista selezionato", "basicInformationTitle": "Informazioni di base", "behavioralInterview": "Intervista comportamentale", "cancelButton": "Cancellare", "completeRequiredFieldsTitle": "Campi richiesti completi", "createTemplateButton": "<PERSON><PERSON>", "creatingButton": "Creazione ...", "creationFailedToastDescription": "Qualcosa è andato storto. \nPer favore riprova.", "creationFailedToastTitle": "La creazione non è riuscita", "experienceLevelLabel": "Livello di esperienza", "interviewTypeLabel": "Tipo di intervista", "juniorLevel": "Junior (0-2 anni)", "leadLevel": "Lead/Principal (8 anni)", "levelRequired": "È richiesto il livello di esperienza", "midLevel": "<PERSON><PERSON> (2-5 anni)", "missingInformationToastDescription": "Si prega di compilare tutti i campi richiesti prima di salvare.", "missingInformationToastTitle": "Informazioni mancanti", "mixedInterview": "Intervista mista", "noTechnologiesAdded": "Nessuna tecnologia ancora aggiunta", "numberOfQuestionsLabel": "Numero di domande", "numberOfQuestionsPlaceholder": "10", "pageDescription": "Progetta un modello per interviste alimentate dall'intelligenza artificiale: le domande verranno generate automaticamente", "pageTitle": "Crea <PERSON>lo di intervista", "permissionDeniedToastDescription": "Non hai il permesso di accedere a questa pagina.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato", "questionCountHint": "<PERSON><PERSON><PERSON> tra 1-50 domande", "questionCountRange": "Il conteggio delle domande deve essere compreso tra 1-50", "questionsGeneratedTitle": "{questionCount, plural, one {Verrà generata 1 domanda} other {Verranno generate # domande}}", "recommendedRangeLabel": "Gamma consigliata", "requiredField": "(necessario)", "roleRequired": "È richiesto il titolo del ruolo", "roleTitleLabel": "<PERSON><PERSON> del ruolo", "roleTitlePlaceholder": "per esempio. \nSviluppatore di frontend senior", "selectExperienceLevelPlaceholder": "Seleziona il livello di esperienza", "selectInterviewTypeFirst": "Seleziona prima il tipo di intervista", "selectInterviewTypePlaceholder": "Seleziona il tipo di intervista", "seniorLevel": "Senior (5 anni)", "systemDesign": "Progettazione del sistema", "techStackPlaceholder": "per esempio. \nReact, node.js, typescript", "techStackRequired": "È richiesta almeno una tecnologia", "technicalInterview": "Intervista tecnica", "technologyStackTitle": "Stack tecnologico", "templateCreatedToastDescription": "Il tuo modello di intervista è stato creato con domande generate da Ai-Generate.", "templateCreatedToastTitle": "<PERSON><PERSON> creato", "typeRequired": "È richiesto il tipo di intervista", "questionsGeneratedDescription": "L'IA creerà {questionCount} domande {type, select, technical {tecniche} behavioral {comportamentali} system_design {di progettazione di sistemi} mixed {miste} other {di colloquio}} rilevanti basate sul tuo modello"}, "created-at": "<PERSON>reato a", "created-by": "<PERSON><PERSON><PERSON> <PERSON>", "created-by-0": "<PERSON><PERSON><PERSON> <PERSON>:", "creating-more-specialized-teams-with-appropriate-permissions": "Creazione di team più specializzati con autorizzazioni appropriate", "csv-file-processed-successfully-interviews-will-be-sent-to-the-candidates": "File CSV elaborato correttamente. \nLe interviste verranno inviate ai candidati.", "csv-file-should-have-2-columns-full-name-and-email": "Il file CSV dovrebbe avere 2 colonne: \"nome completo\" e \"email\"", "csv-upload": "Caricamento CSV", "current-integration-status": "Stato di integrazione attuale", "currently-processing": "Attualmente elaborazione", "currently-subscribed": "Attualmente sottoscritto", "custom": "Costume", "custom-features": "Funzionalità personalizzate", "custom-integration": "Integrazione personalizzata", "custom-solutions-for-large-teams": "Soluzioni personalizzate per grandi team", "customizable": "Personalizzabile", "customize-the-colors-to-match-your-brand-identity": "Personalizza i colori per abbinare l'identità del tuo marchio", "dashboard": "Pannello di controllo", "dashboardPreview": {"createdLabel": "Creato:", "deleteButtonTooltip": "Eliminare", "deleteConfirmation": "Sei sicuro di voler eliminare questo modulo?", "editButton": "Modificare", "fieldsCount": "{count} Fields", "loadingForms": "Caricamento di forme ...", "noDescription": "Nessuna descrizione", "noFormsCardDescription": "Non hai ancora creato alcun modulo. \nUsa il costruttore di moduli sopra per iniziare.", "noFormsCardTitle": "Le tue forme", "responsesButton": "Risposte", "viewButton": "Visualizzazione", "yourFormsTitle": "Le tue forme"}, "dashed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data-integrations": "Integrazioni di dati", "dedicated-support": "Supporto dedicato", "delete": "Eliminare", "delete-chat-session": "Elimina la sessione di chat", "delete-error": "Elimina errore:", "delete-integration": "Elimina l'integrazione", "delete-session": "<PERSON><PERSON> sessione", "delete-user": "Elimina l'utente", "deleting": "Eliminazione ...", "describe-any-conditional-logic-e-g-show-extra-fields-based-on-selection": "Descrivi qualsiasi logica condizionale (ad esempio, mostra campi extra in base alla selezione).", "describe-the-email-you-want-to-create-for-example-create-a-welcome-email-for-new-customers-of-my-fitness-company-fitlife-with-a-blue-color-scheme": "Descrivi l'e -mail che desideri creare. \nAd esempio: \"Crea un'e -mail di benvenuto per i nuovi clienti della mia società di fitness FitLife, con una combinazione di colori blu\".", "describe-the-email-you-want-to-create-then-use-the-generated-prompt-with-your-preferred-ai-model": "Descrivi l'e -mail che desideri creare, quindi usa il prompt generato con il tuo modello AI preferito.", "describe-the-tone-formal-casual-exciting": "Descrivi il tono (formale, casual, eccitante)", "describe-the-type-of-email-you-want-to-create-and-our-ai-will-generate-a-template-for-you": "Descrivi il tipo di e -mail che desideri creare e la nostra intelligenza artificiale genererà un modello per te.", "describe-validation-rules-e-g-required-fields-email-format-character-limits": "Descrivi le regole di validazione (ad es. Campi richiesti, formato e -mail, limiti di carattere).", "describe-your-form": "Descrivi la tua forma", "describe-your-form-purpose-e-g-job-application-form-for-a-marketing-position-event-registration-form-for-a-conference": "Descrivi il tuo scopo del modulo (ad es. \"Modulo di domanda di lavoro per una posizione di marketing\", \"Modulo di registrazione degli eventi per una conferenza\")", "description": "Descrizione", "description-enhanced": "Descrizione migliorata!", "description-tips": "Descrizione Suggerimenti:", "designTab": "Progetto", "detailed-information-about-this-team": "Informazioni dettagliate su questa squadra", "details": "<PERSON><PERSON><PERSON>", "dialogDescription": "Invia l'intervista \"{Role} {Level}\" ai candidati.", "different-teams-can-have-different-permission-sets-based-on-their-needs": "Team diversi possono avere set di autorizzazioni diversi in base alle loro esigenze", "divider": "Divisore", "divider-color": "Colore divisore", "divider-style": "Stile divisore", "divider-width": "Larghezza del divisore", "do": "Fare", "document-any-exceptions-to-standard-security-policies": "Documenta eventuali eccezioni alle politiche di sicurezza standard", "doe": "<PERSON><PERSON>", "domain": "<PERSON>inio", "domain-name": "Nome di dominio", "domain-name-is-required": "È richiesto il nome di dominio.", "dotted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drag-and-drop-a-csv-file-or-click-to-browse": "Trascinare e rilasciare un file CSV o fare clic per sfogliare", "drag-components-here": "Drag componenti qui", "drag-components-to-the-canvas-or-click-to-add-them-to-the-bottom-of-your-email": "Trascina i componenti sulla tela o fai clic per aggiungerli in fondo alla tua e -mail.", "dropdown": "Cadere in picchiata", "e-g-frontend-developer": "per esempio. \nSviluppatore del frontend", "e-g-prd": "per esempio. \nPrd", "e-g-prd-not-the-full-project-name": "(ad esempio \"PRD\", non il nome completo del progetto).", "e-g-react": "per esempio. \nReagire", "e-g-web-development-ai-ml": "ad esempio, sviluppo web, AI/ML", "each-role-contains-a-collection-of-permissions-with-specific-scopes-for-example": "Ogni ruolo contiene una raccolta di autorizzazioni con ambiti specifici. \nPer esempio:", "edit": "Modificare", "edit-0": "Modificare", "edit-description": "Modifica descrizione", "edit-integration": "Modificare l'integrazione", "edit-interview-template": "Modifica il modello di intervista", "edit-permissions": "Modificare le autorizzazioni", "edit-team": "Modifica team", "edit-team-0": "Modifica team", "edit-template": "Modello di modifica", "editIntegrationPage": {"backToIntegrationDetails": "Torna ai dettagli dell'integrazione", "editIntegrationTitle": "Modificare l'integrazione", "integrationDetailsTitle": "Dettagli di integrazione", "integrationNotFound": "Integrazione non trovata.", "loadingIntegration": "Caricamento dell'integrazione ...", "modifySettingsDescription": "Modifica i campi seguenti per aggiornare le impostazioni di integrazione.", "permissionDeniedToastDescription": "Non hai il permesso di modificare questa integrazione.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato", "updateDescription": "Aggiorna la configurazione per {IntegrationName}."}, "editorPage": {"backToGeneratorButton": "<PERSON>na al <PERSON>e", "loadingEditor": "Editor di caricamento ...", "permissionDeniedToastDescription": "Non hai l'autorizzazione per accedere alla pagina del costruttore di posta elettronica.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato"}, "effective-permissions": "Autorizzazioni efficaci", "email": "E-mail", "email-associated-with-your-jira-account": "Email associata al tuo account JIRA", "email-builder": "Email Builder", "email-description": "Descrizione e -mail", "email-field": "Campo e -mail", "email-preview": "Anteprima e -mail", "email-preview-0": "Anteprima e -mail", "emailBuilderPage": {"aiTemplateGenerator": {"aiProcessingToastDescription": "Il tuo modello di posta elettronica viene generato. \nQuesto potrebbe richiedere un momento.", "aiProcessingToastTitle": "AI sta elaborando ...", "emailDescriptionLabel": "Descrizione e -mail (facoltativo)", "emailDescriptionPlaceholder": "Fornire maggiori dettagli per l'IA, ad esempio, \"un'e-mail di benvenuto per nuove iscrizioni, evidenziando le nostre migliori funzionalità e un invito all'azione per esplorare i prodotti\".", "emailSubjectLabel": "Oggetto e -mail", "emailSubjectPlaceholder": "<PERSON> esempio, \"Benvenuti nella nostra newsletter\", \"Annuncio di lancio di nuovi prodotti\"", "enhanceDescriptionButton": "Migliora la descrizione con AI", "errorEnhancingDescriptionToastDescription": "C'è stato un errore che ha migliorato la tua descrizione. \nPer favore riprova.", "errorEnhancingDescriptionToastTitle": "Descrizione di miglioramento degli errori", "errorGeneratingTemplateToastDescription": "C'è stato un errore che genera il tuo modello di posta elettronica. \nPer favore riprova.", "errorGeneratingTemplateToastTitle": "Modello di generazione di errori", "generateTemplateButton": "Generare modello", "generatingTemplateButton": "Modello di generazione ...", "heading": "Generatore di modelli e -mail di AI", "noSubjectError": "Si prega di fornire un argomento e -mail.", "subheading": "Genera bellissimi modelli di posta elettronica con AI in pochi secondi."}, "c-2025-your-company-all-rights-reserved": "© 2025 la tua azienda. \nTutti i diritti riservati.", "click-me": "Clicca a me", "email-image-alt-text": "Immagine e -mail", "email-template-title": "Modello di posta elettronica", "error-loading-template": "Modello di caricamento degli errori", "error-saving-template": "Modello di salvataggio di errore", "permissionDeniedToastDescription": "Non hai l'autorizzazione per accedere alla pagina del costruttore di posta elettronica.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato", "template-loaded": "<PERSON><PERSON> caricato", "template-name-has-been-loaded-successfully": "Il modello \"{name}\" è stato caricato correttamente.", "template-name-has-been-saved-successfully": "Il modello \"{name}\" è stato salvato correttamente.", "template-saved": "<PERSON><PERSON>", "there-was-an-error-loading-your-template": "C'è stato un errore di caricamento del tuo modello.", "there-was-an-error-saving-your-template": "Si è verificato un errore di salvare il modello.", "this-is-a-paragraph-of-text-you-can-edit-this-text-to-add-your-own-content": "Questo è un paragrafo di testo. \nPuoi modificare questo testo per aggiungere i tuoi contenuti.", "viewTemplatesButton": "Visualizza modelli", "your-email-header": "La tua intestazione e -mail"}, "embed-code": "Codice incorporato", "empty": "<PERSON><PERSON><PERSON>", "empty-description": "Descrizione vuota", "empty-prompt": "Prompt vuoto", "en": "🇺🇸 inglese", "end": "FINE", "engineering-team": "Team di ingegneria", "english": "🇺🇸 inglese", "enhance-description": "Migliora la descrizione", "enhance-description-0": "Migliora la descrizione", "enhanced-description": "Descrizione migliorata", "enhanced-description-0": "Descrizione migliorata:", "enhanced-description-applied": "Descrizione migliorata applicata!", "enhancement-failed": "Il miglioramento non è riuscito", "enhancing": "Miglioramento ...", "enter-a-question": "Inserisci una domanda ...", "enter-the-code-from-your-email": "Inserisci il codice dalla tua email", "enter-the-code-from-your-invitation": "Immettere il codice dal tuo invito", "enter-your-email-address-and-well-send-you-a-password-reset-code": "Inserisci il tuo indirizzo email e ti invieremo un codice di reimpostazione della password", "enter-your-email-address-and-well-send-you-a-password-reset-code-0": "Inserisci il tuo indirizzo email e ti invieremo un codice di reimpostazione della password", "enter-your-email-and-access-code-to-start-your-interview": "Inserisci la tua e -mail e il codice di accesso per avviare il tuo colloquio", "enter-your-new-password-and-the-reset-code-we-sent-to-your-email": "Inserisci la tua nuova password e il codice di ripristino che abbiamo inviato alla tua email", "enterprise": "Impresa", "enterprise-features": "Funzionalità aziendali", "enterprise-security": "Sicurezza aziendale", "error": "Errore", "error-adding-members": "Errore che aggiunge membri", "error-assigning-integration": "Errore che assegna l'integrazione:", "error-deleting-user": "Errore eliminazione dell'utente", "error-loading-data": "Errore di caricamento dei dati", "error-loading-integrations": "Integrazioni di caricamento degli errori", "error-loading-knowledge-bases": "Errore di caricamento delle basi di conoscenza:", "error-loading-team-data": "Errore Caricamento dei dati del team", "error-loading-template": "Modello di caricamento degli errori", "error-loading-users": "Errore Caricamento degli utenti", "error-removing-member": "Errore di rimozione del membro", "error-saving-template": "Modello di salvataggio di errore", "error-updating-user-status": "Errore aggiornamento dello stato dell'utente", "error-uploading-file": "File di caricamento degli errori:", "error-verifying-account-status": "Errore di verifica dello stato dell'account", "error-while-fetching-the-templates": "Errore durante il recupero dei modelli", "every-integration-updatetime-days": "<PERSON><PERSON> {0} gior<PERSON>", "everything-worked-perfectly-your-integration-successfully-brought-in-the-data": "Tutto ha funzionato perfettamente. \nLa tua integrazione ha portato con successo i dati.", "example-scope-resolution": "Esempio: risoluzione dell'ambito", "excessive-use-of-permission-overrides-can-make-your-security-model-difficult-to-understand-and-audit-if-you-find-yourself-frequently-using-overrides-consider": "L'uso eccessivo di sovrascrivi di autorizzazione può rendere il tuo modello di sicurezza difficile da comprendere e verificare. \nSe ti trovi frequentemente usando le sovraccarichi, considera:", "experience-level": "Livello di esperienza", "export": "Esportare", "export-data-with-project-scope": "Dati di esportazione con portata del progetto", "export-form-as-document": "Modulo di esportazione come documento", "export-form-configuration": "Configurazione del modulo di esportazione", "export-form-responses": "Risposte del modulo di esportazione", "export-options": "Opzioni di esportazione", "export-your-form-in-different-formats": "Esporta il tuo modulo in diversi formati", "exportCsvFutureUpdate": "L'esportazione in CSV sarà implementata in un aggiornamento futuro.", "exportPdfFutureUpdate": "L'esportazione in PDF verrà implementata in un aggiornamento futuro.", "extra-permissions": "Autorizzazioni extra", "extra-permissions-0": "Autorizzazioni extra", "failed": "Fallito", "failed-0": "Fallito", "failed-to-add-team-members": "Non è riuscito ad aggiungere membri del team.", "failed-to-assign-integration": "Impossibile assegnare l'integrazione", "failed-to-delete-chat-session": "Impossibile eliminare la sessione di chat", "failed-to-delete-the-template-please-try-again": "Impossibile eliminare il modello. \nPer favore riprova.", "failed-to-enhance-description-please-try-again": "Impossibile migliorare la descrizione. \nPer favore riprova.", "integration-metrics": "Metriche di integrazione", "storage-usage": "Utilizzo dello spazio di archiviazione", "storage-usage-title": "Utilizzo dello spazio di archiviazione", "storage-usage-description": "Monitora il tuo consumo di spazio di archiviazione", "normal-usage": "Utilizzo normale", "high-usage": "<PERSON><PERSON><PERSON><PERSON> el<PERSON>", "critical-usage": "<PERSON><PERSON><PERSON><PERSON>", "used": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "available": "Disponibile", "total": "Totale", "loading-storage": "Caricamento utilizzo spazio di archiviazione...", "failed-to-load-storage": "Errore nel caricamento dell'utilizzo dello spazio di archiviazione.", "storage-critical-message": "Critico: Lo spazio di archiviazione è quasi pieno. Considera l'aggiornamento o la pulizia delle integrazioni.", "storage-warning-message": "Avviso: L'utilizzo dello spazio di archiviazione è elevato. Considera di rivedere le tue integrazioni.", "gb": "GB", "percentage-used": "% utilizzato", "failed-to-fetch-integration-metrics": "Impossibile recuperare le metriche di integrazione", "failed-to-fetch-integrations": "Impossibile recuperare le integrazioni", "failed-to-initialize-checkout": "Impossibile inizializzare il checkout", "failed-to-load-data": "Impossibile caricare i dati", "failed-to-load-plans": "Impossibile caricare i piani", "failed-to-load-subscription-details": "Impossibile caricare i dettagli dell'abbonamento", "failed-to-load-subscription-information": "Impossibile caricare le informazioni di abbonamento", "failed-to-load-team-and-member-data": "Impossibile caricare i dati del team e dei membri.", "failed-to-load-team-integrations": "Impossibile caricare le integrazioni del team", "failed-to-remove-team-member": "Impossibile rimuovere il membro del team.", "failed-to-update-usage-tracking": "Impossibile aggiornare il monitoraggio dell'utilizzo", "feb": "<PERSON><PERSON><PERSON>", "file-storage": "Archiviazione file", "file-upload": "Caricamento del file", "file-uploaded": "File caricato", "files": "File", "filter": "Filtro", "filter-by-name": "Filtro per nome ...", "filter-by-role": "Filtrare per ruolo", "filter-by-source": "Filtro per fonte", "filter-by-status": "Filtro per stato", "filter-by-team": "Filtra per squadra", "filter-candidates": "<PERSON><PERSON><PERSON> candi<PERSON>", "filter-templates": "Modelli di filtro", "financial-reports": "Rapporti finanziari", "find-candidates-by-name-email-or-status": "Trova i candidati per nome, e -mail o stato", "find-templates-by-role-type-or-level": "<PERSON>rova modelli per ruolo, tipo o livello", "first-name": "Nome di battesimo", "first-page": "Prima pagina", "font-size": "Dimensione del carattere", "font-weight": "Peso del carattere", "footer": "Piè di pagina", "forgot-your-password": "Hai dimenticato la password?", "form-builder": "Forma Builder", "form-errors": "Errori del modulo:", "form-not-found": "Forma non trovata", "form-submissions-will-be-sent-to-this-url": "Gli invii del modulo verranno inviati a questo URL", "formSavedSuccess": "<PERSON><PERSON>lo salvato con successo!", "forms": "Forme", "fr": "🇫🇷 Français", "francais": "🇫🇷 Français", "free": "<PERSON><PERSON><PERSON><PERSON>", "free-0": "<PERSON><PERSON><PERSON><PERSON>", "free-plan-subscription-limit-reached": "Limite di abbonamento del piano gratuito raggiunto", "ftp-account-password-stored-securely": "Password dell'account FTP (memorizzata in modo sicuro)", "ftp-account-username": "Nome utente dell'account FTP", "ftp-integration": "Integrazione FTP", "ftp-integration-0": "Integrazione FTP", "ftp-not-encrypted": "FTP (non crittografato)", "ftp-server-address": "Indirizzo del server FTP", "ftp-server-port-usually-21": "Porta del server FTP (di solito 21)", "full-name": "Nome e cognome", "full-width": "<PERSON><PERSON><PERSON><PERSON> completa", "generate-ai-prompt": "Generare prompt di intelligenza artificiale", "generate-form": "Generare forma", "generate-template": "Generare modello", "generate-the-interview": "Generare l'intervista", "generated-prompt": "Prompt generato", "generating": "Generare ...", "generating-form": "Forma di generazione ...", "generation-failed": "La generazione non è riuscita", "get-immediate-insights-on-your-performance": "Ottieni approfondimenti immediati sulla tua performance", "get-your-token-here": "Prendi il tuo gettone qui", "global": "Globale", "global-0": "Globale", "global-scope": "Portata globale", "go-to-dashboard": "Vai a Dashboard", "grant-global-permissions-to-teams-unnecessarily": "Concedere le autorizzazioni globali ai team inutilmente", "grant-when-a-user-needs-a-specific-permission-that-isnt-included-in-their-roles-or-teams-but-doesnt-warrant-a-role-change": "Concedi quando un utente ha bisogno di un'autorizzazione specifica che non è inclusa nei loro ruoli o squadre, ma non garantisce un cambiamento di ruolo.", "guidelines-for-implementing-effective-access-control": "Linee guida per l'implementazione di un controllo di accesso efficace", "has-errors": "<PERSON> <PERSON><PERSON>:", "have-questions-about-which-plan-is-right-for-you-our-team-is-happy-to-help": "Hai domande su quale piano è giusto per te? \nIl nostro team è felice di aiutare.", "having-trouble-contact-support-at": "Avere problemi? \nContact support at", "header": "Intestazione", "header-background-color": "Colore di sfondo dell'intestazione", "height": "Altezza", "how-often-to-sync-data-minimum-1-day": "Quanto spesso sincronizzare i dati (minimo 1 giorno)", "how-roles-provide-a-foundation-for-permissions-in-the-system": "Come i ruoli forniscono una base per le autorizzazioni nel sistema", "how-teams-provide-contextual-access-to-resources": "Come i team forniscono accesso contestuale alle risorse", "how-to-use-this-prompt": "Come usare questo prompt:", "icon-color": "Colore icona", "icon-size": "Dimensione dell'icona", "icon-spacing": "Spaziatura delle icone", "identify-and-review-permission-overrides": "Identificare e rivedere il permesso di autorizzazione", "if-a-user-has-the-view-analytics-permission-from": "Se un utente ha l'autorizzazione \"Visualizza analisi\" da:", "image": "<PERSON><PERSON><PERSON><PERSON>", "image-description": "Descrizione dell'immagine", "image-url": "URL di immagine", "implementing-a-more-granular-permission-model": "Implementazione di un modello di autorizzazione più granulare", "important-note": "Nota importante", "in-progress": "In corso", "inactive": "Inattivo", "include-specific-sections-you-want-in-the-email": "Includi sezioni specifiche che desideri nell'e -mail", "include-your-company-or-brand-name": "Includi la tua azienda o il tuo marchio", "include-your-company-or-brand-name-for-customization": "Includi la tua azienda o il marchio per la personalizzazione.", "included-for-all-plans": "Incluso per tutti i piani", "indicate-if-you-want-branding-elements-like-logos-and-theme-colors": "Indica se si desidera elementi di branding come loghi e colori del tema.", "individual-users-can-be-granted-extra-permissions-or-have-inherited-permissions-revoked-to-handle-exceptions": "I singoli utenti possono essere concessi autorizzazioni extra o avere le autorizzazioni ereditarie revocate per gestire le eccezioni.", "information-about-this-interview": "Informazioni su questa intervista", "instant-feedback": "Feedback is<PERSON><PERSON><PERSON>", "instant-feedback-0": "Feedback is<PERSON><PERSON><PERSON>", "instead-of-assigning-individual-permissions-to-each-user-you-can-assign-roles-that-contain-predefined-sets-of-permissions-making-user-management-more-efficient": "Invece di assegnare singole autorizzazioni a ciascun utente, è possibile assegnare ruoli che contengono serie di autorizzazioni predefinite, rendendo la gestione degli utenti più efficiente.", "instructions": "Istruzioni", "integration": "Integrazione", "integration-assigned": "Integrazione assegnata", "integration-has-been-successfully-assigned-to-currentteam-name": "L'integrazione è stata assegnata correttamente a {0}.", "integration-name": "Nome dell'integrazione", "integration-s-total": "Integrazione / i totale", "integration-status-guide": "Guida allo stato di integrazione", "integrationPage": {"backToIntegrations": "Torna alle integrazioni", "integrationDescription": "Visualizza la configurazione e lo stato di questa integrazione.", "integrationNameTitle": "{IntegrationName}", "integrationNotFoundDescription": "L'integrazione richiesta non esiste o è stata rimossa.", "integrationNotFoundTitle": "Integrazione non trovata", "loadingIntegration": "Caricamento dell'integrazione ...", "overviewTitle": "Panoramica dell'integrazione", "permissionDeniedToastDescription": "Non hai il permesso di visualizzare le integrazioni.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato"}, "integrations": "Integrazioni", "interview-access": "Accesso al colloquio", "interview-candidates": "Intervista ai candidati", "interview-details": "Dettagli dell'intervista", "interview-id-or-user-id-is-missing": "Manca l'ID del colloquio o l'ID utente.", "interview-not-found": "Intervista non trovata", "interview-questions": "Domande di intervista", "interview-templates": "Modelli di intervista", "interview-type": "Tipo di intervista", "interviewTemplatesPage": {"addCandidateButton": "Aggiungi candidato", "all-levels": "<PERSON><PERSON> i livelli", "all-types": "<PERSON>tti i tipi", "behavioral": "Comportamentale", "browseFilesButton": "Sfoglia i file", "candidatesCount": "{count} candidati", "completedCount": "{Conte} completato", "created": "Creato:", "createdBy": "<PERSON><PERSON><PERSON> <PERSON>:", "csvDropzoneText": "Trascinare e rilasciare un file CSV o fare clic per sfogliare", "csvFormatHint": "Il file CSV dovrebbe avere 2 colonne: \"nome completo\" e \"email\"", "csvUploadTab": "Caricamento CSV", "deleteButton": "Eliminare", "editButton": "Modificare", "emailPlaceholder": "E-mail", "errorDeletingTemplateToastDescription": "Impossibile eliminare il modello. \nPer favore riprova.", "errorDeletingTemplateToastTitle": "Errore", "errorSendingCandidates": "Errore di invio dei candidati:", "fileUploadedToastDescription": "File CSV elaborato correttamente. \nLe interviste verranno inviate ai candidati.", "fileUploadedToastTitle": "File caricato", "filterTemplatesDescription": "<PERSON>rova modelli per ruolo, tipo o livello", "filterTemplatesTitle": "Modelli di filtro", "fullNamePlaceholder": "Nome e cognome", "invitationsSentSuccessToast": "Inviti inviati con successo!", "junior": "Junior", "lead": "<PERSON><PERSON><PERSON>", "level": "<PERSON><PERSON>", "loading-interviews": "Caricamento di modelli di intervista ...", "manualEntryTab": "Ingresso manuale", "mid-level": "Medio-livello", "missingFileToastDescription": "Carica un file CSV con indirizzi e -mail.", "missingFileToastTitle": "File mancante", "missingInformationToastDescriptionEmails": "Inserisci almeno un indirizzo email.", "missingInformationToastTitle": "Informazioni mancanti", "missingTemplateToastDescription": "Seleziona un modello di intervista.", "missingTemplateToastTitle": "<PERSON><PERSON> man<PERSON>", "mixed": "<PERSON><PERSON>", "moreQuestions": "{Conte} <PERSON><PERSON>", "newTemplateButton": "Nuovo modello", "noTemplatesFoundDescription": "Crea il tuo primo modello di intervista per iniziare.", "noTemplatesFoundTitle": "<PERSON><PERSON><PERSON> trovato", "pageDescription": "Crea e gestisci modelli di intervista da inviare ai candidati", "pageTitle": "Modelli di intervista", "permissionDeniedToastDescription": "Non hai il permesso di accedere alla pagina delle interviste.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato", "questionsLabel": "Domande: {count}", "search": "Ricerca", "search-by-role-or-tech": "Cerca per ruolo o tecnologia", "select-level": "<PERSON><PERSON><PERSON><PERSON>llo", "select-type": "Selezionare il tipo", "selectedFile": "Selezionato: {fileName}", "sendButton": "Inviare", "sendInterviewDialogDescription": "Invia l'intervista \"{Role} {Level}\" ai candidati.", "sendInterviewDialogTitle": "Invita i candidati", "sendInvitationsButton": "Invia inviti", "senior": "Anziano", "serverErrorToastDescription": "Qualcosa è andato storto mentre eliminava il modello.", "serverErrorToastTitle": "Errore del server", "system-design": "Progettazione del sistema", "techStackLabel": "Stack Tech:", "technical": "Tecnico", "templateDeletedToastDescription": "\"Role} {<PERSON><PERSON>}\" Il modello è stato eliminato.", "templateDeletedToastTitle": "<PERSON><PERSON> eliminato", "type": "Tipo", "unexpectedError": "Si è verificato un errore imprevisto.", "uploadAndSendButton": "Carica e invia", "uploadCsvFileLabel": "Carica il file CSV", "uploadFailedToastDescription": "C'è stato un errore che elabora il file. \nPer favore riprova.", "uploadFailedToastTitle": "Caricamento non riuscito", "viewButton": "Visualizzazione"}, "interviews": "Interviste", "invalid-email-or-access-code-please-check-your-credentials-and-try-again": "Email o codice di accesso non validi. \nControlla le tue credenziali e riprova.", "invalid-email-or-access-code-please-check-your-credentials-and-try-again-0": "Email o codice di accesso non validi. \nControlla le tue credenziali e riprova.", "invitations-sent-successfully": "Inviti inviati con successo!", "invite-candidates": "Invita i candidati", "it-looks-like-no-messages-have-been-sent-yet": "Sembra che non siano stati ancora inviati messaggi.", "jan": "Gennaio", "jane-smith": "<PERSON>", "jira": "<PERSON><PERSON>", "jira-integration": "Integrazione di Jira", "john": "<PERSON>", "john-doe": "<PERSON>", "join-100-users": "Unisciti a 100 utenti", "jun": "<PERSON><PERSON>", "junior": "Junior", "junior-0-2-years": "Junior (0-2 anni)", "key": "%", "key-0": "→", "knowledge-bases": "Basi di conoscenza", "knowledge-bases-0": "Basi di conoscenza", "last-name": "Cognome", "last-page": "Ultima pagina", "last-rerun": "Ultimo retro", "last-updated": "Ultimo <PERSON>rnamento:", "lead": "<PERSON><PERSON><PERSON>", "leave-blank-to-keep-current-password": "<PERSON><PERSON> vuoto per mantenere la password corrente", "leave-blank-to-keep-current-token": "<PERSON><PERSON> vuoto per mantenere il token attuale", "leave-unused-roles-assigned-to-users": "Lascia ruoli inutilizzati assegnati agli utenti", "left": "Sinistra", "level": "<PERSON><PERSON>", "lighter": "<PERSON><PERSON> leg<PERSON>o", "limit-team-permissions-to-necessary-resources": "Limitare le autorizzazioni del team alle risorse necessarie", "line-height": "Altezza della linea", "link-image": "Immagine di collegamento", "link-url": "Link URL", "linkedin-profile-optional": "Profilo <PERSON>edIn (opzionale)", "list": "Lista", "list-items": "Elencatura elencatura", "list-type": "Tipo di elenco", "listening": "Ascolt<PERSON>", "loading": "Caricamento", "loading-candidates": "Caricamento dei candidati ...", "loading-integration-metrics": "Caricamento di metriche di integrazione ...", "loading-integrations": "Caricamento di integrazioni ...", "loading-interviews": "Caricamento di interviste ...", "loading-knowledge-bases": "Caricamento di basi di conoscenza ...", "loading-subscription-details": "Caricamento dei dettagli dell'abbonamento ...", "loading-team": "Team di caricamento ...", "loading-team-details": "Caricamento dei dettagli della squadra ...", "loading-team-members": "Caricamento dei membri del team ...", "loading-template": "Modello di caricamento ...", "loading-users": "Caricamento degli utenti ...", "logo": "Logo", "logo-preview": "Anteprima del logo", "manage-and-connect-your-enterprise-data-from-a-variety-of-sources-all-in-one-place": "Gestisci e collega i tuoi dati aziendali da una varietà di fonti, il tutto in un unico posto.", "manage-billing": "Gestire la fatturazione", "manage-integrations": "Gestisci integrazioni", "manage-members": "Gestisci i membri", "manage-members-0": "Gestisci i membri", "manage-members-of-this-team": "Gestisci i membri di questo team.", "manage-profile-with-self-scope": "Gestisci il profilo con l'automanificazione", "manage-your-organizations-users": "Gestisci gli utenti della tua organizzazione.", "manager-role": "<PERSON><PERSON><PERSON> del manager", "manual-entry": "Ingresso manuale", "mar": "Mar", "margin": "<PERSON><PERSON><PERSON>", "may": "Maggio", "member": "Membro", "member-removed": "<PERSON><PERSON><PERSON>", "members": "<PERSON><PERSON><PERSON>", "members-0": "<PERSON><PERSON><PERSON>", "members-added": "<PERSON><PERSON><PERSON> aggiu<PERSON>", "members-of-team": "<PERSON><PERSON><PERSON> <PERSON> {teamname}", "mention-any-color-preferences": "Menzionare qualsiasi preferenze di colore", "mention-any-specific-images-or-buttons-you-want": "Menziona immagini o pulsanti specifici che desideri", "mention-if-you-need-file-uploads-e-g-resumes-images-documents": "Menzionare se hai bisogno di carichi di file (ad es. Ripresi, immagini, documenti).", "mention-if-you-want-special-elements-like-social-icons": "Menziona se vuoi elementi speciali come le icone sociali", "mention-required-fields-e-g-name-email-phone-number": "Menzionare i campi richiesti (ad es. Nome, e -mail, numero di telefono).", "mid-level": "Medio-livello", "mid-level-3-5-years": "<PERSON><PERSON> (3-5 anni)", "missing-file": "File mancante", "missing-information": "Informazioni mancanti", "missing-template": "<PERSON><PERSON> man<PERSON>", "mixed": "<PERSON><PERSON>", "monitor-your-improvement-over-time": "Monitora il tuo miglioramento nel tempo", "monthly": "<PERSON><PERSON><PERSON>", "more-options": "Più opzioni", "more-questions": "<PERSON><PERSON> domande", "multi-layered-permission-system": "Sistema di autorizzazione a più livelli", "must-avoid": "Deve evitare!", "name": "Nome", "name-must-be-at-least-2-characters": "Il nome deve essere almeno 2 caratteri.", "name-must-be-at-least-2-characters-0": "Il nome deve essere almeno 2 caratteri.", "needs-attention": "Ha bisogno di attenzione", "new-chat": "Nuova chat", "new-integration": "Nuova integrazione", "new-password": "Nuova password", "new-template": "Nuovo modello", "newIntegrationPage": {"backToIntegrations": "Torna alle integrazioni", "createIntegrationDescription": "Collegare le origini dati per automatizzare la sincronizzazione dei dati.", "createIntegrationTitle": "Crea nuova integrazione", "fillFormDescription": "Compila il modulo sottostante per configurare l'integrazione.", "integrationDetailsTitle": "Dettagli di integrazione"}, "next": "Prossimo", "next-page": "Pagina successiva", "no": "NO", "no-active-subscription": "Nessun abbonamento attivo", "no-active-subscription-0": "Nessun abbonamento attivo", "no-available-users-found": "Nessun utente disponibile trovato.", "no-candidates-added": "<PERSON><PERSON><PERSON> candidato aggiunto.", "no-candidates-found": "<PERSON><PERSON><PERSON> candidato trovato", "no-components-added-yet": "Nessun componente ancora aggiunto", "no-configuration-available": "Nessuna configurazione disponibile.", "no-credit-card-required": "✓ Nessuna carta di credito richiesta", "no-description": "Nessuna descrizione", "no-description-provided": "Nessuna descrizione fornita", "no-description-provided-0": "Nessuna descrizione fornita", "no-forms-yet": "Nessuna forma ancora", "no-integrations-available": "Nessuna integrazione disponibile", "no-integrations-found": "Nessuna integrazione trovata", "no-integrations-found-0": "Nessuna integrazione trovata.", "no-logo-uploaded": "Nessun logo caricato", "no-members-found": "<PERSON><PERSON><PERSON> membro trovato.", "no-messages-found": "<PERSON><PERSON><PERSON> messaggio trovato", "no-permissions": "Nessuna autorizzazione", "no-permissions-assigned": "Nessuna autorizzazione assegnata", "no-questions-added-yet-add-some-questions-to-create-your-interview-template": "Non sono ancora state aggiunte domande. \nAggiungi alcune domande per creare il tuo modello di intervista.", "no-questions-available-for-this-interview": "Nessuna domanda disponibile per questa intervista", "no-team-members-found": "<PERSON><PERSON>un membro del team trovato", "no-teams-found": "Nessuna squadra trovata", "no-teams-found-0": "Nessuna squadra trovata.", "no-teams-match-your-search-criteria-or-no-teams-have-been-created-yet": "Nessuna squadra corrisponde ai tuoi criteri di ricerca o nessuna squadra è stata ancora creata.", "normal": "Normale", "not-updated": "Non aggiornato:", "not-updated-0": "Non aggiornato", "not-updated-1": "Non aggiornato", "numbered-list": "Elenco numerato", "of-teams-have-members": "di team hanno membri", "open-menu": "<PERSON><PERSON> aperto", "open-menu-0": "<PERSON><PERSON> aperto", "or-select-components-from-the-sidebar": "oppure seleziona i componenti dalla barra laterale", "organize-teams-based-on-functional-areas": "Organizza i team basati su aree funzionali", "our-security-system-uses-a-multi-layered-approach-to-permissions-allowing-for-fine-grained-access-control-while-maintaining-flexibility-and-ease-of-management": "Il nostro sistema di sicurezza utilizza un approccio a più livelli alle autorizzazioni, consentendo il controllo degli accessi a grana fine mantenendo la flessibilità e la facilità di gestione.", "overview": "Panoramica", "overview-0": "Panoramica", "padding": "Imbottitura", "password": "Password", "password-is-required": "È richiesta la password.", "paste-it-to-chatgpt-claude-or-your-preferred-ai-model": "<PERSON>oll<PERSON> a Chatgpt, <PERSON> o al tuo modello AI preferito", "payment-successful": "Pagamento di successo!", "pending": "In attesa di", "permission-applies-across-the-entire-system-to-all-resources-of-the-relevant-type": "L'autorizzazione si applica in tutto il sistema, a tutte le risorse del tipo pertinente.", "permission-applies-only-to-resources-created-by-or-directly-assigned-to-the-user": "L'autorizzazione si applica solo alle risorse create o direttamente assegnate all'utente.", "permission-applies-only-to-resources-owned-by-or-associated-with-the-users-team-s": "L'autorizzazione si applica solo alle risorse di proprietà o associate al team dell'utente.", "permission-applies-only-to-specific-projects-regardless-of-team-ownership": "L'autorizzazione si applica solo a progetti specifici, indipendentemente dalla proprietà del team.", "permission-denied": "<PERSON><PERSON><PERSON> negato", "permission-denied-0": "<PERSON><PERSON><PERSON> negato", "permission-denied-1": "<PERSON><PERSON><PERSON> negato", "permission-inheritance-flow": "Flusso di eredità del permesso", "permission-model-overview": "Panoramica del modello di autorizzazione", "permission-overrides": "Sostituzione del permesso", "permission-resolution": "Risoluzione del permesso", "permission-scopes": "Ambiti di autorizzazione", "permission-scopes-define-the-boundaries-within-which-a-permission-applies-they-allow-for-fine-grained-control-over-what-resources-a-user-can-access-with-a-given-permission": "Gli ampli di autorizzazione definiscono i confini entro i quali si applica un'autorizzazione. \nConsentono il controllo a grana fine sulle risorse che un utente può accedere con una determinata autorizzazione.", "permissions": "Autorizzazioni", "permissions-apply-only-to-resources-owned-by-the-team-this-is-the-most-common-scope-for-team-permissions": "Le autorizzazioni si applicano solo alle risorse di proprietà del team. \nQuesto è l'ambito più comune per le autorizzazioni di squadra.", "permissions-assigned": "autorizzazioni assegnate", "permissions-assigned-to-this-team-are-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users": "Le autorizzazioni assegnate a questo team sono concesse a tutti i membri del team, se non esplicitamente revocati per utenti specifici.", "permissions-assigned-to-this-team-will-be-granted-to-all-team-members-unless-explicitly-revoked-for-specific-users": "Le autorizzazioni assegnate a questo team saranno concesse a tutti i membri del team, se non esplicitamente revocati per utenti specifici.", "permissions-granted-to-all-members-of-the-team-typically-scoped-to-team-resources": "Le autorizzazioni concesse a tutti i membri del team, in genere sono state convocate alle risorse del team", "permissions-may-be-limited-to-specific-projects-that-the-team-is-working-on-even-if-those-projects-are-shared-with-other-teams": "Le autorizzazioni possono essere limitate a progetti specifici su cui il team sta lavorando, anche se tali progetti sono condivisi con altri team.", "personal-info": "Informazioni personali", "pick-a-color": "Scegli un colore", "plan-features": "Funzionalità del piano", "platform": "Piattaforma", "please-complete-onboarding-first": "Si prega di completare prima onboarding", "please-describe-your-form-purpose": "Descrivi il tuo scopo del modulo", "please-enter-a-description-of-the-email-you-want-to-create": "Inserisci una descrizione dell'e -mail che desideri creare.", "please-enter-a-description-to-enhance": "Inserisci una descrizione per migliorare.", "please-enter-a-form-description-first": "Inserisci prima una descrizione del modulo", "please-enter-a-prompt-to-generate-a-template": "Inserisci un prompt per generare un modello.", "please-enter-a-valid-email-address": "Si prega di inserire un indirizzo email valido.", "please-enter-a-valid-url": "Inserisci un URL valido.", "please-enter-both-email-and-access-code": "Inserisci sia il codice e -mail che l'accesso", "please-fill-in-all-required-fields-before-saving": "Si prega di compilare tutti i campi richiesti prima di salvare.", "please-fill-in-the-following-fields": "Si prega di compilare i seguenti campi:", "please-select-an-interview-template": "Seleziona un modello di intervista.", "please-try-refreshing-the-page": "Prova a rinfrescare la pagina", "please-upload-a-csv-file-with-email-addresses": "Carica un file CSV con indirizzi e -mail.", "port": "Porta", "port-is-required": "È richiesto la porta.", "practice-your-interview-skills-with-our-ai-interviewer-get-real-time-feedback-and-improve-your-chances-of-landing-your-dream-job": "Semplifica il tuo processo di assunzione. \nAutomatizza lo screening dei candidati e identifica rapidamente la misura perfetta più veloce, più facile e con una migliore precisione.", "preferences": "Prefer<PERSON><PERSON>", "preview": "Anteprima", "preview-and-send": "Anteprima", "preview-how-your-email-will-look-to-recipients": "Anteprima come la tua e -mail apparirà ai destinatari", "previewPage": {"editTemplateButton": "Modello di modifica", "editToGenerateHtml": "Modifica per generare HTML", "errorTitle": "Errore", "failedToLoadTemplate": "Impossibile caricare il modello. \nPer favore riprova più tardi.", "goBackToTemplates": "<PERSON>na ai modelli", "loadingEditor": "Editor di caricamento ...", "noHtmlPreview": "Nessuna anteprima HTML disponibile per questo modello.", "templateNotFoundDescription": "Il modello che stai cercando non esiste o è stato eliminato.", "templateNotFoundTitle": "<PERSON>lo non trovato"}, "previewTab": "Anteprima", "previous": "Precedente", "previous-30-days": "Precedenti 30 giorni", "previous-7-days": "Precedenti 7 giorni", "previous-page": "<PERSON><PERSON><PERSON>e", "primary-button": "Pulsante primario", "primary-color": "Colore primario", "principle-of-least-privilege": "Principio di minimo privilegio", "priority-24-7-support": "Supporto prioritario 24/7", "product-backlog": "Backlog del prodotto", "professional": "Professionale", "project": "Progetto", "project-is-required": "È richiesto il progetto.", "project-key": "Chiave di progetto", "project-key-0": "Chiave di progetto", "project-scope": "Portata del progetto", "prompt": "<PERSON><PERSON>", "prompt-copied": "Rapido copiato!", "prompt-tips": "Suggerimenti rapidi:", "public-form-url": "Forma pubblica URL", "questions": "<PERSON><PERSON><PERSON>", "quick-actions": "Azioni rapide", "radio-group": "Gruppo radio", "real-time-analysis": "<PERSON><PERSON><PERSON> in tempo reale", "realistic-experience": "Esperienza realistica", "recent": "<PERSON><PERSON>", "recent-activity": "Attività recente", "redirecting-to-checkout": "Reindirizzamento al checkout ...", "redirecting-you-to-your-interview": "Reindirizzandoti alla tua intervista ...", "refresh": "Rinfrescare", "refreshing": "Rinfrescante", "regenerate": "R<PERSON>ner<PERSON>", "regular-security-audits": "Audit di sicurezza regolari", "regularly-review-and-audit-role-assignments": "Rivedere regolarmente e assegnazioni di ruoli di audit", "regularly-review-team-memberships": "Rivedere regolarmente le iscrizioni al team", "remove": "Rimuovere", "remove-column": "Rimuovere la colonna", "remove-from-team": "<PERSON><PERSON><PERSON> dalla squadra", "remove-permissions-for-inactive-users": "Rimuovere le autorizzazioni per utenti inattivi", "remove-row": "Rimuovere la riga", "remove-team-member": "Rimuovere il membro del team", "remove-unnecessary-roles-when-job-functions-change": "Rimuovere i ruoli non necessari quando le funzioni lavorative cambiano", "removes-any-permissions-explicitly-revoked-for-the-user": "Rimuove eventuali autorizzazioni revocate esplicitamente per l'utente", "reset-code": "RESET CODICE", "reset-password": "Reimpostare la password", "resource-scope": "Ambito di risorse", "responses": "Risposte", "retry": "<PERSON><PERSON><PERSON><PERSON>", "retry-integration": "Retry Integration", "retrying": "Riprovare ...", "return-to-dashboard": "Torna a Dashboard", "return-to-interviews": "<PERSON><PERSON><PERSON> alle interviste", "return-to-templates": "<PERSON>na ai modelli", "review-user-role-assignments-for-appropriateness": "Rivedi le assegnazioni del ruolo dell'utente per l'adeguatezza", "reviewing-and-adjusting-your-role-definitions": "Rivedere e regolare le definizioni del ruolo", "revoked-permissions": "Autorizzazioni revocate", "revoked-permissions-0": "Autorizzazioni revocate", "right": "<PERSON><PERSON><PERSON>", "robert-johnson": "<PERSON>", "role": "<PERSON><PERSON><PERSON>", "role-0": "<PERSON><PERSON><PERSON>", "role-assignment": "Assegnazione del ruolo", "role-based-access-control": "Controllo dell'accesso basato sul ruolo", "role-permissions": "Autorizzazioni di ruolo", "role-structure": "Struttura del ruolo", "roles": "<PERSON><PERSON><PERSON>", "roles-and-permissions": "<PERSON><PERSON><PERSON>", "roles-are-defined-at-the-system-level-and-cannot-be-created-or-modified-by-regular-users-contact-your-system-administrator-if-you-need-a-new-role-or-modifications-to-existing-roles": "I ruoli sono definiti a livello di sistema e non possono essere creati o modificati da utenti normali. \nContatta l'amministratore di sistema se hai bisogno di un nuovo ruolo o modifiche ai ruoli esistenti.", "roles-are-predefined-sets-of-permissions-that-represent-common-job-functions-or-responsibility-levels-within-your-organization-each-user-can-be-assigned-one-or-more-roles": "I ruoli sono serie predefinite di autorizzazioni che rappresentano funzioni lavorative comuni o livelli di responsabilità all'interno dell'organizzazione. \nA ciascun utente può essere assegnato uno o più ruoli.", "roles-ensure-that-users-with-similar-responsibilities-have-consistent-access-rights-reducing-the-risk-of-permission-inconsistencies": "I ruoli assicurano che gli utenti con responsabilità simili abbiano diritti di accesso coerenti, riducendo il rischio di incoerenze di autorizzazione.", "roles-provide-a-clear-structure-for-access-rights-making-it-easier-to-audit-who-has-access-to-what-and-why-they-have-that-access": "I ruoli forniscono una chiara struttura per i diritti di accesso, rendendo più facile da controllare chi ha accesso a cosa e perché hanno quell'accesso.", "rows-per-page": "Righe per pagina:", "save-20": "(Risparmia 20%)", "save-changes": "Salva le modifiche", "save-integration-settings": "Salva le impostazioni di integrazione", "save-template": "Salva modello", "saveButton": "<PERSON><PERSON>", "scalability": "Scalabilità", "scope-examples": "Esempi di ambito", "scope-hierarchy-broadest-to-narrowest": "Gerarchia di portata (più ampia a stretta)", "scope-resolution": "Risoluzione dell'ambito", "scope-types": "Tipi di portata", "scopes": "<PERSON><PERSON><PERSON>", "score": "Punt<PERSON>", "search": "Ricerca", "search-0": "Ricerca", "search-by-name-or-email": "Cerca per nome o e -mail ...", "search-by-role-or-tech": "Cerca per ruolo o tecnologia ...", "search-members": "Membri di ricerca ...", "search-settings": "Impostazioni di ricerca", "search-users": "Cerca utenti ...", "secondary-button": "Pulsante secondario", "secure-connection": "Connessione sicura", "security-best-practices": "Best practice di sicurezza", "security-system": "Sistema di sicurezza", "select-alignment": "Seleziona l'allineamento", "select-connection-type": "Selezionare il tipo di connessione", "select-experience-level": "Seleziona il livello di esperienza", "select-level": "<PERSON><PERSON><PERSON><PERSON>llo", "select-list-type": "Seleziona il tipo di elenco", "select-role": "Seleziona il ruolo", "select-status": "Seleziona Stato", "select-style": "Seleziona stile", "select-team": "Seleziona squadra", "select-type": "Selezionare il tipo", "select-users-to-add-to-the": "Seleziona gli utenti da aggiungere al file", "select-weight": "Seleziona peso", "select-which-knowledge-bases-to-search-when-performing-searches": "Seleziona quali basi di conoscenza da cercare durante l'esecuzione delle ricerche", "select-width": "<PERSON><PERSON><PERSON><PERSON>", "send": "Inviare", "send-invitations": "Invia inviti", "send-reset-code": "Invia il codice di ripristino", "send-reset-code-0": "Invia il codice di ripristino", "sendEmailDialog": {"cancelButton": "Cancellare", "description": "Send your email template to a recipient. \nCompila i dettagli seguenti.", "emailContentError": "Non è stato possibile generare contenuti e -mail", "fromEmailLabel": "Dall'e -mail", "fromEmailPlaceholder": "<EMAIL>", "fromNameLabel": "Dal nome", "fromNamePlaceholder": "Il tuo nome o azienda o e -mail", "htmlGenerationError": "Errore generazione di e -mail HTML. \nPer favore riprova.", "recipientSubjectRequiredError": "Sono richiesti e -mail e soggetto del destinatario", "sendButton": "Invia email", "sendFailToastDescription": "Si è verificato un errore di inviare la tua email. \nPer favore riprova.", "sendFailToastTitle": "Impossibile inviare e -mail", "sendSuccessToastDescription": "L'e -mail è stata inviata a {ricettesemail}", "sendSuccessToastTitle": "Email inviata con successo", "sendingButton": "Invio ...", "subjectLabel": "<PERSON><PERSON><PERSON>", "subjectPlaceholder": "Oggetto e -mail", "title": "Invia email", "toLabel": "A", "toPlaceholder": "<EMAIL>"}, "senior": "Anziano", "senior-5-years": "Senior (5 anni)", "server": "Server", "server-0": "Server:", "server-error": "Errore del server", "server-ip": "Server IP", "server-ip-is-required": "È richiesto IP del server.", "settings": "Impostazioni", "settingsTab": "Impostazioni", "sftp-secure": "SFTP (Secure)", "share": "Condividere", "share-your-form": "Condividi il tuo modulo", "share-your-form-with-others-to-collect-responses": "Condividi il tuo modulo con gli altri per raccogliere le risposte", "shared-with-me": "Condiviso con me", "showing": "Esibizione", "simplified-management": "Gestione semplificata", "simulates-real-interview-scenarios": "Simula i veri scenari di intervista", "social-icons": "Icone sociali", "social-media-links": "Collegamenti sui social media", "software-engineer": "Ingegnere del software", "solid": "Solido", "some-team-permissions-may-have-global-scope-allowing-team-members-to-perform-actions-across-the-entire-system": "Alcune autorizzazioni di team possono avere un ambito globale, consentendo ai membri del team di eseguire azioni in tutto il sistema.", "something-went-wrong": "Qualcosa è andato storto.", "something-went-wrong-0": "Qualcosa è andato storto!", "something-went-wrong-please-try-again": "Qualcosa è andato storto. \nPer favore riprova.", "something-went-wrong-the-integration-couldnt-fetch-the-data-properly": "Qualcosa è andato storto. \nL'integrazione non ha potuto recuperare i dati correttamente.", "something-went-wrong-while-deleting-the-template": "Qualcosa è andato storto mentre eliminava il modello.", "source-type": "Tipo di origine", "specify-form-actions-e-g-email-responses-save-to-database-integrate-with-apis": "Specificare le azioni del modulo (ad es. Risposte e -mail, salvare nel database, integrare con le API).", "specify-if-you-need-auto-confirmation-emails-or-notifications": "Specificare se sono necessarie e-mail o notifiche auto-conforazioni.", "specify-the-purpose-of-the-form-job-application-event-registration-feedback-etc": "Specificare lo scopo del modulo (domanda di lavoro, registrazione degli eventi, feedback, ecc.).", "specify-the-type-of-email-welcome-newsletter-promotion-etc": "Specificare il tipo di e -mail (benvenuto, newsletter, promozione, ecc.)", "standardization": "Standardizzazione", "start-free-trial": "Inizia la prova gratuita", "start-growing-your-business-quickly": "Inizia a far crescere rapidamente la tua attività", "status": "Stato", "status-0": "Stato", "status-1": "Stato", "subscribe-now": "<PERSON><PERSON><PERSON><PERSON><PERSON> ora", "successfully-processed": "Elaborato con successo", "support": "Supporto", "switch-language": "Switch Language", "syncing": "Sincronizzazione", "system-design": "Progettazione del sistema", "table": "<PERSON><PERSON><PERSON>", "table-headers": "Testate da tavolo", "table-rows": "<PERSON><PERSON><PERSON> da tabella", "tailored-pricing-for-your-specific-needs": "<PERSON>zzi personalizzati per le tue esigenze specifiche", "tailored-to-your-needs": "Su misura per le tue esigenze", "team": "Squadra", "team-0": "Squadra", "team-1": "Squadra", "team-activity-rate": "Tasso di attività di squadra", "team-activity-tracking-will-be-available-in-a-future-update": "Il monitoraggio delle attività del team sarà disponibile in un futuro aggiornamento.", "team-based-permissions": "Autorizzazioni basate sul team", "team-description-is-required": "È richiesta la descrizione del team.", "team-details": "Dettagli della squadra", "team-details-0": "Dettagli della squadra", "team-information": "Informazioni sul team", "team-information-and-statistics": "Informazioni e statistiche del team", "team-integrations": "Integrazioni di squadra", "team-management": "Gestione del team", "team-members": "Membri del team", "team-members-0": "Membri del team:", "team-members-1": "membri del team", "team-name": "Nome della squadra", "team-name-is-required": "È richiesto il nome del team.", "team-not-found": "Squadra non trovata", "team-permission-scopes": "Accopi per autorizzazione della squadra", "team-permissions": "Autorizzazioni di squadra", "team-permissions-0": "Autorizzazioni di squadra", "team-permissions-1": "Autorizzazioni di squadra:", "team-permissions-2": "Autorizzazioni di squadra", "team-permissions-3": "Autorizzazioni di squadra", "team-permissions-are-typically-scoped-to-the-teams-resources-but-they-can-also-have-different-scope-types": "Le autorizzazioni del team sono in genere ammesse alle risorse del team, ma possono anche avere diversi tipi di ambito:", "team-scope": "Ambito di squadra", "team-status": "Stato della squadra", "team-structure": "Struttura della squadra", "team-updated": "Team aggiornato", "team-vs-role-permissions": "Team vs. Autorizzazioni di ruolo", "teams": "<PERSON><PERSON>", "teams-and-permissions": "Squadre e autorizzazioni", "teams-or-groups-allow-you-to-organize-users-and-grant-permissions-based-on-the-resources-they-need-to-access-teams-are-particularly-useful-for-departmental-or-project-based-access-control": "I team (o gruppi) consentono di organizzare gli utenti e concedere le autorizzazioni in base alle risorse di cui hanno bisogno per accedere. \nI team sono particolarmente utili per il controllo dell'accesso dipartimentale o basato su progetti.", "tech-stack": "Stack Tech:", "tech-stack-0": "Stack tecnologico", "technical": "Tecnico", "tell-us-about-yourself": "Raccontaci di te", "tell-us-more-about-yourself-to-get-started": "Raccontaci di più su di te per iniziare", "tell-us-what-kind-of-form-you-need-and-our-ai-will-generate-it-for-you": "Dicci di che tipo di forma hai bisogno e la nostra AI lo genererà per te", "template-deleted": "<PERSON><PERSON> eliminato", "template-generated": "<PERSON>lo generato!", "template-loaded": "<PERSON><PERSON> caricato", "template-name-has-been-loaded-successfully": "Il modello \"{0}\" è stato caricato correttamente.", "template-name-has-been-saved-successfully": "Il modello \"{0}\" è stato salvato con successo.", "template-not-found": "<PERSON>lo non trovato", "template-saved": "<PERSON><PERSON>", "template-updated": "<PERSON><PERSON>", "templatesPage": {"cancelButton": "Cancellare", "componentsCount": "{count} Componenti", "createFirstTemplateButton": "Crea il tuo primo modello", "createNewTemplateButton": "Crea nuovo modello", "deleteAlertDialogDescription": "Questa azione non può essere annullata. \nQuesto eliminerà permanentemente il modello e lo rimuoverà dai nostri server.", "deleteAlertDialogTitle": "Sei sicuro?", "deleteButton": "Eliminare", "deleteErrorToastDescription": "Impossibile eliminare il modello. \nPer favore riprova.", "deleteMenuItem": "Eliminare", "duplicateMenuItem": "Dup<PERSON><PERSON>", "editMenuItem": "Modificare", "editTooltip": "Modifica questo modello", "emailTemplatesTitle": "Modelli di posta elettronica", "errorToastTitle": "Errore", "failedToLoadTemplates": "Impossibile caricare i modelli. \nPer favore riprova più tardi.", "noDescription": "Nessuna descrizione", "noTemplatesFoundDescription": "Non hai ancora creato alcun modello di posta elettronica.", "noTemplatesFoundTitle": "<PERSON><PERSON><PERSON> trovato", "permissionDeniedToastDescription": "Non hai l'autorizzazione per accedere alla pagina del costruttore di posta elettronica.", "permissionDeniedToastTitle": "<PERSON><PERSON><PERSON> negato", "previewMenuItem": "Anteprima", "previewTooltip": "Anteprima questo modello", "templateDeletedToastDescription": "Il modello è stato eliminato con successo.", "templateDeletedToastTitle": "<PERSON><PERSON> eliminato"}, "text-area": "Area di testo", "text-block": "<PERSON><PERSON> di testo", "text-color": "Colore di testo", "text-field": "Campo di testo", "the-ai-will-generate-a-json-template-based-on-your-description": "L'IA genererà un modello JSON in base alla tua descrizione", "the-effective-scope-will-be-global-as-its-broader-than-team": "L'ambito efficace sarà globale, in quanto è più ampio del team.", "the-form-youre-looking-for-doesnt-exist-or-has-been-deleted": "Il modulo che stai cercando non esiste o è stato eliminato.", "the-integration-already-worked-before-and-now-its-trying-to-update-its-data-again": "L'integrazione ha già funzionato prima e ora sta cercando di aggiornare nuovamente i suoi dati.", "the-leadership-team-with-global-scope": "Il team di \"leadership\" con portata globale", "the-same-user-in-the-marketing-team-can-manage-marketing-resources": "Lo stesso utente nel team \"marketing\" può gestire le risorse di marketing", "the-specific-resources-projects-data-etc-that-the-team-has-access-to": "Le risorse specifiche (pro<PERSON><PERSON>, dati, ecc.) A cui il team ha accesso", "the-system-tried-to-refresh-the-data-but-it-didnt-work-this-time-it-stayed-as-it-was-before": "Il sistema ha cercato di aggiornare i dati, ma questa volta non ha funzionato. \nRimase com'era prima.", "the-team-already-exists": "La squadra esiste già", "the-web-url-to-fetch-data-from": "L'URL Web per recuperare i dati da", "their-manager-role-with-team-scope": "Il loro ruolo di \"manager\" con l'ambito di squadra", "there-are-no-integrations-to-assign-at-this-time": "Non ci sono integrazioni da assegnare in questo momento", "there-was-an-error-enhancing-your-description-please-try-again": "C'è stato un errore che migliora la tua descrizione. \nPer favore riprova.", "there-was-an-error-generating-your-template-please-try-again": "C'è stato un errore che genera il tuo modello. \nPer favore riprova.", "there-was-an-error-loading-the-team-data": "Si è verificato un errore di caricamento dei dati del team.", "there-was-an-error-loading-your-template": "C'è stato un errore di caricamento del tuo modello.", "there-was-an-error-processing-the-file-please-try-again": "C'è stato un errore che elabora il file. \nPer favore riprova.", "there-was-an-error-saving-your-template": "Si è verificato un errore di salvare il modello.", "there-was-an-error-updating-the-team-information": "Si è verificato un errore di aggiornamento delle informazioni del team.", "this-action-cannot-be-undone": "Questa azione non può essere annullata.", "this-action-cannot-be-undone-0": "? \nQuesta azione non può essere annullata.", "this-is-a-paragraph-of-text-you-can-edit-this-text-to-add-your-own-content": "Questo è un paragrafo di testo. \nPuoi modificare questo testo per aggiungere i tuoi contenuti.", "this-is-the-unique": "Questo è l'unico", "this-means-your-integration-just-started-collecting-information-its-the-first-step-after-setting-it-up": "Ciò significa che la tua integrazione ha appena iniziato a raccogliere informazioni. \nÈ il primo passo dopo averlo configurato.", "this-team-doesnt-have-any-integrations-assigned-yet": "Questa squadra non ha ancora integrazioni assegnate", "this-team-doesnt-have-any-permissions-assigned-yet": "Questa squadra non ha ancora assegnate autorizzazioni.", "this-team-has-no-members-or-none-match-your-search": "Questa squadra non ha membri o nessuno corrisponde alla tua ricerca", "this-will-give-all-team-members-access-to-this-integration": "? \nCiò darà a tutti i membri del team accedere a questa integrazione.", "today": "<PERSON><PERSON><PERSON>", "toolbar": {"cancelButton": "Cancellare", "copyHtmlButton": "Copia HTML", "designMode": "Modalità di progettazione", "desktopView": "Vista desktop", "htmlCode": "Codice HTML", "htmlCopiedToastDescription": "Il codice HTML è stato copiato negli appunti.", "htmlCopiedToastTitle": "HTML copiato", "importButton": "Importare", "importJsonDialogTitle": "Importa modello JSON", "importJsonTemplateButton": "Importa modello JSON", "invalidJsonToastDescription": "Fornire una serie di componenti JSON valida.", "invalidJsonToastTitle": "JSON non valido", "loadButton": "Carico", "loadTemplateButton": "Modello di carico", "loadTemplateDialogTitle": "Modello di carico", "mobileView": "Vista mobile", "pasteJsonDescription": "Incolla l'array JSON generato da un modello AI in base al prompt.", "pasteJsonLabel": "Incolla il modello JSON", "pasteJsonPlaceholder": "[{\"id\": \"1\", \"type\": \"header\", \"content\": \"welcome\", \"impostazioni\": {...}}]", "previewMode": "Modalità di anteprima", "redoButton": "<PERSON><PERSON><PERSON>", "saveButton": "<PERSON><PERSON>", "saveTemplateButton": "Salva modello", "saveTemplateDialogTitle": "Salva modello", "selectTemplateLabel": "Seleziona il modello", "selectTemplatePlaceholder": "Seleziona un modello", "tabletView": "Vista del tablet", "templateImportedToastDescription": "Il modello JSON è stato importato con successo.", "templateImportedToastTitle": "Modello importato", "templateNameLabel": "Nome modello", "templateNamePlaceholder": "Il mio modello di email", "undoButton": "Disfare", "viewAllTemplatesButton": "Visualizza tutti i modelli"}, "total-integrations": "Integrazioni totali", "total-teams": "Squadre totali", "total-users": "Utenti totali", "track-progress": "Traccia i progressi", "transcript": "Trascrizione", "trash": "Spazzatura", "try-again": "<PERSON><PERSON><PERSON><PERSON>", "try-again-0": "<PERSON><PERSON><PERSON><PERSON>", "two-factor-authentication-is-required-but-this-ui-does-not-handle-that": "È richiesta l'autenticazione a due fattori, ma questa interfaccia utente non lo gestisce", "type": "Tipo", "understand-what-each-status-means-in-simple-words": "Comprendi cosa significa ogni stato in parole semplici.", "understanding-how-permission-scopes-limit-the-reach-of-permissions": "Comprendere come gli ampli di autorizzazione limitano la portata delle autorizzazioni", "understanding-how-permissions-work-in-our-enterprise-system": "Comprendere come funzionano le autorizzazioni nel nostro sistema aziendale", "understanding-the-permission-model-and-access-control": "Comprensione del modello di autorizzazione e del controllo di accesso.", "unit": "Unità", "unit-0": "Unità", "unknown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update-failed": "Aggiornamento non riuscito", "update-failed-0": "Aggiornamento non riuscito", "update-frequency": "Aggiornamento della frequenza", "update-frequency-days": "Aggiorna frequenza (giorni)", "update-frequency-days-0": "Aggiorna frequenza (giorni)", "update-team-information-and-permissions": "Aggiorna le informazioni e le autorizzazioni del team.", "update-time": "Tempo di aggiornamento:", "update-time-must-be-at-least-1-day": "Il tempo di aggiornamento deve essere almeno 1 giorno.", "update-time-must-be-at-least-1-day-0": "Il tempo di aggiornamento deve essere almeno 1 giorno.", "updating": "Aggiornamento", "upload-and-send": "Carica e invia", "upload-csv-file": "Carica il file CSV", "upload-failed": "Caricamento non riuscito", "upload-failed-0": "Caricamento non riuscito", "upload-logo": "Carica il logo", "upload-your-company-logo-to-display-on-the-form": "Carica il logo della tua azienda da visualizzare nel modulo", "url-copied-to-clipboard": "URL copiato negli appunti!", "use-permission-overrides-sparingly-and-only-when-necessary-they-should-be-the-exception-not-the-rule": "Usa il permesso prevale con parsimonia e solo quando necessario. \nDovrebbero essere l'eccezione, non la regola.", "use-team-scoped-permissions-when-possible": "Usa le autorizzazioni scavate dal team quando possibile", "use-template": "Usa il modello", "use-the-import-json-feature-in-our-email-builder-to-load-your-template": "Usa la funzione \"Importa JSON\" nel nostro costruttore di e -mail per caricare il tuo modello", "use-the-most-restrictive-role-that-meets-the-users-needs": "Usa il ruolo più restrittivo che soddisfa le esigenze dell'utente", "use-this": "<PERSON>a questo", "use-this-description": "Usa questa descrizione", "use-when-a-user-has-a-role-or-team-membership-that-grants-permissions-they-shouldnt-have-but-they-still-need-the-role-or-team-for-other-permissions": "Utilizzare quando un utente ha un ruolo o un abbonamento al team che concede le autorizzazioni che non dovrebbero avere, ma hanno ancora bisogno del ruolo o del team per altre autorizzazioni.", "user-can-create-data-providers-only-for-their-team-s": "L'utente può creare fornitori di dati solo per i loro team.", "user-can-export-data-only-from-specific-projects-they-have-access-to": "L'utente può esportare dati solo da progetti specifici a cui hanno accesso.", "user-can-only-manage-their-own-profile-information": "L'utente può gestire solo le proprie informazioni sul profilo.", "user-can-view-analytics-for-all-teams-and-projects-in-the-system": "L'utente può visualizzare l'analisi per tutti i team e i progetti nel sistema.", "user-deleted": "L'utente eliminato", "user-growth": "Crescita dell'utente", "user-not-found": "Utente non trovato.", "user-overrides": "L'utente prevale", "username": "Nome utente", "username-is-required": "È richiesto il nome utente.", "users": "<PERSON><PERSON><PERSON>", "users-0": "<PERSON><PERSON><PERSON>", "users-are-assigned-roles-that-grant-a-set-of-permissions-roles-provide-a-baseline-of-access-appropriate-for-different-job-functions": "Agli utenti vengono assegnati ruoli che concedono una serie di autorizzazioni. \nI ruoli forniscono una base di accesso appropriato per diverse funzioni lavorative.", "users-belong-to-teams-that-grant-additional-permissions-team-permissions-are-typically-scoped-to-the-teams-resources": "Gli utenti appartengono a team che concedono autorizzazioni aggiuntive. \nLe autorizzazioni del team sono in genere ammesse alle risorse del team.", "users-per-team": "utenti per squadra", "users-who-belong-to-the-team-and-inherit-its-permissions": "Utenti che appartengono al team e ereditano le sue autorizzazioni", "verify-team-memberships-and-permissions": "Verifica le iscrizioni e le autorizzazioni del team", "verifying-account-status": "Verifica dello stato dell'account ...", "view": "Visualizzazione", "view-analytics": "Visualizza l'analisi", "view-analytics-0": "Visualizza l'analisi", "view-analytics-with-global-scope": "Visualizza l'analisi con l'ambito globale", "view-details": "Visualizza i dettagli", "view-details-0": "Visualizza i dettagli", "view-filter-and-manage-your-data-integrations": "Visualizza, filtra e gestisci le integrazioni dei dati", "view-integrations": "Visualizza integrazioni", "view-interviews-templates": "Visualizza i modelli di interviste", "view-team-members": "Visualizza i membri del team", "view-your-forms-dashboard": "Visualizza la dashboard dei moduli", "viewButton": "Visualizzazione", "visit-website": "Visita il sito web", "we-couldnt-find-the-interview-youre-looking-for-it-may-have-been-deleted-or-doesnt-exist": "Non siamo riusciti a trovare l'intervista che stai cercando. \nPotrebbe essere stato eliminato o non esiste.", "we-couldnt-find-the-template-youre-looking-for-it-may-have-been-deleted-or-doesnt-exist": "Non siamo riusciti a trovare il modello che stai cercando. \nPotrebbe essere stato eliminato o non esiste.", "web": "Web", "web-integration": "Integrazione web", "web-integration-0": "Integrazione web", "webhook-url": "Webhook URL", "welcome-to": "Benvenuti a", "what-are-permission-scopes": "Cosa sono gli ampli di autorizzazione?", "what-are-roles": "Cosa sono i ruoli?", "when-a-user-has-the-same-permission-with-different-scopes-e-g-from-different-roles-or-teams-the-system-uses-the-broadest-scope": "Quando un utente ha la stessa autorizzazione con ambiti diversi (ad esempio, da ruoli o team diversi), il sistema utilizza l'ambito più ampio:", "when-determining-if-a-user-has-a-specific-permission-the-system": "Quando si determina se un utente ha un'autorizzazione specifica, il sistema:", "when-to-use-permission-overrides": "Quando utilizzare il permesso di autorizzazione", "while-roles-define-what-a-user-can-do-based-on-their-job-function-teams-define-what-resources-they-can-access-this-combination-provides-a-flexible-and-powerful-access-control-system": "Mentre i ruoli definiscono ciò che un utente può fare in base alla propria funzione lavorativa, i team definiscono le risorse a cui possono accedere. \nQuesta combinazione fornisce un sistema di controllo di accesso flessibile e potente:", "width": "<PERSON><PERSON><PERSON><PERSON>", "yes": "SÌ", "yesterday": "<PERSON><PERSON>", "you": "Voi", "you-can-generate-a-jira-api-token-from-your-atlassian-account-settings": "Puoi generare un token API JIRA dalle impostazioni dell'account Atlassian.", "you-can-now-paste-this-prompt-to-your-preferred-ai-model": "Ora puoi incollare questo prompt al tuo modello AI preferito.", "you-can-only-subscribe-to-the-free-plan-once-even-if-your-previous-free-plan-subscription-is-no-longer-active-you-cannot-subscribe-to-the-free-plan-again-to-continue-using-the-service-please-choose-a-paid-plan": "Puoi iscriverti al piano gratuito solo una volta. \nAnche se l'abbonamento al piano gratuito precedente non è più attivo, non è possibile iscriverti al piano gratuito. \nPer continuare a utilizzare il servizio, scegli un piano a pagamento.", "you-dont-have-permission-to-access-the-integrations-page": "Non hai il permesso di accedere alla pagina Integrazioni.", "you-dont-have-permission-to-access-the-interviews-page": "Non hai il permesso di accedere alla pagina delle interviste.", "you-dont-have-permission-to-access-the-users-page": "Non hai il permesso di accedere alla pagina degli utenti.", "you-dont-have-permission-to-assign-integrations-to-teams": "Non hai il permesso di assegnare integrazioni ai team.", "you-dont-have-permission-to-edit-teams": "Non hai il permesso di modificare i team.", "you-dont-have-permission-to-view-team-details": "Non hai il permesso di visualizzare i dettagli del team.", "you-dont-have-permission-to-view-team-integrations": "Non hai il permesso di visualizzare le integrazioni del team.", "you-havent-created-any-forms-yet-use-the-form-builder-to-get-started": "Non hai ancora creato alcun modulo. \nUsa il costruttore di moduli per iniziare.", "your-account-has-been-successfully-upgraded": "Il tuo account è stato aggiornato con successo.", "your-description-has-been-enhanced-with-additional-details": "La tua descrizione è stata migliorata con ulteriori dettagli.", "your-email-header": "La tua intestazione e -mail", "your-email-template-has-been-created-successfully": "Il tuo modello di posta elettronica è stato creato correttamente.", "your-forms-dashboard": "La tua dashboard dei moduli", "your-interview-template-has-been-updated-successfully": "Il tuo modello di intervista è stato aggiornato con successo.", "your-jira-instance-domain": "Il tuo dominio dell'istanza Jira", "es": "🇪🇸 spagnolo", "it": "🇮🇹 italiano", "ai-powered": "AI alimentato", "CompanyUsagePage": {"activeStatus": "Attivo", "aiInterviewerUsageTitle": "Utilizzo dell'intervistatore di AI", "emailBuilderUsageTitle": "Email Builder Utilizzo", "emailsUnit": "{Count} e -mail", "exceededStatus": "Superato", "failedToLoadUsageInfoToast": "Impossibile caricare le informazioni di utilizzo", "loadingPageTitle": "Informazioni sull'utilizzo dell'azienda", "minutesUnit": "{conta} minuti", "tableHeadRemaining": "<PERSON><PERSON><PERSON>", "tableHeadTotalLimit": "Limite totale", "tableHeadUsagePercentage": "Percentuale di utilizzo", "tableHeadUsed": "<PERSON><PERSON>"}, "NotFoundPage": {"dashboardButton": "Pannello di controllo", "footerText": "© {anno} Passisto. \nTutti i diritti riservati.", "goBackButton": "Torna indietro", "pageDescription": "Non siamo riusciti a trovare la pagina che stai cercando. \nPotrebbe essere stato rimosso, rinominato o non esiste.", "pageHeading": "Pagina non trovata", "pageTitle": "404"}, "chatPage": {"actions": {"copy": "Copia negli appunti", "helpful": "Utile", "notHelpful": "Non utile", "readAloud": "Leggi ad alta voce", "regenerate": "Risposta rigenerata"}, "avatars": {"ai": "AI", "user": "U"}, "input": {"placeholder": "Digita il tuo messaggio qui ...", "waitingPlaceholder": "In attesa di risposta ..."}, "recording": {"start": "Inizia a registrare", "started": "Iniziato a registrare", "stop": "Smetti di registrazione", "stopped": "Ha smesso di registrare"}, "session": {"label": "Sessione"}, "sources": {"label": "<PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON><PERSON>"}, "ThemeToggle": {"darkOption": "<PERSON><PERSON><PERSON>", "lightOption": "<PERSON><PERSON><PERSON>", "systemOption": "Sistema", "toggleThemeSrOnly": "Attiva tema"}, "workflows-automation": "Automazione dei flussi di lavoro", "OnboardingPage": {"access-denied-please-contact-support": "Accesso negato. \nSi prega di contattare il supporto.", "acme-inc": "Acme Inc", "areas-of-interest": "<PERSON><PERSON> di interesse", "authentication-failed-please-sign-in-again": "Autenticazione non riuscita. \nSi prega di accedere di nuovo.", "authentication-failed-please-try-again": "Autenticazione non riuscita. \nPer favore riprova.", "bio": "Bio", "company": "Azienda", "complete": "Completare", "complete-your-profile": "Completa il tuo profilo", "doe": "<PERSON><PERSON>", "e-g-web-development-ai-ml": "ad esempio, sviluppo web, AI/ML, Data Science ...", "error": "Errore", "experience-level": "Livello di esperienza", "first-name": "Nome di battesimo", "invalid-form-data-please-check-your-input": "Dati del modulo non validi. \nSi prega di controllare il tuo input.", "john": "<PERSON>", "junior-0-2-years": "Junior (0-2 anni)", "last-name": "Cognome", "lead-architect": "Piombo/architetto", "linkedin-profile-optional": "Profilo <PERSON>edIn (opzionale)", "mid-level-3-5-years": "<PERSON><PERSON> (3-5 anni)", "network-error-please-check-connection": "Errore di rete. \nSi prega di controllare la tua connessione Internet.", "next": "Prossimo", "of": "Di", "personal-info": "Informazioni personali", "please-fill-required-fields": "Si prega di compilare tutti i campi richiesti", "please-fill-required-fields-company-role-experience": "Si prega di compilare i campi richiesti: azienda, ruolo e livello di esperienza", "please-fill-required-fields-first-last-name": "Si prega di compilare i campi richiesti: nome e cognome", "please-sign-in-to-continue": "Si prega di accedere per continuare", "preferences": "Prefer<PERSON><PERSON>", "previous": "Precedente", "professional": "Professionale", "role": "<PERSON><PERSON><PERSON>", "select-experience-level": "Seleziona il livello di esperienza", "senior-5-years": "Senior (5 anni)", "server-error-please-try-again-later": "Errore del server. \nPer favore riprova più tardi.", "software-engineer": "Ingegnere del software", "something-went-wrong-please-try-again": "Qualcosa è andato storto. \nPer favore riprova.", "step": "Fare un passo", "tell-us-about-yourself": "Raccontaci di te ...", "unexpected-error-occurred": "Si è verificato un errore imprevisto. \nPer favore riprova."}, "UsageLimitDialog": {"candidateMessage1": "Questo link al colloquio non è più attivo.", "candidateMessage2": "Si prega di contattare la società che ti ha inviato questo invito al colloquio per assistenza.", "candidateTitle": "Link non disponibile", "chat messages": "Messaggi di chat", "closeButton": "<PERSON><PERSON>", "genericUnit": "", "interview": "colloquio", "messages": "<PERSON><PERSON><PERSON>", "minutes": "minuti", "processingButton": "Elaborazione ...", "toastFailedAccess": "Impossibile accedere al portale di fatturazione", "upgradeButton": "Piano di aggiornamento", "userMessage": "Hai raggiunto il tuo limite {type} ({limit} {unit}). \nPer continuare a utilizzare questa funzione, aggiorna il tuo piano.", "userTitle": "Limite di utilizzo superato"}, "common": {"error": "Errore"}, "usageLimit": {"exceeded": "Limite di utilizzo superato per questa funzione.", "warning": "Avviso limite di utilizzo: hai {remaining} rimanenti per questa funzione nel tuo piano attuale.", "checkFailed": "Impossibile verificare il limite di utilizzo"}, "usage": {"errorCheckingLimit": "Impossibile controllare il limite di utilizzo", "errorTitle": "Errore", "limitExceeded": "Limite di utilizzo superato per questa funzione.", "warning": "Avviso limite di utilizzo: hai {rimanente} rimanente per questa funzione nel tuo piano attuale."}}